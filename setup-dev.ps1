#!/usr/bin/env pwsh
#requires -PSEdition Core

if (!(Get-Command volta -ErrorAction SilentlyContinue)) {
    Write-Error "Volta is not installed. Please install Volta first: https://docs.volta.sh/guide/getting-started"
    exit 1
}

if (!(Get-Command fvm -ErrorAction SilentlyContinue)) {
    dart pub global activate fvm
}

npm install -g firebase-tools
dart pub global activate melos
dart pub global activate cider
dart pub global activate flutterfire_cli

Write-Host "Installation complete. Please restart your terminal."
