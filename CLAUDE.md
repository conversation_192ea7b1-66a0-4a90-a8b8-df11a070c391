# MedPulse Development Guide

## General project information
- There are two web apps: admin for managing questions and tests, medpulse web app for users to take tests
- Entities shared between both apps reside in packages/entities
- Entities that are specific to one app reside the apps entities folder
- Same for shared providers

## Build and Development Commands
- Run buildrunner: `./br.ps1` (starts watch mode for all packages)
- Run admin app: `cd apps/admin && flutter run -d chrome`
- Run mobile app: `cd apps/medpulse && flutter run -d chrome --flavor dev`

## Code Style Guidelines
- **Imports**: dart: → package: → relative imports
- **Naming**: PascalCase for classes/types, camelCase for variables/methods
- **Architecture**: Feature-first organization with Riverpod for state management
- **Error Handling**: Use centralized ErrorHandler utility, AsyncValue pattern
- **Providers**: Suffix with "Provider", organize by feature
- **File Structure**: models/ → providers/ → screens/ → widgets/
- **Linting**: Follow flutter_lints rules, treat build_context warnings as errors

## Best Practices
- Use immutable state with Freezed
- Separate UI and business logic
- Properly handle loading/error states
- Follow repository pattern for data access
- Use providers for dependency injection
- Document public APIs and complex logic
- Use annotation syntax for RiverPod
- Avoid deprecated libraries and classes and outdated approaches
- No automated tests for the time being
