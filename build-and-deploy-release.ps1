# build.ps1

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("medpulse", "admin")]
    [string]$project
)

# Store the original directory
$originalDir = Get-Location

# Function to ensure we return to the original directory
function Reset-Location {
    Set-Location $originalDir
}

# Function to generate the source map ID
function Get-SourceMapId {
    $username = (git config user.name).Replace(" ", "").ToLower()
    $timestamp = Get-Date -Format "yy-MM-dd_HHmmss"
    return "${project}_${username}_${timestamp}"
}

# Function to check if the working directory is clean
function Test-GitStatus {
    $status = git status --porcelain
    if ($status) {
        Write-Host "Warning: You have uncommitted changes. Continue? (y/n)" -ForegroundColor Yellow
        $response = Read-Host
        if ($response -ne "y") {
            Reset-Location
            exit
        }
    }
}

# Main build process
try {
    # Change to project directory
    $projectPath = Join-Path "apps" $project
    if (-not (Test-Path $projectPath)) {
        Write-Host "Error: Project directory '$projectPath' not found" -ForegroundColor Red
        exit 1
    }
    Set-Location $projectPath

    # Check git status
    Test-GitStatus

    # Generate source map ID
    $sourceMapId = Get-SourceMapId
    Write-Host "Building with source map ID: $sourceMapId" -ForegroundColor Green

    # Run Flutter build with source maps enabled
    $buildCommand = "fvm flutter build web --release --source-maps --dart-define=SOURCE_MAP_ID=$sourceMapId"
    Write-Host "Running: $buildCommand" -ForegroundColor Cyan
    Invoke-Expression $buildCommand

    if ($LASTEXITCODE -eq 0) {
        # Copy source map to a versioned file
        $sourceFile = Join-Path "build" "web" "main.dart.js.map"
        $targetFile = Join-Path "build" "web" "main.dart.js.map.$sourceMapId"
        Copy-Item $sourceFile $targetFile

        Write-Host "`nBuild completed successfully!" -ForegroundColor Green
        Write-Host "Source map ID: $sourceMapId" -ForegroundColor Green
        Write-Host "Source map saved as: build/web/main.dart.js.map.$sourceMapId" -ForegroundColor Green
    }
    else {
        throw "Flutter build failed with exit code $LASTEXITCODE"
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    Reset-Location
    exit 1
}
finally {
    # Always return to original directory
    Reset-Location
}