<!DOCTYPE html>
<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta content="MedPulse" name="MedPulse E-Learning Platform for Medical Students">

    <!-- Google Sign-In configuration -->
    <meta content="99761044972-k3h1g28j9efb0br9qi775sk27qporupo.apps.googleusercontent.com"
          name="google-signin-client_id">

    <!-- Apple Sign-In script -->
    <script src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"
            type="text/javascript"></script>

    <!-- iOS meta tags & icons -->
    <meta content="yes" name="mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="MedPulse" name="apple-mobile-web-app-title">
    <link rel="apple-touch-icon" sizes="180x180" href="icons/apple-touch-icon.png" />


    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png"  sizes="48x48"/>
    <link rel="icon" type="image/png" href="favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="favicon.svg" />
    <link rel="shortcut icon" href="favicon.ico" />

    <!-- Font preloading -->
    <link href="https://fonts.googleapis.com" rel="preconnect">
    <link crossorigin href="https://fonts.gstatic.com" rel="preconnect">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
          rel="stylesheet">

    <title>MedPulse</title>
    <link href="manifest.json" rel="manifest">

    <style>
        :root {
          --primary: #4353E0;
          --primary-dark: #3544CB;
          --primary-light: #8992EB;
          --secondary: #6C63FF;
          --text-primary: #333333;
          --text-secondary: #6B7280;
          --background: #F7F7F7;
          --white: #FFFFFF;
          --shadow: 0 10px 25px rgba(67, 83, 224, 0.07);
          --transition: all 0.3s ease;
        }

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        html, body {
          height: 100%;
          width: 100%;
          overflow: hidden;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          background: linear-gradient(135deg, #F7F7F7 0%, #FFFFFF 100%);
          color: var(--text-primary);
        }

        #flutter-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          width: 100%;
          position: fixed;
          top: 0;
          left: 0;
          z-index: 9999;
          background: linear-gradient(135deg, #F7F7F7 0%, #FFFFFF 100%);
          transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
        }

        .logo-container {
          position: relative;
          margin-bottom: 40px;
        }

        .logo {
          width: 140px;
          height: 140px;
          object-fit: contain;
          filter: drop-shadow(0 8px 16px rgba(67, 83, 224, 0.2));
          animation: float 3s ease-in-out infinite;
        }

        .logo-glow {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 180px;
          height: 180px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(67, 83, 224, 0.2) 0%, rgba(67, 83, 224, 0) 70%);
          z-index: -1;
          animation: pulse 2s ease-in-out infinite;
        }

        .loading-spinner {
          position: relative;
          width: 60px;
          height: 60px;
          margin-bottom: 30px;
        }

        .spinner-ring {
          position: absolute;
          width: 60px;
          height: 60px;
          border: 3px solid rgba(67, 83, 224, 0.1);
          border-radius: 50%;
        }

        .spinner-fill {
          position: absolute;
          width: 60px;
          height: 60px;
          border: 3px solid transparent;
          border-top: 3px solid var(--primary);
          border-radius: 50%;
          animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
        }

        .loading-text {
          font-size: 18px;
          font-weight: 500;
          color: var(--text-secondary);
          margin-bottom: 8px;
          letter-spacing: 0.5px;
        }

        .loading-subtext {
          font-size: 14px;
          color: var(--text-secondary);
          opacity: 0.8;
          text-align: center;
          max-width: 400px;
          line-height: 1.5;
        }

        .loading-progress {
          width: 200px;
          height: 4px;
          background-color: rgba(67, 83, 224, 0.1);
          border-radius: 4px;
          margin: 30px 0;
          overflow: hidden;
          position: relative;
        }

        .progress-bar {
          position: absolute;
          height: 100%;
          width: 0%;
          background-color: var(--primary);
          border-radius: 4px;
          transition: width 0.4s ease;
          animation: progress 3s ease-in-out forwards;
        }

        .particles {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          z-index: -1;
        }

        .particle {
          position: absolute;
          display: block;
          background-color: var(--primary-light);
          opacity: 0.3;
          border-radius: 50%;
          animation: particleFloat 15s infinite linear;
        }

        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }

        @keyframes pulse {
          0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
          50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes progress {
          0% { width: 0%; }
          50% { width: 60%; }
          80% { width: 85%; }
          100% { width: 100%; }
        }

        @keyframes particleFloat {
          0% { transform: translateY(0) rotate(0deg); }
          100% { transform: translateY(-100vh) rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .logo { width: 120px; height: 120px; }
          .logo-glow { width: 150px; height: 150px; }
          .loading-spinner { width: 50px; height: 50px; }
          .spinner-ring, .spinner-fill { width: 50px; height: 50px; }
          .loading-text { font-size: 16px; }
          .loading-progress { width: 180px; }
        }
    </style>
</head>
<body>
<!-- Professional loading experience -->
<div id="flutter-loading">
    <!-- Animated background particles -->
    <div class="particles" id="particles"></div>

    <!-- Logo with glow effect -->
    <div class="logo-container">
        <div class="logo-glow"></div>
        <img alt="MedPulse" class="logo" onerror="this.style.display='none'"
             src="app_logo.png">
    </div>

    <!-- Custom spinner -->
    <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-fill"></div>
    </div>

    <!-- Loading message -->
    <p class="loading-text">Initializing MedPulse</p>
    <p class="loading-subtext">Preparing your personal medical training environment</p>

    <!-- Progress indicator -->
    <div class="loading-progress">
        <div class="progress-bar" id="progress-bar"></div>
    </div>
</div>

<script async src="flutter_bootstrap.js"></script>

<!-- Capture PWA install prompt event -->
<script>
    let deferredPrompt;

    window.addEventListener('beforeinstallprompt', (e) => {
        deferredPrompt = e;
    });

    function promptInstall(){
        deferredPrompt.prompt();
    }

    // Listen for app install event
    window.addEventListener('appinstalled', () => {
        deferredPrompt = null;
        appInstalled();
    });

    // Track how PWA was launched (either from browser or as PWA)
    function getLaunchMode() {
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        if(deferredPrompt) hasPrompt();
        if (document.referrer.startsWith('android-app://')) {
            appLaunchedAsTWA();
        } else if (navigator.standalone || isStandalone) {
            appLaunchedAsPWA();
        } else {
            window.appLaunchedInBrowser();
        }
    }

    // Create background particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      const particleCount = 20;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('span');
        particle.classList.add('particle');

        // Random sizing and positioning
        const size = Math.random() * 20 + 5;
        const left = Math.random() * 100;
        const delay = Math.random() * 0;
        const duration = Math.random() * 10 + 10;

        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${left}%`;
        particle.style.top = `${Math.random() * 100}%`;
        particle.style.animationDuration = `${duration}s`;
        particle.style.animationDelay = `${delay}s`;

        particlesContainer.appendChild(particle);
      }
    }

    // Initialize particles on load
    document.addEventListener('DOMContentLoaded', createParticles);

    // Handle Flutter initialization and loading screen
    window.addEventListener('flutter-first-frame', function() {
      document.getElementById('progress-bar').style.width = '100%';

      setTimeout(() => {
        const loadingScreen = document.getElementById('flutter-loading');
        loadingScreen.style.opacity = '0';
        loadingScreen.style.visibility = 'hidden';
      }, 500);
    });

    // Simulate progress for better UX if Flutter takes time to load
    let progressValue = 0;
    const progressSimulation = setInterval(() => {
      if (progressValue >= 90) {
        clearInterval(progressSimulation);
      } else {
        progressValue += Math.random() * 5;
        if (progressValue > 90) progressValue = 90;
        document.getElementById('progress-bar').style.width = `${progressValue}%`;
      }
    }, 300);
</script>
</body>
</html>