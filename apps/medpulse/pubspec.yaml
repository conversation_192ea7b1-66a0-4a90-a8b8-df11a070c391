name: medpulse
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Monorepo!
# resolution: workspace

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  providers:
    path: "../../packages/providers"
  entities:
    path: "../../packages/entities"
  storage_service:
    path: "../../packages/storage_service"
  flutter_riverpod: ^2.6.1
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  riverpod_annotation: ^2.6.1
  firebase_analytics_web: ^0.5.10+9
  app_links: ^6.3.3
  path: ^1.9.0
  app_tracking_transparency: ^2.0.6+1
  cloud_functions: ^5.3.1
  flutter_facebook_auth: ^6.0.4
  sign_in_with_apple: ^6.1.4
  google_sign_in: ^6.3.0
  google_sign_in_web: ^0.12.4+4
  firebase_auth: ^5.4.2
  firebase_storage: ^12.4.2
  path_provider: ^2.1.5
  idb_shim: ^2.6.5+1
  package_info_plus: ^8.2.1
  cloud_firestore: ^5.6.4
  crypto: ^3.0.6
  encrypt: ^5.0.3
  http: ^1.1.0
  url_launcher: ^6.1.14
  intl: ^0.18.1
  webview_flutter: ^4.9.0
  web: ^1.1.1
  pwa_install: ^0.0.5
  flutter_markdown: ^0.6.20
  fl_chart: ^0.69.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.14
  freezed: ^2.5.8
  json_serializable: ^6.9.3
  riverpod_generator: ^2.6.4
  riverpod_lint: ^2.6.4

dependency_overrides:
  archive: ^4.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family: Cabin
      fonts:
        - asset: assets/fonts/Cabin-Regular.ttf

  # To add assets to your application, add an assets section, like this:
  assets:
#    - assets/fonts/Cabin-VariableFont_wdth,wght.ttf
#    - assets/fonts/Cabin-Italic-VariableFont_wdth,wght.ttf
    - assets/images/onboarding_1.png
    - assets/images/onboarding_2.png
    - assets/legal/privacy-policy.md
    - assets/legal/terms-conditions.md

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
