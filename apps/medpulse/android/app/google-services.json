{"project_info": {"project_number": "99761044972", "project_id": "medpulse-prod", "storage_bucket": "medpulse-prod.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:99761044972:android:6eba52616572ba845454c9", "android_client_info": {"package_name": "com.medpulse.app"}}, "oauth_client": [{"client_id": "99761044972-76ks61b40rqo551bsbji91ntblkqe4oe.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.medpulse.app", "certificate_hash": "7aa9513d1e01b9772e91393233d618fb6bd50271"}}, {"client_id": "99761044972-na1oqmvh0i2bnn323r9ebcineqpl6719.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.medpulse.app", "certificate_hash": "044ceb7618e8ef7f75b45225284d08965b60c4ef"}}, {"client_id": "99761044972-k3h1g28j9efb0br9qi775sk27qporupo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyATl5RGKmjnQ2chUIoQJsMc2QNxJxRIC_w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "99761044972-k3h1g28j9efb0br9qi775sk27qporupo.apps.googleusercontent.com", "client_type": 3}, {"client_id": "99761044972-8rbjgiihb7i3cc5su6vecpeegjbbtda9.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.medpulse.client.medpulse"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:99761044972:android:3d3cacd67d1d4d2a5454c9", "android_client_info": {"package_name": "com.medpulse.client.medpulse"}}, "oauth_client": [{"client_id": "99761044972-k3h1g28j9efb0br9qi775sk27qporupo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyATl5RGKmjnQ2chUIoQJsMc2QNxJxRIC_w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "99761044972-k3h1g28j9efb0br9qi775sk27qporupo.apps.googleusercontent.com", "client_type": 3}, {"client_id": "99761044972-8rbjgiihb7i3cc5su6vecpeegjbbtda9.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.medpulse.client.medpulse"}}]}}}], "configuration_version": "1"}