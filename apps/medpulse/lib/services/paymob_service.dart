import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:entities/user_entity.dart';
import 'package:providers/common.dart';
import 'package:uuid/uuid.dart';

class PaymobService {
  static const String _apiKey =
      'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TVRZMk1ETXhMQ0p1WVcxbElqb2lhVzVwZEdsaGJDSjkuVDNVdDF4R1VOWEd1dUNTR2dLUjR2X29Kb2VTNVZ0d0JpaURiNkxmbC01aThuODJjXzdvVllqNHIyQzhFSHp0Y2s4djZlNjhmQVJUUjIwblQ3emJEaFE=';
  static const String _integrationId = '190286';
  static const String _iFrameId = '199574';
  static const String _hmacKey = 'FD3DFF512A9F7725995F0FFFF52A81A2';
  static const String _baseUrl = 'https://pakistan.paymob.com/api';
  static const _uuid = Uuid();

  // Subscription plans
  static const Map<String, Map<String, dynamic>> _subscriptionPlans = {
    '3_months': {'amount': 700, 'duration_months': 3},
    '6_months': {'amount': 1000, 'duration_months': 6},
    '12_months': {'amount': 1500, 'duration_months': 12},
    'lifetime': {
      'amount': 5000,
      'duration_months': 1200, // 100 years
    },
  };

  Future<Map<String, dynamic>> initiatePayment({
    required UserEntity user,
    required String subscriptionType,
    required String callbackUrl,
    required String subscriptionId,
  }) async {
    dbgPrint('Initiating Paymob payment flow for subscription type: $subscriptionType');
    try {
      final transactionId = _uuid.v4();
      dbgPrint('Generated transaction ID: $transactionId');

      // Step 1: Get authentication token
      dbgPrint('Step 1: Getting authentication token');
      final authToken = await _getAuthToken();
      dbgPrint('Auth token obtained successfully');

      // Step 2: Register order
      dbgPrint('Step 2: Registering order');
      final orderId = await _registerOrder(
        authToken: authToken,
        amount: 5000, //_subscriptionPlans[subscriptionType]!['amount'],
        user: user,
        subscriptionId: subscriptionId,
        transactionId: transactionId,
      );
      dbgPrint('Order registered with ID: $orderId');

      // Step 3: Get payment key
      dbgPrint('Step 3: Getting payment key');
      final paymentKey = await _getPaymentKey(
        authToken: authToken,
        orderId: orderId,
        amount: _subscriptionPlans[subscriptionType]!['amount'],
        user: user,
        callbackUrl: callbackUrl,
        subscriptionId: subscriptionId,
        transactionId: transactionId,
      );
      dbgPrint('Payment key obtained successfully');

      // Step 4: Return payment URL and transaction ID
      final paymentUrl =
          'https://pakistan.paymob.com/api/acceptance/iframes/$_iFrameId?payment_token=$paymentKey';
          // 'https://pakistan.paymob.com/api/acceptance/iframes//$_iFrameId?payment_token=$paymentKey';
      dbgPrint('Step 4: Generated payment URL: $paymentUrl');

      return {'paymentUrl': paymentUrl, 'transactionId': transactionId};
    } catch (e) {
      dbgPrint('Error initiating payment: $e');
      throw Exception('Failed to initiate payment: $e');
    }
  }

  Future<String> _getAuthToken() async {
    dbgPrint('Requesting auth token from $_baseUrl/auth/tokens');
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/tokens'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'api_key': _apiKey}),
      );

      dbgPrint('Auth token response status: ${response.statusCode}');
      dbgPrint('Auth token response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData.containsKey('token')) {
          dbgPrint('Auth token obtained successfully');
          return responseData['token'];
        } else {
          dbgPrint('Auth token not found in response');
          throw Exception('Auth token not found in response: ${response.body}');
        }
      } else {
        dbgPrint('Failed to get auth token: ${response.statusCode}');
        throw Exception('Failed to get auth token: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      dbgPrint('Exception during auth token request: $e');
      rethrow;
    }
  }

  Future<String> _registerOrder({
    required String authToken,
    required int amount,
    required UserEntity user,
    required String subscriptionId,
    required String transactionId,
  }) async {
    dbgPrint('Registering order with Paymob');
    try {
      final requestBody = {
        'auth_token': authToken,
        'delivery_needed': false,
        'amount_cents': amount * 100, // Convert to cents
        'currency': 'PKR',
        'items': [
          {
            'name': 'Subscription for $subscriptionId',
            'amount_cents': amount * 100,
            'description': 'Premium access to all questions',
            'quantity': 1,
          },
        ],
        'shipping_data': {
          'email': user.email ?? '<EMAIL>', // Ensure email is not null
          'first_name': user.displayName ?? 'na',
          'last_name': 'na', // Fill with 'na' as required
          'phone_number': 'na', // Fill with 'na' as required
          'city': 'na', // Fill with 'na' as required
          'street': 'na', // Additional required field
          'building': 'na', // Additional required field
          'floor': 'na', // Additional required field
          'apartment': 'na', // Additional required field
          'postal_code': 'na', // Additional required field
          'state': 'na', // Additional required field
          'country': 'PK',
        },
        'merchant_order_id': transactionId,
      };

      dbgPrint('Order request body: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('$_baseUrl/ecommerce/orders'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      dbgPrint('Order registration response status: ${response.statusCode}');
      dbgPrint('Order registration response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData.containsKey('id')) {
          dbgPrint('Order registered successfully with ID: ${responseData['id']}');
          return responseData['id'].toString();
        } else {
          dbgPrint('Order ID not found in response');
          throw Exception('Order ID not found in response: ${response.body}');
        }
      } else {
        dbgPrint('Failed to register order: ${response.statusCode}');
        throw Exception('Failed to register order: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      dbgPrint('Exception during order registration: $e');
      rethrow;
    }
  }

  Future<String> _getPaymentKey({
    required String authToken,
    required String orderId,
    required int amount,
    required UserEntity user,
    required String callbackUrl,
    required String subscriptionId,
    required String transactionId,
  }) async {
    dbgPrint('Getting payment key from Paymob');
    try {
      final requestBody = {
        'auth_token': authToken,
        'amount_cents': amount * 100,
        'expiration': 3600,
        'order_id': orderId,
        'billing_data': {
          'email': user.email ?? '<EMAIL>', // Ensure email is not null
          'first_name': user.displayName ?? 'na',
          'last_name': 'na', // Fill with 'na' as required
          'phone_number': 'na', // Fill with 'na' as required
          'city': 'na', // Fill with 'na' as required
          'country': 'PK',
          'apartment': 'na',
          'floor': 'na',
          'street': 'na',
          'building': 'na',
          'postal_code': 'na',
          'state': 'na',
        },
        'currency': 'PKR',
        'integration_id': _integrationId,
        'lock_order_when_paid': false,
        'callback_url': callbackUrl,
        'metadata': {
          'subscription_id': subscriptionId,
          'subscription_type': 'premium',
          'transaction_id': transactionId,
          'hmac_key': _hmacKey,
        },
      };

      dbgPrint('Payment key request body: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('$_baseUrl/acceptance/payment_keys'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      dbgPrint('Payment key response status: ${response.statusCode}');
      dbgPrint('Payment key response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        if (responseData.containsKey('token')) {
          dbgPrint('Payment key obtained successfully');
          return responseData['token'];
        } else {
          dbgPrint('Payment key not found in response');
          throw Exception('Payment key not found in response: ${response.body}');
        }
      } else {
        dbgPrint('Failed to get payment key: ${response.statusCode}');
        throw Exception('Failed to get payment key: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      dbgPrint('Exception during payment key request: $e');
      rethrow;
    }
  }
}
