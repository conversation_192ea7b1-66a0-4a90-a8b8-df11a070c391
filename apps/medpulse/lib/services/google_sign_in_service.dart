import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:google_sign_in_platform_interface/google_sign_in_platform_interface.dart';
import 'package:google_sign_in_web/google_sign_in_web.dart';

/// Service class to manage Google Sign-In functionality in a structured and reusable manner.
/// Todo: Web authentication and authorization are now supposed to be separated. However, stuff does not appear to work (yet)
/// Todo: So wait until the APIs mature and implement it then.
/// -----------------------------------------------------------------------------------------------------------------------------------------
/// taken from https://levelup.gitconnected.com/comprehensive-guide-to-integrating-google-sign-in-in-flutter-web-android-and-ios-3dcbf02df8b0
/// -----------------------------------------------------------------------------------------------------------------------------------------
class GoogleSignInService {
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final GoogleSignInPlugin _googleSignInPlugin =
  GoogleSignInPlatform.instance as GoogleSignInPlugin;

  /// Initializes the Google Sign-In plugin with the required parameters.
  ///
  /// This method **must** be called before using any other methods in this class.
  init() {
    _googleSignInPlugin.initWithParams(SignInInitParameters());
  }

  /// Signs in a user using Google authentication.
  ///
  /// This is method does not return idToken on web,So it is considered as mobile only.
  ///
  /// Performs an interactive authentication process.
  /// For more details, refer to the official documentation:
  /// [GoogleSignIn.signIn](https://pub.dev/documentation/google_sign_in/latest/google_sign_in/GoogleSignIn/signIn.html)
  ///
  /// Returns the [GoogleSignInAccount] if successful, or `null` if the user cancels or an error occurs.
  Future<GoogleSignInAccount?> signIn() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return null;
      }

      return googleUser;
    } catch (e) {
      debugPrint('Error during Google Sign-In: $e');
      return null;
    }
  }

  /// Signs in a user silently using previously granted permissions.
  ///
  /// Attempts to authenticate the user without prompting for credentials.
  ///
  /// This  is considered as web only.
  ///
  /// For more details, refer to the official documentation:
  /// [GoogleSignIn.signInSilently](https://pub.dev/documentation/google_sign_in/latest/google_sign_in/GoogleSignIn/signInSilently.html)
  ///
  /// Returns the [GoogleSignInAccount] if successful, or `null` if the user is not signed in or an error occurs.
  Future<GoogleSignInAccount?> signInSilently() async {
    try {
      final GoogleSignInAccount? googleUser =
      await _googleSignIn.signInSilently();

      if (googleUser == null) {
        return null;
      }

      return googleUser;
    } catch (e) {
      debugPrint('Error during Google Sign-In: $e');
      return null;
    }
  }

  /// Renders the Google Sign-In button using the Google Sign-In web plugin.
  ///
  /// The button is customizable using [GSIButtonConfiguration], such as size and width.
  /// When the button is pressed, the [onPress] callback is triggered, providing the user's ID token.
  ///
  /// This method is considered as web only.
  ///
  /// For more details, refer to the official documentation:
  /// [GoogleSignInPlugin.renderButton](https://pub.dev/documentation/google_sign_in_web/latest/google_sign_in_web/GoogleSignInPlugin/rende
  Widget renderButton(Function(String idToken) onPress) {
    _googleSignInPlugin.userDataEvents?.listen((event) {
      if (event != null) {
        onPress(event.idToken!);
      }
    });

    return _googleSignInPlugin.renderButton(
        configuration: GSIButtonConfiguration(
            size: GSIButtonSize.large, minimumWidth: double.maxFinite));
  }

  /// Provides a stream of the currently signed-in user.
  ///
  /// This stream emits events whenever the user signs in or out.
  Stream<GoogleSignInAccount?> onCurrentUserChanged() {
    return _googleSignIn.onCurrentUserChanged;
  }

  /// Signs out the currently signed-in user from Google.
  /// Disconnect user from the app.
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
      await _googleSignIn.disconnect();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<bool> isSignedIn() async {
    return _googleSignIn.isSignedIn();
  }
}