import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:firebase_analytics_web/firebase_analytics_web.dart';
import 'package:providers/common.dart';

class AnalyticsService {
  final  _analytics = FirebaseAnalyticsWeb();
  static final AnalyticsService _instance = AnalyticsService._internal();
  var iOSTrackingStatus = TrackingStatus.notDetermined;

  factory AnalyticsService() {
    return _instance;
  }

  AnalyticsService._internal();

  void setTrackingStatus(TrackingStatus status) {
    iOSTrackingStatus = status;
  }

  void logEvent(
    String eventName,
    Map<String, Object>? parameters,
  ) {
/*
    if (Platform.isIOS && iOSTrackingStatus != TrackingStatus.authorized) {
      dbgPrint(
          'AnalyticsService:logEvent: iOS: Analytics Logging is disabled because of TrackingTransparency being not authorized');
    }
*/
    try {
      _analytics.logEvent(
        name: eventName,
        parameters: parameters,
      );
      dbgPrint("AnalyticsService: Event logged: $eventName");
    } catch (e) {
      dbgPrint("AnalyticsService: Error logging event $eventName: $e");
    }
  }

  void logVisitedScreen(String screenName) {
    try {
      _analytics.logEvent(
        name: screenName,
      );
      dbgPrint("AnalyticsService: Event logged: $screenName");
    } catch (e) {
      dbgPrint("AnalyticsService: Error logging event $screenName: $e");
    }
  }
}
