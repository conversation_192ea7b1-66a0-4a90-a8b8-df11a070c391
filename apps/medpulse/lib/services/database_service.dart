/*
import 'package:path/path.dart';
import 'package:providers/common.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseService {
  static const _databaseName = "database.db";
  static const _databaseVersion = 1;
  static const f = "DatabaseService";

  DatabaseService._privateConstructor();

  static final DatabaseService instance = DatabaseService._privateConstructor();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize the database
  Future<Database> _initDatabase() async {
    // Get the default database location
    try {

    databaseFactory = databaseFactoryFfiWeb;

    dbgPrint('$f: initDatabase');
    String path = join(await getDatabasesPath(), _databaseName);
    dbgPrint('$f: initDatabase 2');

    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
    } catch (e) {
      dbgPrint(e.toString());
      rethrow;
    }
  }

  static Future<String?> getPref(String id) async {
    final db = await DatabaseService.instance.database;
    var result = await db.query('prefs',
        columns: ['value'], where: 'id = ?', whereArgs: [id]);
    if (result.isNotEmpty) {
      return result.first['value'] as String?;
    }
    return null;
  }

  static Future<void> setPref(String id, String? value) async {
    final db = await DatabaseService.instance.database;
    await db.insert(
      'prefs',
      {'id': id, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }


  // Create tables and initial data
  Future<void> _onCreate(Database db, int version) async {
    // language=sqlite
    await db.execute('''    
      CREATE TABLE IF NOT EXISTS docs (
        path TEXT PRIMARY KEY ,
        data TEXT,
        timestamp INTEGER
      )
    ''');

    // language=sqlite
    await db.execute('''
      CREATE TABLE IF NOT EXISTS images (
        path TEXT PRIMARY KEY ,
        timestamp INTEGER
      )
''');

    // language=sqlite
    await db.execute('''
      CREATE TABLE IF NOT EXISTS prefs (
        id TEXT PRIMARY KEY,
        value TEXT
      )
    ''');

  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle different version upgrades
    // if (oldVersion < 2) {
    //   await db.execute('ALTER TABLE users ADD COLUMN phone_number TEXT');
    // }
  }

  // Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
*/
