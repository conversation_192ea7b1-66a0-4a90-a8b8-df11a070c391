import 'dart:html' as html;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

import 'package:providers/providers.dart';

Future<void> reportError(
    dynamic error,
    StackTrace stackTrace, {
      String? errorType = 'Handled Error',
    }) async {
  try {

    dbgPrint('reportError: Caught: $error\n$stackTrace');

    if (kDebugMode) return;
    // Generate error hash for deduplication
    final errorHash = sha256
        .convert(utf8.encode('$error-$stackTrace'))
        .toString();

    final sourceMapId = const String.fromEnvironment('SOURCE_MAP_ID', defaultValue: "debug");
    PackageInfo? packageInfo;
    try {
      packageInfo = await PackageInfo.fromPlatform();
    } catch(e) {
      dbgPrint('reportError: packageInfo failed with $e. Reporting without package info');
    }
    // Get browser info if on web platform
    final Map<String, dynamic> browserInfo = kIsWeb ? {
      'userAgent': html.window.navigator.userAgent,
      'platform': html.window.navigator.platform,
      'language': html.window.navigator.language,
      'vendor': html.window.navigator.vendor,
      'cookieEnabled': html.window.navigator.cookieEnabled,
      'onLine': html.window.navigator.onLine,
      'windowWidth': html.window.innerWidth,
      'windowHeight': html.window.innerHeight,
      'pixelRatio': html.window.devicePixelRatio,
    } : {};

    // Get current user if available
    final User? currentUser = FirebaseAuth.instance.currentUser;

    final errorData = {
      'errorHash': errorHash,
      'error': error.toString(),
      'stackTrace': stackTrace.toString(),
      'errorType': errorType ?? 'Unknown',
      'timestamp': DateTime.now().toIso8601String(),
      'appVersion': packageInfo?.version ?? 'Unknown',
      'buildNumber': packageInfo?.buildNumber ?? 'Unknown',
      'packageName': packageInfo?.packageName ?? 'Unknown',
      'sourceMapId': sourceMapId,
      'userId': currentUser?.uid ?? 'Not authenticated',
      'userEmail': currentUser?.email ?? 'Unknown',
      'platform': {
        'isWeb': kIsWeb,
        'browserInfo': browserInfo,
      },
    };

    try {
      // Attempt to report to Firebase
      final callable = FirebaseFunctions.instanceFor(region: 'asia-southeast1').httpsCallable('reportError');
      await callable.call(errorData);
      dbgPrint('Error reported to Firebase successfully');
    } catch (firebaseError) {
      // If Firebase fails, log to console
      dbgPrint('Failed to report to Firebase: $firebaseError');
      dbgPrint('Error details: $errorData');
    }
  } catch (e) {
    // If anything fails in the error reporting process, log to console
    dbgPrint('Error in reportError function: $e');
    dbgPrint('Original error: $error');
    dbgPrint('Original stack trace: $stackTrace');
  } finally {
    dbgPrint('Original error: $error');
    dbgPrint('Original stack trace: $stackTrace');
  }
}