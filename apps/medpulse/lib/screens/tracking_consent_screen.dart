import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';

import '../providers/tracking_transparency_provider.dart';

class TrackingConsentScreen extends ConsumerStatefulWidget {
  const TrackingConsentScreen({
    super.key,
  });

  @override
  TrackingConsentScreenState createState() => TrackingConsentScreenState();
}

class TrackingConsentScreenState extends ConsumerState<TrackingConsentScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => appTrackingTransparencyPlugIn());
  }

  @override
  Widget build(BuildContext context) {
    return Center(child: CircularProgressIndicator());
  }

  Future<void> appTrackingTransparencyPlugIn() async {
    try {
      final status = ref.read(trackingTransparencyProvider);
      // If the system can show an authorization request dialog
      if (status.valueOrNull == TrackingStatus.notDetermined) {
        // Show a custom explainer dialog before the system dialog
        await _showCustomTrackingDialog(context);
        // Wait for dialog popping animation
        await Future.delayed(const Duration(milliseconds: 200));
        // Request system's tracking authorization dialog
        TrackingStatus status = await AppTrackingTransparency.requestTrackingAuthorization();
        if (status == TrackingStatus.denied) {
          try {
            await FirebaseFunctions.instance.httpsCallable('optOutFromTracking').call();
          } on FirebaseFunctionsException catch (e) {
            dbgPrint("AppTrackingTransparency FirebaseFunctionsException: ${e.message}");
          } catch (e) {
            dbgPrint("AppTrackingTransparency FirebaseFunctions error: $e");
          }
        }
        ref.invalidate(trackingTransparencyProvider);
      }
    } on PlatformException catch (e) {
      dbgPrint("AppTrackingTransparency PlatformException: ${e.message}");
    } catch (e) {
      dbgPrint("Error in AppTrackingTransparency: $e");
    }
  }

  Future<void> _showCustomTrackingDialog(BuildContext context) async {
    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          "Enhance Your Learning Experience",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: RichText(
          text: TextSpan(
            style: DefaultTextStyle.of(context).style,
            children: const [
              TextSpan(
                text: "We value your privacy and want to be transparent about how we use data to improve your experience.\n\n",
              ),
              TextSpan(
                text: "By allowing tracking, you'll help us:\n\n",
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: "• Deliver personalized content and recommendations tailored to your learning needs\n\n"
                    "• Show relevant ads that align with your field of study\n\n"
                    "• Enhance features based on how students use the app\n\n",
              ),
              TextSpan(
                text: "You can modify this setting anytime in your device's Privacy settings.",
                style: TextStyle(fontSize: 13, color: Colors.grey),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Continue',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}
