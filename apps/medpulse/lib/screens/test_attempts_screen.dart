import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/test_result.dart';
import 'package:medpulse/features/mcq_test/widgets/answers_review_dialog.dart';
import 'package:medpulse/providers/test_attempt_provider.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/constrained_width_app_bar.dart';
import 'package:providers/common.dart';

import '../widgets/test_result_card.dart';

/// Screen that displays all test attempts by the user
class TestAttemptsScreen extends ConsumerWidget {
  const TestAttemptsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return Scaffold(
      appBar: ConstrainedWidthAppBar(appBar: AppBar(title: const Text('Test Attempts'))),
      body: userAsync.when(
        data: (user) {
          if (user == null || user.results == null || user.results!.isEmpty) {
            return const Center(child: Text('No test attempts yet'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: user.results!.length,
            itemBuilder: (context, index) {
              final result = user.results![index];
              return TestResultCard(result: result, onTap: () => _showTestResultDetails(context, ref, result));
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) {
          dbgPrint('Error loading user: $error');
          return Center(child: Text('Error: ${error.toString()}'));
        },
      ),
    );
  }

  Future<void> _showTestResultDetails(BuildContext context, WidgetRef ref, TestResult result) async {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Load the test progress
      final testProgress = await ref.read(testAttemptProvider(result.id).future);

      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      // Show the answers review dialog
      AnswersReviewDialog.show(
        context: context,
        subscriptionId: testProgress.result!.subscriptionId,
        testId: testProgress.result!.testId,
        // Not needed for review only
        free: testProgress.result!.free,
        // Not needed for review only
        progress: testProgress,
        questionCount: result.questionCount,
      );
    } catch (error) {
      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      // Show error dialog
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Error'),
              content: Text('Failed to load test details: ${error.toString()}'),
              actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
            ),
      );
    }
  }
}
