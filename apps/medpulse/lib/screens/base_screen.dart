import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/question_entity.dart';
import 'package:medpulse/screens/tests_screen.dart';
import 'package:providers/common.dart';

import '../providers/tab_page_index_provider.dart';
import '../services/analytics_service.dart';
import '../widgets/width_restrictor.dart';
import 'home_screen.dart';
import '../features/profile/screens/profile_screen.dart';

final GlobalKey<NavigatorState> homeNavigatorKey = GlobalKey<NavigatorState>();

class BaseScreen extends ConsumerStatefulWidget {
  const BaseScreen({super.key});

  @override
  ConsumerState createState() => _BaseScreenState();
}

class _BaseScreenState extends ConsumerState<BaseScreen> {
  final List<int> _tabHistory = [0]; // Start with the home tab
  /*
  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;
*/

  @override
  void initState() {
    super.initState();
    // initDeepLinks();
  }

  @override
  void dispose() {
    //    _linkSubscription?.cancel();
    super.dispose();
  }

  /*
  Future<void> initDeepLinks() async {
    _appLinks = AppLinks();
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) async {
      dbgPrint('onAppLink: $uri');
      await openAppLink(uri);
    });
  }
*/

  /*
  openAppLink(Uri appLink) async {
    final tab = ref.read(tabPageIndexProvider.notifier);
    final lc = context.locale.languageCode;
    dbgPrint("openAppLink: ${appLink.path}");
    if (appLink.path.contains("/details/artist/")) {
      dbgPrint("openAppLink: The string contains '/artist/'.");
      String id = appLink.path.substring(appLink.path.lastIndexOf("-") + 1);
      tab.setPageIndex(pageIndexArtists);
      final artist = await ref.read(artistProvider(id).future);
      if (artist == null) {
        _appLinkErrorSnackBar();
        return;
      }
      final title = getLocal(lc, artist.title);
      AnalyticsService().logEvent('deep_link_artist', {
        'artist_id': id,
        'artist_name': getLocal(lc, artist.title),
      });
      homeNavigatorKey.currentState?.push(
        nasheedPageRoute(builder: (context) => AlbumsScreen(artist: PagesArtistDoc(docId: id, title: title))),
      );
    }

    if (appLink.path.contains("/artists")) {
      dbgPrint("openAppLink: The string contains '/artists/'.");
      tab.setPageIndex(pageIndexArtists);
    }
    if (appLink.path.contains("/album/")) {
      dbgPrint("openAppLink: The string contains '/album/'.");
      tab.setPageIndex(pageIndexArtists);
      String id = appLink.path.substring(appLink.path.lastIndexOf("-") + 1);
      final album = await ref.read(albumProvider(id).future);
      if (album == null) {
        _appLinkErrorSnackBar();
        return;
      }
      AnalyticsService().logEvent('deep_link_album', {
        'album_id': id,
        'album_name': getLocal(lc, album.title),
      });
      homeNavigatorKey.currentState?.push(
        nasheedPageRoute(
            builder: (context) =>
                AlbumScreen(artistId: album.artistId, album: IdWithTitle(docId: id, title: album.title))),
      );
    }
    if (appLink.path.contains("/details/nasheed/")) {
      dbgPrint("openAppLink: Nasheed found in app link");
      String id = appLink.path.substring(appLink.path.lastIndexOf("-") + 1);
      final nasheed = await ref.read(nasheedProvider(id).future);
      if (nasheed == null) {
        _appLinkErrorSnackBar();
        return;
      }
      AnalyticsService().logEvent('deep_link_nasheed', {
        'nasheed_id': id,
        'nasheed_name': getLocal(lc, nasheed.title),
      });
      final current = ref.read(currentNasheedProvider.notifier);
      current.play(NasheedDetails(
        nasheedId: id,
        nasheedName: getLocal(lc, nasheed.title),
        artistId: nasheed.artistDetail.docId,
        artistName: getLocal(lc, nasheed.artistDetail.title),
      ));
    }
  }
*/

  /*
  void _appLinkErrorSnackBar() {
    final online = ref.read(connectivityProvider).valueOrNull ?? false;
    if (!online) {
      // that artist is not cached and we are offline.
      showSnackBar(context, context.tr('seemsLikeYouAreOffline'));
      return;
    } else {
      // the artist does not exist (any more?)
      showSnackBar(context, context.tr('invalidLink'));
      return;
    }
  }
*/

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // prevent the app from closing with back
      onPopInvokedWithResult: (didPop, result) async {
        // First, try to pop the current route in the nested navigator
        final navigator = homeNavigatorKey.currentState;
        if (navigator != null && await navigator.maybePop()) {
          return; // If we successfully popped a route, we're done
        }

        // If we couldn't pop a route and we have tab history, go to previous tab
        if (_tabHistory.length > 1) {
          _tabHistory.removeLast();
          int previousTab = _tabHistory.last;
          ref.read(tabPageIndexProvider.notifier).setPageIndex(previousTab);
        }
      },
      child: Scaffold(
        bottomNavigationBar: MedpulseNavigationBar(),
        body: Navigator(
          key: homeNavigatorKey,
          onGenerateRoute: (RouteSettings settings) {
            return MaterialPageRoute(builder: (context) => TabbedPages());
          },
        ),
      ),
    );
  }
}

class TabbedPages extends ConsumerWidget {
  TabbedPages({super.key});

  final _pages = <Widget>[
    HomeScreen(),
    TestsScreen(questionType: ContentType.mcq),
    TestsScreen(questionType: ContentType.flipCard),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPageIndex = ref.watch(tabPageIndexProvider);
    dbgPrint('BaseScreen: TabIndex $currentPageIndex');
    return _pages[currentPageIndex];
  }
}

class MedpulseNavigationBar extends ConsumerWidget {
  const MedpulseNavigationBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final useEmulator = const bool.fromEnvironment('USE_FIREBASE_EMULATOR', defaultValue: false);

    // Define constants for our layout requirements
    return WidthRestrictor(
      minWidth: 250,
      maxWidth: 500,
      child: NavigationBar(
        onDestinationSelected: (index) {
          homeNavigatorKey.currentState?.popUntil((route) => route.isFirst);
          final name = ref.read(tabPageIndexProvider.notifier).setPageIndex(index);
          AnalyticsService().logEvent('tab_menu_tapped', {'item': name});
          // Add the new tab to the history
          final baseScreenState = context.findAncestorStateOfType<_BaseScreenState>();
          baseScreenState?._tabHistory.add(index);
        },
        animationDuration: const Duration(milliseconds: 1000),
        destinations: <Widget>[
          NavigationDestination(icon: Icon(Icons.home_outlined), selectedIcon: Icon(Icons.home), label: 'Home'),
          NavigationDestination(
            icon: Icon(Icons.check_box_outlined),
            selectedIcon: Icon(Icons.check_box),
            label: 'MCQ',
          ),
          NavigationDestination(icon: Icon(Icons.flip_outlined), selectedIcon: Icon(Icons.flip), label: 'FlipCard'),
          NavigationDestination(icon: Icon(Icons.person_outline), selectedIcon: Icon(Icons.person), label: 'Profile'),
        ],
        selectedIndex: ref.watch(tabPageIndexProvider),
        // height: 60,
        // labelBehavior: NavigationDestinationLabelBehavior.onlyShowSelected,
        backgroundColor: Theme.of(context).colorScheme.surface,
        // elevation: 3,
      ),
    );
  }
}
