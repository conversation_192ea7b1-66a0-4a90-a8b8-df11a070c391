import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/common/common.dart';
import 'package:medpulse/providers/preferences_provider.dart';
import 'package:medpulse/widgets/height_restrictor.dart';
import 'package:medpulse/widgets/width_restrictor.dart';
import 'package:medpulse/widgets/glitchy_text_transition.dart'; // Import the new widget

import '../widgets/wide_button.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => OnboardingScreenState();
}

class OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF2BDFF), Color(0xFFC6D1FF)],
        ),
      ),
      child: PageView(controller: _pageController, children: [_buildFirstPage(), _buildSecondPage()]),
    );
  }

  Widget _buildFirstPage() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double maxImageHeight = 400;
        final double imageHeight = min(constraints.maxHeight * 0.5, maxImageHeight);
        final double fontSize = 48;
        final double maxWidth = 530;

        return HeightRestrictor(
          minHeight: 650,
          maxHeight: 2000,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Spacer(),
                SizedBox(height: imageHeight * 0.66, child: Image.asset('assets/images/onboarding_1.png', fit: BoxFit.contain)),
                const SizedBox(height: 32),
                GlitchyTextTransition(
                  initialText: 'Test',
                  finalText: 'Train',
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 10),
                Container(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text.rich(
                    TextSpan(
                      style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold),
                      children: [
                        TextSpan(text: 'Your '),
                        TextSpan(text: 'Medical', style: TextStyle(color: Theme.of(context).colorScheme.primary)),
                        TextSpan(text: ' Knowledge'),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text(
                    'Challenge yourself with engaging quizzes designed to expand your understanding of medicine and healthcare.',
                    textAlign: TextAlign.center,
                  ),
                ),
                Spacer(),
                const SizedBox(height: 24),
                _NextPage(pageController: _pageController),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSecondPage() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double maxImageHeight = 400;
        final double imageHeight = min(constraints.maxHeight * 0.5, maxImageHeight);
        final double fontSize = 48;
        final double maxWidth = 500;

        return HeightRestrictor(
          minHeight: 650,
          maxHeight: 2000,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Spacer(),
                SizedBox(height: imageHeight * 0.66, child: Image.asset('assets/images/onboarding_2.png', fit: BoxFit.contain)),
                const SizedBox(height: 32),
                GlitchyTextTransition(
                  initialText: 'Learn',
                  finalText: 'Study',
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                ),
                const SizedBox(height: 10),
                Container(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text.rich(
                    TextSpan(
                      style: TextStyle(fontSize: fontSize, fontWeight: FontWeight.bold),
                      children: [
                        TextSpan(text: 'At Your '),
                        TextSpan(text: 'Own', style: TextStyle(color: Theme.of(context).colorScheme.primary)),
                        TextSpan(text: ' Pace'),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text(
                    'Track your progress, discover new topics, and strengthen your expertise one quiz at a time.',
                    textAlign: TextAlign.center,
                  ),
                ),
                Spacer(),
                const SizedBox(height: 24),
                _StartNow(pageController: _pageController),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _StartNow extends ConsumerWidget {
  const _StartNow({super.key, required PageController pageController}) : _pageController = pageController;

  final PageController _pageController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool tablet = tabletDevice(context);
    return WidthRestrictor(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 20,
        children: [
          OutlinedButton(
            onPressed: () {
              _pageController.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
            },
            child: const Icon(Icons.chevron_left),
          ),
          if (!tablet) Expanded(child: const SizedBox.shrink()),
          FilledButton(
            onPressed: () {
              ref.read(preferencesProvider.notifier).setOnboardingCompleted(true);
            },
            child: const Text('Start now'),
          ),
        ],
      ),
    );
  }
}

/*
class _StartNowButton extends ConsumerWidget {
  const _StartNowButton({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: WideButton(
        onPressed: () {
          ref.read(preferencesProvider.notifier).setOnboardingCompleted(true);
        },
        child: const Text('Start now'),
      ),
    );
  }
}
*/

class _NextPage extends ConsumerWidget {
  const _NextPage({super.key, required PageController pageController}) : _pageController = pageController;

  final PageController _pageController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool tablet = tabletDevice(context);
    return WidthRestrictor(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        spacing: 20,
        children: [
          TextButton(
            onPressed: () {
              ref.read(preferencesProvider.notifier).setOnboardingCompleted(true);
            },
            child: const Text('Skip'),
          ),
          if (!tablet) Expanded(child: const SizedBox.shrink()),
          FilledButton(
            onPressed: () {
              _pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
            },
            child: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }
}
