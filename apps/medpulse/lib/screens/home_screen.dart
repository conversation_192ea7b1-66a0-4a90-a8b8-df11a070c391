import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/user_provider.dart';
import '../widgets/header_widget.dart';
import '../widgets/latest_test_attempts_section.dart';
import '../widgets/legal_documents_dialog.dart';


class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends ConsumerState<HomeScreen> {

  @override
  void initState() {
    super.initState();
    // read the current user
    ref.read(userProvider.future).then((user) {
      if (user != null) {
        // check if the user has accepted the legal documents
        final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
        if (hasAccepted) return;
        // add a postframe callback to show legal documents dialog
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!mounted) return;
          LegalDocumentsDialog.show(context: context);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 600),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section with Image and Welcome Message
                const HeaderWidget(
                  imagePath: 'assets/images/onboarding_1.png',
                  subtitle: 'Take the first step toward success, test your skills and achieve greatness!',
                ),

                /*
                SizedBox(height: 32),

                FilledButton(
                  onPressed: () {
                    PWAInstall().promptInstall_();
                  },
                  child: Text('Install PWA'),
                ),
*/
                SizedBox(height: 32),

                // What's New Section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.new_releases, color: Theme
                            .of(context)
                            .colorScheme
                            .primary),
                        SizedBox(width: 8),
                        Text("What's New", style: Theme
                            .of(context)
                            .textTheme
                            .titleLarge),
                      ],
                    ),
                    SizedBox(height: 16),
                    WhatsNewContent(),
                  ],
                ),
                SizedBox(height: 24),

                // Latest Test Attempts Section
                const LatestTestAttemptsSection(),

                SizedBox(height: 24),

                // Top Results Section
/*                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.emoji_events, color: Theme
                            .of(context)
                            .colorScheme
                            .primary),
                        SizedBox(width: 8),
                        Text('Top Results', style: Theme
                            .of(context)
                            .textTheme
                            .titleLarge),
                      ],
                    ),
                    SizedBox(height: 16),
                    TopResultsContent(),
                  ],
                ),*/
              ],
            ),
          ),
        ),
      ),
    );
  }
}


class WhatsNewContent extends StatelessWidget {
  const WhatsNewContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var item in _getWhatsNewItems())
          Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Icon(item.icon, size: 20),
                SizedBox(width: 12),
                Expanded(child: Text(item.text, style: Theme
                    .of(context)
                    .textTheme
                    .bodyMedium)),
              ],
            ),
          ),
      ],
    );
  }

  List<WhatsNewItem> _getWhatsNewItems() {
    return [
      WhatsNewItem(icon: Icons.campaign, text: 'MedPulse Release Promotion Period Has Started!'),
      WhatsNewItem(icon: Icons.quiz, text: 'MCQ Paper B Mock Test for KMU Now Available'),
      WhatsNewItem(icon: Icons.library_books, text: 'Comprehensive Proteins Biochemistry Flashcards Added'),
    ];
  }
}

class TopResultsContent extends StatelessWidget {
  const TopResultsContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var result in _getTopResults())
          Padding(
            padding: EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Theme
                      .of(context)
                      .colorScheme
                      .primaryContainer,
                  child: Text(
                    '${result.rank}',
                    style: TextStyle(color: Theme
                        .of(context)
                        .colorScheme
                        .onPrimaryContainer),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(result.name, style: Theme
                          .of(context)
                          .textTheme
                          .titleMedium),
                      Text('${result.score}% in ${result.subject}', style: Theme
                          .of(context)
                          .textTheme
                          .bodyMedium),
                    ],
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  List<TopResult> _getTopResults() {
    return [
      TopResult(rank: 1, name: 'Ahmed Hassan', score: 98, subject: 'Pathology Test'),
      TopResult(rank: 2, name: 'Fatima Khan', score: 96, subject: 'Biochemistry MCQs'),
      TopResult(rank: 3, name: 'Muhammad Ali', score: 95, subject: 'Pharmacology Test'),
    ];
  }
}

class WhatsNewItem {
  final IconData icon;
  final String text;

  WhatsNewItem({required this.icon, required this.text});
}

class TopResult {
  final int rank;
  final String name;
  final int score;
  final String subject;

  TopResult({required this.rank, required this.name, required this.score, required this.subject});
}
