import 'package:flutter/material.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({
    required this.context,
    required this.description,
    required this.onPressed,
    super.key,
  });

  final BuildContext context;
  final String description;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          spacing: 20,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            Text(
              'Hmmm',
              style: TextStyle(fontSize: 48),
            ),
            Text(
              description,
              textAlign: TextAlign.center,
              style: TextStyle(height: 1.5),
            ),
            SizedBox(
              width: 100,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  backgroundColor: Theme.of(context).brightness == Brightness.dark ? Colors.red : Colors.grey[300],
                ),
                onPressed: onPressed,
                child: Icon(
                  Icons.refresh,
                  size: 30,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
