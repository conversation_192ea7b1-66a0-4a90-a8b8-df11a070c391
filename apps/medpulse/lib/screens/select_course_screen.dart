import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/services/error_reporting_service.dart';
import 'package:medpulse/widgets/header_widget.dart';
import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:entities/course_entity.dart';

import '../providers/courses_provider.dart';
import '../providers/user_provider.dart';
import 'error_screen.dart';

part 'select_course_screen.g.dart';

// Provider to initialize user's course selection from Firebase
@riverpod
class UserSelectionInitializer extends _$UserSelectionInitializer {
  @override
  Future<void> build() async {
    // Get user and course data
    final user = await ref.watch(userProvider.future);
    dbgPrint('UserSelectionInitializerProvider: start ${user?.uid}');

    // If user has courseId, we need to initialize their selection
    if (user != null && user.courseId != null) {
      final courses = await ref.watch(coursesProvider.future);

      if (courses != null) {
        final course = courses.courses[user.courseId];
        if (course != null) {
          // Initialize the course selection
          ref.read(selectedCourseProvider.notifier).setCourse(course);

          // Initialize the year selection if available
          if (user.yearId != null) {
            ref.read(selectedYearProvider.notifier).setYear(user.yearId);
          }
        }
      }
    }
  }
}

@riverpod
class SelectedCourse extends _$SelectedCourse {
  @override
  CourseEntity? build() => null;

  void setCourse(CourseEntity? course) => state = course;
}

@riverpod
class SelectedYear extends _$SelectedYear {
  @override
  String? build() => null;

  void setYear(String? year) => state = year;
}

@riverpod
List<String> availableYears(Ref ref) {
  final selectedCourse = ref.watch(selectedCourseProvider);
  if (selectedCourse == null) return [];
  final years = selectedCourse.years.keys.toList();
  years.sort(); // Sort the years alphabetically
  return years;
}

class SelectCourseScreen extends ConsumerWidget {
  const SelectCourseScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the initializer to ensure user data is loaded properly
    final userAsync = ref.watch(userProvider);
    final user = userAsync.valueOrNull;

    if (userAsync.hasError) {
      reportError(userAsync.error, userAsync.stackTrace ?? StackTrace.current);
      return ErrorScreen(
        context: context,
        description: 'Error loading user data. Please try again.\n${userAsync.error}',
        onPressed: () {
          ref.invalidate(userProvider);
        },
      );
    }

    if (user == null) {
      return Center(child: CircularProgressIndicator());
    }

    // Watch the initializer to ensure user data is loaded properly
    final initializer = ref.watch(userSelectionInitializerProvider);
    if (initializer.isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with image and welcome message
            const HeaderWidget(
              imagePath: 'assets/images/onboarding_1.png',
              title: 'Select Your Course',
              subtitle: 'Pick your course and year to get started. You can change this at any time later.',
            ),
            const SizedBox(height: 32),

            // Course Selection Section
            Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 450),
                child: const Card(child: Padding(padding: EdgeInsets.all(16), child: _SelectionForm())),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Main selection form with course and year dropdowns
class _SelectionForm extends ConsumerWidget {
  const _SelectionForm();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const _CourseDropdown(),
        const SizedBox(height: 16),
        const _YearDropdown(),
        const SizedBox(height: 24),
        const _ContinueButton(),
      ],
    );
  }
}

// Course dropdown component
class _CourseDropdown extends ConsumerWidget {
  const _CourseDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coursesAsync = ref.watch(coursesProvider);
    final selectedCourse = ref.watch(selectedCourseProvider);

    return coursesAsync.when(
      data: (coursesData) {
        var coursesList = coursesData?.courses.values.toList() ?? [];
        coursesList.sort((a, b) => a.id.compareTo(b.id));
        return DropdownButtonFormField<CourseEntity>(
          value: selectedCourse,
          decoration: const InputDecoration(labelText: 'Select Course', border: OutlineInputBorder()),
          items:
              coursesList.map((course) {
                return DropdownMenuItem(value: course, child: Text(course.id));
              }).toList(),
          onChanged: (newValue) {
            ref.read(selectedCourseProvider.notifier).setCourse(newValue);
            ref.read(selectedYearProvider.notifier).setYear(null);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}

// Year dropdown component
class _YearDropdown extends ConsumerWidget {
  const _YearDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCourse = ref.watch(selectedCourseProvider);
    final selectedYear = ref.watch(selectedYearProvider);
    final availableYears = ref.watch(availableYearsProvider);

    if (selectedCourse == null) {
      return const SizedBox.shrink();
    }

    return DropdownButtonFormField<String>(
      value: selectedYear,
      decoration: const InputDecoration(labelText: 'Select Year', border: OutlineInputBorder()),
      items:
          availableYears.map((year) {
            return DropdownMenuItem(value: year, child: Text(year));
          }).toList(),
      onChanged: (newValue) {
        // This is inside an event handler, so it's appropriate to call notifiers
        ref.read(selectedYearProvider.notifier).setYear(newValue);
      },
    );
  }
}

// Continue button component
class _ContinueButton extends ConsumerStatefulWidget {
  const _ContinueButton();

  @override
  _ContinueButtonState createState() => _ContinueButtonState();
}

class _ContinueButtonState extends ConsumerState<_ContinueButton> {
  bool _isSaving = false;

  @override
  Widget build(BuildContext context) {
    final selectedCourse = ref.watch(selectedCourseProvider);
    final selectedYear = ref.watch(selectedYearProvider);

    final bool isEnabled = selectedCourse != null && selectedYear != null && !_isSaving;

    return FilledButton(
      onPressed: isEnabled ? () => _saveCourseAndYear(context) : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child:
            _isSaving
                ? const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
                    SizedBox(width: 8),
                    Text('Saving...'),
                  ],
                )
                : const Text('Continue'),
      ),
    );
  }

  Future<void> _saveCourseAndYear(BuildContext context) async {
    final selectedCourse = ref.read(selectedCourseProvider);
    final selectedYear = ref.read(selectedYearProvider);

    if (selectedCourse == null || selectedYear == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // Update user data
      await ref.read(userProvider.notifier).updateCourseAndYear(selectedCourse.id, selectedYear);
      if (!context.mounted) return;
      final error = ref.read(userProvider).error;
      if (error != null) {
        throw Exception(error);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text("Course and year saved successfully!")));
      }
    } catch (error) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("Error: ${error.toString()}")));
      }
      rethrow;
    } finally {
      if (context.mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
