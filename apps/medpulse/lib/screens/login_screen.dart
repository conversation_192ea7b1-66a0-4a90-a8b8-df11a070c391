import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';
import 'package:providers/google_sign_in.dart';
import 'package:providers/login_provider.dart';
import 'package:google_sign_in_web/web_only.dart' as web;
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/auth_social_button.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';

class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final loginState = ref.watch(loginProvider);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'MedPulse',
                  style: theme.textTheme.displayLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'Your personal medical exam preparation companion',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 48),
                Text(
                  'Sign in to access your personalized study plan',
                  textAlign: TextAlign.center,
                ),
                if (loginState.hasError) ...[
                  const SizedBox(height: 16),
                  Text(
                    'An error occurred. Please try again. ${ref.read(loginProvider).error}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                const SizedBox(height: 24),
                AuthSocialButton(
                  text: 'Google',
                  icon: Icons.g_mobiledata,
                  onPressed: loginState.isLoading ? null : () => ref.read(loginProvider.notifier).signInWithGoogle(),
                ),
                const SizedBox(height: 16),
                AuthSocialButton(
                  text: 'Apple',
                  icon: Icons.apple,
                  onPressed: loginState.isLoading ? null : () => ref.read(loginProvider.notifier).signInWithApple(),
                ),
                const SizedBox(height: 16),
                /*
                _SocialButton(
                  text: 'Facebook',
                  icon: Icons.facebook,
                  onPressed: loginState.isLoading
                      ? null
                      : () => ref.read(loginProvider.notifier).signInWithFacebook(),
                ),
            */
                const SizedBox(height: 24),
                if (loginState.isLoading)
                  const Center(child: CircularProgressIndicator())
                else
                  Text(
                    'or',
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                const SizedBox(height: 24),
                OutlinedButton(
                  onPressed: loginState.isLoading
                    ? null
                    : () async {
                        await ref.read(loginProvider.notifier).signInAnonymously();
                        if (context.mounted) {
                          final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
                          if (!hasAccepted) {
                            // Show the legal documents dialog
                            await LegalDocumentsDialog.show(context: context);
                          }
                        }
                      },
                  child: const Text('Explore MedPulse'),
                ),
                const SizedBox(height: 16),
                Text(
                  'Start your medical prep journey now - upgrade anytime to unlock full access',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SocialButton extends StatelessWidget {
  const _SocialButton({
    required this.text,
    required this.icon,
    required this.onPressed,
  });

  final String text;
  final IconData icon;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      constraints: const BoxConstraints(minWidth: 150, maxWidth: 400, maxHeight: 50),
      child: FilledButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(text),
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }
}


class _GoogleSocialButton extends ConsumerWidget {
  const _GoogleSocialButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final googleSignIn = ref.watch(googleSignInProvider);
    dbgPrint(googleSignIn.toString());
    // right now we did this only for web, android works differently
    if (!kIsWeb) {
      throw Exception('Google sign in Button is not implemented for this platform');
    }
    return Container(
      width: double.maxFinite,
      constraints: const BoxConstraints(minWidth: 150, maxWidth: 400, maxHeight: 50),
      child: web.renderButton(
        configuration: web.GSIButtonConfiguration(
          logoAlignment: null,
          minimumWidth: null,
          shape: web.GSIButtonShape.rectangular,
          type: web.GSIButtonType.standard,
          text: web.GSIButtonText.continueWith,
          theme: web.GSIButtonTheme.outline,
          size: web.GSIButtonSize.medium,
        ),
      ),
    );
  }
}


/*
class _GoogleButton extends ConsumerWidget {
  const _GoogleButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (kIsWeb) {
      ref.watch(googleSignInProvider);
      return web_sign_in.renderButton(configuration:
          web_sign_in.GSIButtonConfiguration(
            logoAlignment: null,
            minimumWidth: null,
            shape: web_sign_in.GSIButtonShape.rectangular,
            type: web_sign_in.GSIButtonType.standard,
            text: web_sign_in.GSIButtonText.continueWith,
            theme: web_sign_in.GSIButtonTheme.outline,
            size: web_sign_in.GSIButtonSize.medium,
          )
      );
    }
    throw Exception('Google sign in Button is not implemented for this platform');
  }
}
*/
