import 'package:flutter/material.dart';

class SomethingWentWrongScreen extends StatelessWidget {
  const SomethingWentWrongScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: NasheedAppBar(showLogo: false, showSearchIcon: false),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 500),
          margin: EdgeInsets.all(20),
          child: Column(
            children: [
              Spacer(flex: 2),
              Text('Hmmm...', style: TextStyle(fontSize: 48)),
              SizedBox(height: 20),
              Text(
                'Something went wrong.\nA bug report will be sent and our team will look into this problem.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 24),
              ),
              Spacer(flex: 3),
            ],
          ),
        ),
      ),
    );
  }
}
