import 'dart:convert';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

/// Utility class for handling string-level encryption
class StringUtil {

  /// Decrypts a string that has an IV prepended to it
  /// Expects a base64 encoded string containing IV + encrypted data
  static String? dc(String? encodedValue, encrypt.Key key) {

    if (encodedValue == null) return null;

    // Decode the combined data
    final combined = base64Decode(encodedValue);

    // First 16 bytes are the IV
    final ivBytes = combined.sublist(0, 16);
    final iv = encrypt.IV(ivBytes);

    // Rest is the encrypted content
    final encryptedBytes = combined.sublist(16);

    // Create encrypter with the provided key
    final encrypter = encrypt.Encrypter(encrypt.AES(key));

    // Decrypt using the extracted IV
    var value = encrypter.decrypt(encrypt.Encrypted(encryptedBytes), iv: iv);
    return value == '!"§!"§' ? '' : value;
  }

  /// Generates an encryption key from the test ID
  /// Uses a combination of the test ID and its MD5 hash to derive a key
  static encrypt.Key gk(String testId) {
    // Create MD5 hash of the test ID
    final testIdHash = md5.convert(utf8.encode(testId)).toString();

    // Combine the test ID and its hash
    final combinedString = '$testId:$testIdHash';

    // Create SHA-256 hash of the combined string to get exactly 32 bytes (256 bits)
    final keyBytes = sha256.convert(utf8.encode(combinedString)).bytes;

    // Create encryption key from the hash
    return encrypt.Key(Uint8List.fromList(keyBytes));
  }
}