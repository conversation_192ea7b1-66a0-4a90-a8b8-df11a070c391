// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBvUPVawj0X44SnRZhA4WcSIKyCqSRi4Cg',
    appId: '1:99761044972:web:4c50179fe732e7315454c9',
    messagingSenderId: '99761044972',
    projectId: 'medpulse-prod',
    authDomain: 'medpulse-prod.firebaseapp.com',
    storageBucket: 'medpulse-prod.appspot.com',
    measurementId: 'G-DMGXPE6JWY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyATl5RGKmjnQ2chUIoQJsMc2QNxJxRIC_w',
    appId: '1:99761044972:android:3d3cacd67d1d4d2a5454c9',
    messagingSenderId: '99761044972',
    projectId: 'medpulse-prod',
    storageBucket: 'medpulse-prod.appspot.com',
    measurementId: 'G-DMGXPE6JWY',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7MVJ9KuByMdSZ4SvSaJeKHVPxzDzHzIg',
    appId: '1:99761044972:ios:f75192ffd17747525454c9',
    messagingSenderId: '99761044972',
    projectId: 'medpulse-prod',
    storageBucket: 'medpulse-prod.appspot.com',
    androidClientId: '99761044972-76ks61b40rqo551bsbji91ntblkqe4oe.apps.googleusercontent.com',
    iosClientId: '99761044972-8rbjgiihb7i3cc5su6vecpeegjbbtda9.apps.googleusercontent.com',
    iosBundleId: 'com.medpulse.client.medpulse',
    measurementId: 'G-DMGXPE6JWY',
  );

}