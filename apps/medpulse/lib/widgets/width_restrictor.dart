import 'package:flutter/material.dart';

class WidthRestrictor extends StatelessWidget {
  final Widget child;
  final double minWidth;
  final double maxWidth;

  const WidthRestrictor({super.key, required this.child, this.minWidth = 250.0, this.maxWidth = 500.0});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the container width based on available space
        double containerWidth = constraints.maxWidth;

        // Apply min/max constraints to our container
        if (containerWidth > maxWidth) {
          containerWidth = maxWidth;
        } else if (containerWidth < minWidth) {
          containerWidth = minWidth;
        }

        return Stack(alignment: Alignment.bottomCenter, children: [SizedBox(width: containerWidth, child: child)]);
      },
    );
  }
}
