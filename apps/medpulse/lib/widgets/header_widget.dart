import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// A reusable header widget with an image and text content.
///
/// This widget displays an image on the left side and title/subtitle text on the right.
/// It's designed to be used as a header section in various screens of the application.
class HeaderWidget extends StatelessWidget {
  /// The asset path for the image to display.
  final String imagePath;

  /// The title text to display.
  /// If null, a welcome message with the user's name will be displayed.
  final String? title;

  /// The subtitle text to display below the title.
  final String subtitle;

  /// Optional maximum width constraint for the header.
  final double? maxWidth;

  /// Optional image height.
  final double imageHeight;

  /// Optional border radius for the image.
  final double borderRadius;

  const HeaderWidget({
    super.key,
    required this.imagePath,
    this.title,
    required this.subtitle,
    this.maxWidth = 650,
    this.imageHeight = 150,
    this.borderRadius = 12,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth ?? double.infinity),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(borderRadius),
              child: Image.asset(
                imagePath,
                height: imageHeight,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null)
                    Text(
                      title!,
                      style: Theme.of(context).textTheme.headlineMedium,
                    )
                  else
                    Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: 'Welcome, ',
                            style: Theme.of(context).textTheme.headlineMedium,
                          ),
                          TextSpan(
                            text: FirebaseAuth.instance.currentUser?.displayName ?? 'Guest',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 8),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(179), // ~70% opacity
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}