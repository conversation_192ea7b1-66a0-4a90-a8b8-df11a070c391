/*
import 'package:flutter/material.dart';
import 'package:entities/user_entity.dart';
import 'package:medpulse/services/paymob_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/providers/firebase_user_provider.dart';
import 'package:providers/login_provider.dart';
import 'package:intl/intl.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';
import 'package:medpulse/widgets/upgrade_dialog.dart' as new_dialog;

// This file is deprecated and will be removed in a future release.
// Use the new subscription feature in lib/features/subscribe instead.

import 'payment_iframe.dart';

// DEPRECATED: Use the new subscription feature in lib/features/subscribe instead.
@Deprecated('Use the new subscription feature in lib/features/subscribe instead.')
class SubscriptionDialog extends ConsumerStatefulWidget {
  const SubscriptionDialog({super.key});

  @override
  ConsumerState<SubscriptionDialog> createState() => _SubscriptionDialogState();
}

class _SubscriptionDialogState extends ConsumerState<SubscriptionDialog> with SingleTickerProviderStateMixin {
  bool _isLoading = false;
  bool _isProcessing = false;
  bool _processingComplete = false;
  bool _processingSuccess = false;
  bool _showingIframe = false;
  String? _error;
  String? _processingPlanType;
  String? _transactionId;
  String? _paymentUrl;
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Authentication related state
  bool _authInProgress = false;
  String? _authError;

  // For processing animation
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentStep = 0;
  final List<String> _processingSteps = [
    'Initializing payment...',
    'Connecting to payment gateway...',
    'Preparing subscription details...',
    'Securing transaction...',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 1500));
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _startProcessingAnimation() {
    _animationController.repeat();
    _currentStep = 0;
    _advanceProcessingStep();
  }

  void _advanceProcessingStep() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isProcessing && _currentStep < _processingSteps.length - 1) {
        setState(() {
          _currentStep++;
        });
        _advanceProcessingStep();
      }
    });
  }

  void _handlePaymentComplete(bool success, String? message) {
    if (!mounted) return;

    setState(() {
      _showingIframe = false;
      _processingComplete = true;
      _processingSuccess = success;
      if (!success) {
        _error = message ?? 'Payment failed';
      }
    });

    // Stop the animation
    _animationController.stop();
  }

  // Authentication methods
  Future<void> _handleGoogleSignIn() async {
    if (!mounted) return;

    setState(() {
      _authInProgress = true;
      _authError = null;
    });

    try {
      // Use the loginProvider to sign in with Google
      await ref.read(loginProvider.notifier).signInWithGoogle();

      // Check if the sign-in was successful
      final loginState = ref.read(loginProvider);
      if (loginState.hasError) {
        if (!mounted) return;
        setState(() {
          _authInProgress = false;
          _authError = 'Failed to sign in with Google: ${loginState.error}';
        });
        return;
      }

      // Check if the user has accepted the legal documents
      if (mounted) {
        final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
        if (!hasAccepted) {
          // Show the legal documents dialog
          final accepted = await LegalDocumentsDialog.show(context: context);
          if (accepted != true) {
            // Reset auth state
            setState(() {
              _authInProgress = false;
              _authError = null;
            });
            return;
          }
        }
      }

      // If authentication was successful and we have a plan type stored, continue with subscription
      if (_processingPlanType != null && mounted) {
        final planType = _processingPlanType!;

        // Reset auth state
        setState(() {
          _authInProgress = false;
          _authError = null;
        });

        // Navigate back to plans page
        _navigateToPage(1);

        // Continue with subscription
        _handleSubscription(planType);
      } else if (mounted) {
        // Just navigate back to plans page if no plan was selected
        _navigateToPage(1);

        // Reset auth state
        setState(() {
          _authInProgress = false;
          _authError = null;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _authInProgress = false;
        _authError = 'Failed to sign in: ${e.toString()}';
      });
    }
  }

  Future<void> _handleAppleSignIn() async {
    if (!mounted) return;

    setState(() {
      _authInProgress = true;
      _authError = null;
    });

    try {
      // Use the loginProvider to sign in with Apple
      await ref.read(loginProvider.notifier).signInWithApple();

      // Check if the sign-in was successful
      final loginState = ref.read(loginProvider);
      if (loginState.hasError) {
        if (!mounted) return;
        setState(() {
          _authInProgress = false;
          _authError = 'Failed to sign in with Apple: ${loginState.error}';
        });
        return;
      }

      // Check if the user has accepted the legal documents
      if (mounted) {
        final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
        if (!hasAccepted) {
          // Show the legal documents dialog
          final accepted = await LegalDocumentsDialog.show(context: context);
          if (accepted != true) {
            // Reset auth state
            setState(() {
              _authInProgress = false;
              _authError = null;
            });
            return;
          }
        }
      }

      // If authentication was successful and we have a plan type stored, continue with subscription
      if (_processingPlanType != null && mounted) {
        final planType = _processingPlanType!;

        // Reset auth state
        setState(() {
          _authInProgress = false;
          _authError = null;
        });

        // Navigate back to plans page
        _navigateToPage(1);

        // Continue with subscription
        _handleSubscription(planType);
      } else if (mounted) {
        // Just navigate back to plans page if no plan was selected
        _navigateToPage(1);

        // Reset auth state
        setState(() {
          _authInProgress = false;
          _authError = null;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _authInProgress = false;
        _authError = 'Failed to sign in: ${e.toString()}';
      });
    }
  }

  void _navigateToPage(int page) {
    _pageController.animateToPage(page, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    setState(() {
      _currentPage = page;
    });
  }

  Future<void> _handleSubscription(String planType) async {
    if (!mounted) return;

    // Check if the user is anonymous
    final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
    if (firebaseUser != null && firebaseUser.isAnonymous) {
      // Store the plan type for later use after authentication
      _processingPlanType = planType;

      // Navigate to the authentication page
      _navigateToPage(2); // Index 2 is the authentication page
      return;
    }

    // Check if the user has accepted the legal documents
    final user = ref.read(userProvider).valueOrNull;
    if (user != null) {
      if (!mounted) return;

      // Check if the user has accepted the legal documents
      final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
      if (!hasAccepted) {
        if (!mounted) return;
        // Show the legal documents dialog
        final accepted = await LegalDocumentsDialog.show(context: context);
        if (accepted != true || !mounted) return;
      }
    }

    setState(() {
      _isLoading = true;
      _isProcessing = true;
      _processingPlanType = planType;
      _error = null;
    });

    // Start the processing animation
    _startProcessingAnimation();

    try {
      final user = ref.read(userProvider).value;
      if (user == null) {
        if (!mounted) return;
        setState(() {
          _error = 'Please sign in to subscribe';
          _isLoading = false;
          _isProcessing = false;
        });
        return;
      }

      final paymobService = PaymobService();
      final subscriptionId = '${user.courseId}:${user.yearId}';
      final callbackUrl = 'https://us-central1-medpulse-8d1b7.cloudfunctions.net/handlePaymobCallback';

      // Initiate payment (async operation)
      final result = await paymobService.initiatePayment(
        user: user,
        subscriptionType: planType,
        callbackUrl: callbackUrl,
        subscriptionId: subscriptionId,
      );

      // Update processing state to complete
      if (mounted) {
        setState(() {
          _processingComplete = true;
          _processingSuccess = true;
          _transactionId = result['transactionId'];
          _paymentUrl = result['paymentUrl'];
          // Keep _isProcessing true to continue showing the processing UI
        });

        // Stop the animation
        _animationController.stop();
      }

      // Set state to show iframe instead of launching URL in a new tab
      if (mounted) {
        setState(() {
          _showingIframe = true;
        });
      }
    } catch (e) {
      // After async gap, check if still mounted
      if (!mounted) return;

      // Update state to show error in the processing UI
      setState(() {
        _processingComplete = true;
        _processingSuccess = false;
        _error = 'Failed to initiate payment: $e';
        // Keep _isProcessing true to continue showing the processing UI
      });

      // Stop the animation
      _animationController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userProvider).value;
    final theme = Theme.of(context);

    // If in processing state, show the processing UI
    if (_isProcessing && _processingPlanType != null) {
      // If showing iframe, display the payment iframe
      if (_showingIframe && _paymentUrl != null) {
        return AlertDialog(
          title: const Text('Complete Payment'),
          content: Container(
            constraints: const BoxConstraints(maxWidth: 500),
            child: SizedBox(
              height: 480, // Fixed height for consistency
              width: 450,
              child: PaymentIframeFactory.create(paymentUrl: _paymentUrl!, onPaymentComplete: _handlePaymentComplete),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                setState(() {
                  _showingIframe = false;
                  _processingComplete = true;
                  _processingSuccess = false;
                  _error = 'Payment cancelled';
                });
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      }

      final String planTypeFormatted = _processingPlanType!.replaceAll('_', ' ').toUpperCase();

      // Determine title and status based on processing state
      String title = 'Processing Payment';
      Widget statusIcon;
      String statusMessage = '';
      List<Widget> actions = [];

      if (_processingComplete) {
        if (_processingSuccess) {
          title = 'Payment Completed';
          statusIcon = Icon(Icons.check_circle, color: Colors.green, size: 60);
          statusMessage = 'Your payment has been successfully processed. Thank you for your subscription!';

          // Add continue button for success case
          actions.add(
            FilledButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog

                // If we have a transaction ID, show the transaction status dialog
                if (_transactionId != null) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => _TransactionStatusDialog(transactionId: _transactionId!),
                  );
                }
              },
              child: const Text('Continue'),
            ),
          );
        } else {
          title = 'Payment Failed';
          statusIcon = Icon(Icons.error, color: Colors.red, size: 60);
          statusMessage = _error ?? 'An error occurred while processing your payment.';

          // Add try again button for failure case
          actions.add(
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog

                // Show subscription dialog again
                showDialog(context: context, builder: (context) => SubscriptionDialog());
              },
              child: const Text('Try Again'),
            ),
          );
        }
      } else {
        // Still processing
        statusIcon = RotationTransition(
          turns: _animation,
          child: Container(
            width: 60,
            height: 60,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(color: theme.colorScheme.primary.withAlpha(26), shape: BoxShape.circle),
            // 0.1 * 255 = 25.5, rounded to 26
            child: Icon(Icons.sync, color: theme.colorScheme.primary, size: 32),
          ),
        );
      }

      return AlertDialog(
        title: Text(title),
        content: Container(
          constraints: const BoxConstraints(maxWidth: 500),
          child: SizedBox(
            height: 480, // Fixed height for consistency
            width: 450,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                statusIcon,
                const SizedBox(height: 30),
                Text(
                  _processingComplete ? statusMessage : 'Preparing your $planTypeFormatted subscription',
                  style: theme.textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                if (!_processingComplete) ...[
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: theme.colorScheme.outline.withAlpha(77),
                      ), // 0.3 * 255 = 76.5, rounded to 77
                    ),
                    child: Column(
                      children: [
                        for (int i = 0; i < _processingSteps.length; i++)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                if (i < _currentStep)
                                  Icon(Icons.check_circle, color: Colors.green, size: 18)
                                else if (i == _currentStep)
                                  SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2))
                                else
                                  Icon(Icons.circle_outlined, color: Colors.grey, size: 18),
                                const SizedBox(width: 12),
                                Text(
                                  _processingSteps[i],
                                  style: TextStyle(
                                    color: i <= _currentStep ? theme.colorScheme.onSurface : Colors.grey,
                                    fontWeight: i == _currentStep ? FontWeight.bold : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Please do not close this window',
                    style: TextStyle(color: theme.colorScheme.error, fontSize: 12),
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: actions,
      );
    }

    // Regular subscription dialog
    return AlertDialog(
      title: const Text('Upgrade to Premium'),
      content: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: SizedBox(
          height: 480, // Fixed height for consistency
          width: 450,
          child: PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(), // Disable swiping
            children: [
              _BenefitsPage(user: user!),
              _SubscriptionPlansPage(
                user: user,
                isLoading: _isLoading,
                error: _error,
                onSubscribe: _handleSubscription,
              ),
              _AuthenticationPage(
                isLoading: _authInProgress,
                error: _authError,
                onGoogleSignIn: _handleGoogleSignIn,
                onAppleSignIn: _handleAppleSignIn,
              ),
            ],
          ),
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (_currentPage == 1)
              TextButton.icon(
                onPressed: _isLoading ? null : () => _navigateToPage(0),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Benefits'),
              )
            else if (_currentPage == 2)
              TextButton.icon(
                onPressed: _authInProgress ? null : () => _navigateToPage(1),
                icon: const Icon(Icons.arrow_back),
                label: const Text('Plans'),
              )
            else
              const SizedBox.shrink(),
            if (_currentPage == 0)
              FilledButton.icon(
                onPressed: () => _navigateToPage(1),
                icon: const Icon(Icons.arrow_forward),
                label: const Text("Let's do this"),
              )
            else if (_currentPage != 2) // Don't show cancel on auth page
              TextButton(onPressed: _isLoading ? null : () => Navigator.of(context).pop(), child: const Text('Cancel'))
            else
              const SizedBox.shrink(),
          ],
        ),
      ],
    );
  }
}

class _BenefitsPage extends StatelessWidget {
  final UserEntity user;

  const _BenefitsPage({required this.user});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (user.courseId != null && user.yearId != null) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(color: Colors.black),
                  children: [
                    const TextSpan(text: 'Course: ', style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: user.courseId ?? ''),
                    const TextSpan(text: '  '),
                    const TextSpan(text: 'Year: ', style: TextStyle(fontWeight: FontWeight.bold)),
                    TextSpan(text: user.yearId ?? ''),
                  ],
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Text(
                'This subscription will grant you full access to all premium content for the selected course and year, including:',
                style: TextStyle(fontSize: 14),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _ExpandableFeatureCard(
                    title: 'Complete MCQ questions and answers',
                    icon: Icons.question_answer,
                    description:
                        'Access our extensive database of Multiple Choice Questions with detailed explanations for right and wrong answers to solidify your understanding.',
                    color: Colors.blue,
                  ),
                  const SizedBox(height: 8),
                  _ExpandableFeatureCard(
                    title: 'Complete Flipcard questions and answers',
                    icon: Icons.flip,
                    description:
                        'Practice with interactive flashcards designed to help you master key concepts through active recall techniques.',
                    color: Colors.purple,
                  ),
                  const SizedBox(height: 8),
                  _ExpandableFeatureCard(
                    title: 'Bookmark questions for recap',
                    icon: Icons.bookmark,
                    description:
                        'Save challenging questions to revisit later. Create your personalized study list for efficient exam preparation.',
                    color: Colors.orange,
                  ),
                  const SizedBox(height: 8),
                  _ExpandableFeatureCard(
                    title: 'Access to all new and updated content',
                    icon: Icons.new_releases,
                    description:
                        'Automatically receive access to new tests, questions, and content updates throughout your subscription period at no additional cost.',
                    color: Colors.green,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _SubscriptionPlansPage extends StatelessWidget {
  final UserEntity? user;
  final bool isLoading;
  final String? error;
  final Function(String) onSubscribe;

  const _SubscriptionPlansPage({
    required this.user,
    required this.isLoading,
    required this.error,
    required this.onSubscribe,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Choose your subscription plan:'),
          const SizedBox(height: 16),
          if (error != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(error!, style: TextStyle(color: Theme.of(context).colorScheme.error)),
            ),
          _SubscriptionPlanCard(
            title: '3 Months',
            price: 998,
            icon: Icons.calendar_today,
            onPressed: isLoading ? null : () => onSubscribe('3_months'),
            isLoading: isLoading,
          ),
          const SizedBox(height: 12),
          _SubscriptionPlanCard(
            title: '6 Months',
            price: 1798,
            icon: Icons.calendar_month,
            isPopular: true,
            onPressed: isLoading ? null : () => onSubscribe('6_months'),
            isLoading: isLoading,
          ),
          const SizedBox(height: 12),
          _SubscriptionPlanCard(
            title: '12 Months',
            price: 2998,
            icon: Icons.event_available,
            isBestValue: true,
            onPressed: isLoading ? null : () => onSubscribe('12_months'),
            isLoading: isLoading,
          ),
          const SizedBox(height: 12),
          _SubscriptionPlanCard(
            title: 'Lifetime',
            price: 4998,
            icon: Icons.all_inclusive,
            onPressed: isLoading ? null : () => onSubscribe('lifetime'),
            isLoading: isLoading,
            hasWorryFreeTag: true,
          ),
        ],
      ),
    );
  }
}

class _ExpandableFeatureCard extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const _ExpandableFeatureCard({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  @override
  State<_ExpandableFeatureCard> createState() => _ExpandableFeatureCardState();
}

class _ExpandableFeatureCardState extends State<_ExpandableFeatureCard> with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _controller;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
    _expandAnimation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: widget.color.withAlpha(77), width: 1), // 0.3 * 255 = 76.5, rounded to 77
      ),
      child: InkWell(
        onTap: _toggleExpanded,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: widget.color.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(widget.icon, size: 18, color: widget.color),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: Text(widget.title, style: const TextStyle(fontWeight: FontWeight.w500))),
                  RotationTransition(
                    turns: Tween(begin: 0.0, end: 0.25).animate(_expandAnimation),
                    child: Icon(
                      Icons.keyboard_arrow_right,
                      size: 18,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                    ),
                  ),
                ],
              ),
              ClipRect(
                child: SizeTransition(
                  sizeFactor: _expandAnimation,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12, left: 36),
                    child: Text(
                      widget.description,
                      style: TextStyle(
                        fontSize: 13,
                        color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
                      ), // 0.7 * 255 = 178.5, rounded to 179
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SubscriptionPlanCard extends StatelessWidget {
  final String title;
  final double price;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isPopular;
  final bool isBestValue;
  final bool hasWorryFreeTag;

  const _SubscriptionPlanCard({
    required this.title,
    required this.price,
    required this.icon,
    this.onPressed,
    required this.isLoading,
    this.isPopular = false,
    this.isBestValue = false,
    this.hasWorryFreeTag = false,
  });

  @override
  Widget build(BuildContext context) {
    final formattedPrice = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(price);
    final theme = Theme.of(context);

    // Calculate per month pricing for comparison
    double monthlyPrice = 0;
    String perMonthText = '';

    if (title == '3 Months') {
      monthlyPrice = price / 3;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    } else if (title == '6 Months') {
      monthlyPrice = price / 6;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    } else if (title == '12 Months') {
      monthlyPrice = price / 12;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isBestValue
                  ? theme.colorScheme.primary
                  : isPopular
                  ? theme.colorScheme.secondary
                  : Colors.transparent,
          width: isBestValue || isPopular ? 2 : 0,
        ),
      ),
      child: InkWell(
        onTap: isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(icon, size: 28, color: theme.colorScheme.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(title, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                        if (perMonthText.isNotEmpty)
                          Text(
                            perMonthText,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(179), // 0.7 * 255 = 178.5, rounded to 179
                            ),
                          ),
                      ],
                    ),
                  ),
                  Text(
                    formattedPrice,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              if (isBestValue || isPopular || hasWorryFreeTag)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (isBestValue)
                        _PlanBadge(label: 'BEST VALUE', color: theme.colorScheme.primary, icon: Icons.star),
                      if (isPopular)
                        _PlanBadge(label: 'POPULAR', color: theme.colorScheme.secondary, icon: Icons.thumb_up),
                      if (hasWorryFreeTag) _PlanBadge(label: 'WORRY FREE', color: Colors.teal, icon: Icons.security),
                    ],
                  ),
                ),
              if (isLoading)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2)),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class _PlanBadge extends StatelessWidget {
  final String label;
  final Color color;
  final IconData icon;

  const _PlanBadge({required this.label, required this.color, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(label, style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold, color: color)),
        ],
      ),
    );
  }
}

class _TransactionStatusDialog extends ConsumerWidget {
  final String transactionId;

  const _TransactionStatusDialog({required this.transactionId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return userAsync.when(
      data: (user) {
        if (user == null) {
          return const AlertDialog(
            title: Text('Error'),
            content: Text('User not found'),
            actions: [TextButton(onPressed: null, child: Text('Close'))],
          );
        }

        final transaction = user.transactions?[transactionId];
        if (transaction == null) {
          return const AlertDialog(
            title: Text('Verifying Payment'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Please complete the payment in the browser tab and wait while we verify your transaction.'),
                SizedBox(height: 8),
                Text('This may take a few moments.', style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic)),
              ],
            ),
          );
        }

        final isSuccess = transaction['status'] == 'success';
        final isRefunded = transaction['isRefunded'] == true;
        final isVoided = transaction['isVoided'] == true;

        String statusText;
        Color statusColor;
        if (isRefunded) {
          statusText = 'Refunded';
          statusColor = Colors.orange;
        } else if (isVoided) {
          statusText = 'Voided';
          statusColor = Colors.grey;
        } else if (isSuccess) {
          statusText = 'Success';
          statusColor = Colors.green;
        } else {
          statusText = 'Failed';
          statusColor = Colors.red;
        }

        return AlertDialog(
          title: Text(statusText),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(isSuccess ? Icons.check_circle : Icons.error, color: statusColor, size: 48),
              const SizedBox(height: 16),
              Text(transaction['message'] ?? statusText),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (isSuccess) {
                  // Optionally navigate to a success screen or home
                }
              },
              child: Text(isSuccess ? 'Continue' : 'Try Again'),
            ),
          ],
        );
      },
      loading:
          () => const AlertDialog(
            title: Text('Verifying Payment'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Please complete the payment in the browser tab and wait while we verify your transaction.'),
                SizedBox(height: 8),
                Text('This may take a few moments.', style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic)),
              ],
            ),
          ),
      error:
          (error, stack) => AlertDialog(
            title: const Text('Error'),
            content: Text('Error: $error'),
            actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Close'))],
          ),
    );
  }
}

class _AuthenticationPage extends StatelessWidget {
  final bool isLoading;
  final String? error;
  final VoidCallback onGoogleSignIn;
  final VoidCallback onAppleSignIn;

  const _AuthenticationPage({
    required this.isLoading,
    this.error,
    required this.onGoogleSignIn,
    required this.onAppleSignIn,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.account_circle, size: 48, color: theme.colorScheme.primary),
        const SizedBox(height: 24),
        const Text(
          'Sign In Required',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            'To make a purchase, you need to sign in with a Google or Apple account.',
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        */
/*
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 24),
          child: Text(
            'Your current progress and data will be preserved and linked to your account.',
            textAlign: TextAlign.center,
            style: TextStyle(fontStyle: FontStyle.italic, fontSize: 12),
          ),
        ),
*//*

        if (error != null) ...[
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Text(error!, style: TextStyle(color: theme.colorScheme.error), textAlign: TextAlign.center),
          ),
        ],
        const SizedBox(height: 32),
        _SocialButton(
          text: 'Continue with Google',
          icon: Icons.g_mobiledata,
          onPressed: isLoading ? null : onGoogleSignIn,
        ),
        const SizedBox(height: 16),
        _SocialButton(text: 'Continue with Apple', icon: Icons.apple, onPressed: isLoading ? null : onAppleSignIn),
        if (isLoading) ...[const SizedBox(height: 24), const CircularProgressIndicator()],
      ],
    );
  }
}

class _SocialButton extends StatelessWidget {
  const _SocialButton({required this.text, required this.icon, required this.onPressed});

  final String text;
  final IconData icon;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      constraints: const BoxConstraints(minWidth: 150, maxWidth: 300, maxHeight: 50),
      child: FilledButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(text),
        style: FilledButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
      ),
    );
  }
}

// This enum has been moved to upgrade_dialog.dart
enum UpgradeScenario { general, freeTestEnd, premiumQuestion, bookmarkFeature }

// This function has been moved to upgrade_dialog.dart
@Deprecated('Use the showUpgradeDialog function from upgrade_dialog.dart instead.')
void showUpgradeDialog(
  BuildContext context,
  WidgetRef ref,
  String message, {
  UpgradeScenario scenario = UpgradeScenario.general,
  VoidCallback? onDismiss,
  int? remainingQuestions,
  VoidCallback? onFinishTest,
}) {
  // Use the new implementation
  new_dialog.showUpgradeDialog(
    context,
    ref,
    message,
    scenario: new_dialog.UpgradeScenario.values[scenario.index],
    onDismiss: onDismiss,
    remainingQuestions: remainingQuestions,
    onFinishTest: onFinishTest,
  );
}
*/
