import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';

class GlitchyTextTransition extends StatefulWidget {
  final String initialText;
  final String finalText;
  final double fontSize;
  final FontWeight fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int firstPhaseDuration;
  final bool enableSecondPhase;
  final int minSecondPhaseInterval;
  final int maxSecondPhaseInterval;

  const GlitchyTextTransition({
    super.key,
    required this.initialText,
    required this.finalText,
    required this.fontSize,
    this.fontWeight = FontWeight.bold,
    this.color,
    this.textAlign,
    this.firstPhaseDuration = 500,
    this.enableSecondPhase = true,
    this.minSecondPhaseInterval = 3000, // 3 seconds minimum between glitches
    this.maxSecondPhaseInterval = 15000, // 6 seconds maximum between glitches
  });

  @override
  State<GlitchyTextTransition> createState() => GlitchyTextTransitionState();
}

class GlitchyTextTransitionState extends State<GlitchyTextTransition> with SingleTickerProviderStateMixin {
  late String currentText;
  late Timer phaseOneTimer;
  Timer? phaseTwoTimer;
  final Random random = Random();
  bool inFirstPhase = true;
  bool isGlitching = true;
  late AnimationController controller;
  late Animation<double> glitchAnimation;

  // Timeline for phase one glitch events (milliseconds)
  final List<int> phaseOneEvents = [100, 300]; // [300, 800, 1300, 1700, 2200];

  @override
  void initState() {
    super.initState();
    currentText = widget.initialText;

    // Animation controller for glitch effect
    controller = AnimationController(vsync: this, duration: Duration(milliseconds: widget.firstPhaseDuration));

    glitchAnimation = Tween<double>(begin: 0, end: 1).animate(controller)..addListener(() {
      if (mounted) {
        setState(() {}); // Trigger rebuild when animation ticks
      }
    });

    startPhaseOne();
    controller.forward();
  }

  @override
  void dispose() {
    phaseOneTimer.cancel();
    phaseTwoTimer?.cancel();
    controller.dispose();
    super.dispose();
  }

  void startPhaseOne() {
    // Start the initial timer for phase one
    int tickCount = 0;
    int currentEventIndex = 0;

    phaseOneTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      tickCount += 100;

      // Check if we're at a glitch event point
      if (currentEventIndex < phaseOneEvents.length && tickCount >= phaseOneEvents[currentEventIndex]) {
        // Trigger a glitch
        triggerPhaseOneGlitch();
        currentEventIndex++;
      }

      // End phase one and set final text
      if (tickCount >= widget.firstPhaseDuration) {
        setState(() {
          currentText = widget.finalText;
          isGlitching = false;
          inFirstPhase = false;
        });
        timer.cancel();

        // Start phase two if enabled
        if (widget.enableSecondPhase && mounted) {
          startPhaseTwo();
        }
      }
    });
  }

  void startPhaseTwo() {
    // Schedule the first random glitch
    scheduleNextPhaseTwoGlitch();
  }

  void scheduleNextPhaseTwoGlitch() {
    if (!mounted) return;

    // Random interval between min and max
    int nextInterval =
        widget.minSecondPhaseInterval + random.nextInt(widget.maxSecondPhaseInterval - widget.minSecondPhaseInterval);

    phaseTwoTimer = Timer(Duration(milliseconds: nextInterval), () {
      if (!mounted) return;

      // Trigger a brief glitch back to the initial text
      triggerPhaseTwoGlitch();

      // Schedule the next glitch
      scheduleNextPhaseTwoGlitch();
    });
  }

  void triggerPhaseOneGlitch() {
    // Create a short sequence of rapid text changes
    int glitchSteps = 3 + random.nextInt(3); // 3-5 glitch steps
    int glitchDuration = 250; // total duration of this glitch sequence
    int stepDuration = (glitchDuration / glitchSteps).floor();

    int glitchStep = 0;
    Timer? glitchTimer;
    glitchTimer = Timer.periodic(Duration(milliseconds: stepDuration), (timer) {
      if (!mounted) {
        glitchTimer?.cancel();
        return;
      }

      if (glitchStep < glitchSteps - 1) {
        // During glitching, alternate between initial and final text
        setState(() {
          // 40% chance to show final text during glitch
          if (random.nextDouble() < 0.4) {
            currentText = widget.finalText;
          } else {
            currentText = widget.initialText;
          }
        });
      } else {
        // Last step - restore initial text until the end of the sequence
        setState(() {
          currentText = widget.initialText;
        });
        glitchTimer?.cancel();
      }
      glitchStep++;
    });
  }

  void triggerPhaseTwoGlitch() {
    if (!mounted) return;

    // Set the glitching flag to add visual effects
    setState(() {
      isGlitching = true;
    });

    // Shorter, simpler glitch sequence for phase two
    int glitchSteps = 2 + random.nextInt(2); // 2-3 glitch steps
    int glitchDuration = 200; // shorter total duration
    int stepDuration = (glitchDuration / glitchSteps).floor();

    int glitchStep = 0;
    Timer? glitchTimer;
    glitchTimer = Timer.periodic(Duration(milliseconds: stepDuration), (timer) {
      if (!mounted) {
        glitchTimer?.cancel();
        return;
      }

      setState(() {
        // First half of steps, show the initial text
        if (glitchStep < glitchSteps / 2) {
          currentText = widget.initialText;
        } else {
          // Second half, go back to final text
          currentText = widget.finalText;
        }
      });

      glitchStep++;

      if (glitchStep >= glitchSteps) {
        // End of this glitch
        setState(() {
          currentText = widget.finalText;
          isGlitching = false;
        });
        glitchTimer?.cancel();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Calculate displacement for the glitch effect
    final double offsetX = isGlitching && shouldDisplace() ? (random.nextDouble() * 6 - 3) : 0;
    final double offsetY = isGlitching && shouldDisplace() ? (random.nextDouble() * 4 - 2) : 0;

    return Stack(
      children: [
        // Main text with slight displacement during glitching
        Transform.translate(
          offset: Offset(offsetX, offsetY),
          child: Text(
            currentText,
            style: TextStyle(fontSize: widget.fontSize, fontWeight: widget.fontWeight, color: widget.color),
            textAlign: widget.textAlign,
          ),
        ),

        // RGB split effects only visible during glitching
        if (isGlitching && shouldShowRGBSplit()) ...[
          // Red channel offset
          Positioned(
            left: 3 + (random.nextDouble() * 2),
            child: Text(
              currentText,
              style: TextStyle(
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                color: Colors.red.withAlpha(128), // 0.5 opacity (128/255)
              ),
              textAlign: widget.textAlign,
            ),
          ),
          // Blue/Cyan channel offset
          Positioned(
            left: -3 - (random.nextDouble() * 2),
            child: Text(
              currentText,
              style: TextStyle(
                fontSize: widget.fontSize,
                fontWeight: widget.fontWeight,
                color: Colors.cyan.withAlpha(128), // 0.5 opacity (128/255)
              ),
              textAlign: widget.textAlign,
            ),
          ),
        ],
      ],
    );
  }

  bool shouldShowRGBSplit() {
    if (inFirstPhase) {
      // Phase one: Show color splitting at specific animation points
      final animPoint = controller.value;
      return (animPoint > 0.3 && animPoint < 0.4) ||
          (animPoint > 0.6 && animPoint < 0.7) ||
          (animPoint > 0.85 && animPoint < 0.95);
    } else {
      // Phase two: Always show color splitting when glitching
      return true;
    }
  }

  bool shouldDisplace() {
    if (inFirstPhase) {
      // Phase one: Show displacement at specific animation points
      final animPoint = controller.value;
      return (animPoint > 0.25 && animPoint < 0.45) || (animPoint > 0.55 && animPoint < 0.75) || (animPoint > 0.85);
    } else {
      // Phase two: Always show displacement when glitching
      return true;
    }
  }
}
