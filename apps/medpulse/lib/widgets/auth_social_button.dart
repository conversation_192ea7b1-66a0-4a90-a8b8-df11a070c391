import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';

/// A social authentication button that checks for legal document acceptance after sign-in.
class AuthSocialButton extends ConsumerWidget {
  /// Creates a social authentication button.
  const AuthSocialButton({
    super.key,
    required this.text,
    required this.icon,
    required this.onPressed,
  });

  /// The text to display on the button.
  final String text;

  /// The icon to display on the button.
  final IconData icon;

  /// The callback to execute when the button is pressed.
  final Future<void> Function()? onPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: double.maxFinite,
      constraints: const BoxConstraints(minWidth: 150, maxWidth: 400, maxHeight: 50),
      child: FilledButton.icon(
        onPressed: onPressed == null
            ? null
            : () async {
                // Execute the sign-in operation
                await onPressed!();

                // If the context is still mounted, check for legal document acceptance
                if (context.mounted) {
                  final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
                  if (!hasAccepted) {
                    // Show the legal documents dialog
                    await LegalDocumentsDialog.show(context: context);
                  }
                }
              },
        icon: Icon(icon),
        label: Text(text),
        style: FilledButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
      ),
    );
  }
}
