import 'package:flutter/material.dart';

class WideButton extends StatelessWidget {
  const WideButton({required this.child, this.onPressed, super.key});

  final Widget child;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      constraints: const BoxConstraints(minWidth: 100, maxWidth: 300, maxHeight: 50),
      child: FilledButton(
        style: FilledButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        onPressed: onPressed,
        child: child, // ?? Text(buttonText ?? "", style: theme.elevatedButtonTheme.style?.textStyle?.resolve({})),
      ),
    );
  }
}
