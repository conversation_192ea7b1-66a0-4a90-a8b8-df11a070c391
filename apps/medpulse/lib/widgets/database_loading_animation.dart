import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';

class DatabaseLoadingAnimation extends StatefulWidget {
  const DatabaseLoadingAnimation({super.key});

  @override
  DatabaseLoadingAnimationState createState() => DatabaseLoadingAnimationState();
}

class DatabaseLoadingAnimationState extends State<DatabaseLoadingAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _glitchAnimation;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();

    // Fast animation loop (2 seconds total)
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _glitchAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final accentColor = theme.colorScheme.secondary;

    return AnimatedBuilder(
      animation: _glitchAnimation,
      builder: (context, child) {
        // Different timings for various glitch elements
        final glitchPhase = (_glitchAnimation.value * 10).floor() % 10;
        final textGlitchIntensity = _shouldGlitch(0.7) ? _random.nextDouble() * 0.1 : 0.0;
        final colorGlitchAlpha = _shouldGlitch(0.4) ? 0.3 + (_random.nextDouble() * 0.3) : 0.0;
        final scanlineOpacity = 0.1 + (_getAnimationValue(0.0, 0.15) * 0.2);

        return Stack(
          children: [
            // Background
            Container(
              width: size.width,
              height: size.height,
              color: Colors.black,
            ),

            // Scanlines
/*
            Opacity(
              opacity: scanlineOpacity,
              child: Container(
                width: size.width,
                height: size.height,
                foregroundDecoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/scanlines.png'),
                    repeat: ImageRepeat.repeat,
                    opacity: 0.07,
                  ),
                ),
              ),
            ),
*/

            // RGB split/glitch effect layers when glitching
            if (colorGlitchAlpha > 0)
              ..._buildColorShiftLayers(size, primaryColor, accentColor, colorGlitchAlpha),

            // Horizontal glitch bars
            ..._buildGlitchBars(size, primaryColor, accentColor),

            // Center content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Glitchy database icon
                  _GlitchyIcon(
                    icon: Icons.storage_rounded,
                    size: 70,
                    color: primaryColor,
                    glitchFactor: _shouldGlitch(0.3) ? _random.nextDouble() * 5.0 : 0.0,
                  ),

                  SizedBox(height: 30),

                  // Glitchy text
                  _GlitchyText(
                    text: 'INITIALIZING',
                    glitchFactor: textGlitchIntensity,
                    baseColor: Colors.white,
                    glitchColor: primaryColor,
                  ),
                ],
              ),
            ),

            // CRT effect overlay
            Container(
              width: size.width,
              height: size.height,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.center,
                  radius: 1.5,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.4),
                  ],
                  stops: const [0.6, 1.0],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  List<Widget> _buildColorShiftLayers(Size size, Color primaryColor, Color accentColor, double alpha) {
    // Calculate shift amounts based on animation
    final shiftX = (_random.nextDouble() * 10) - 5.0;
    final shiftY = (_random.nextDouble() * 6) - 3.0;

    return [
      // Red shift layer
      Positioned(
        left: shiftX,
        top: shiftY,
        child: Opacity(
          opacity: alpha * 0.7,
          child: Container(
            width: size.width,
            height: size.height,
            color: Colors.red.withOpacity(0.2),
            child: Center(
              child: Icon(
                Icons.storage_rounded,
                size: 70,
                color: Colors.red.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),

      // Cyan shift layer
      Positioned(
        left: -shiftX,
        top: -shiftY,
        child: Opacity(
          opacity: alpha * 0.7,
          child: Container(
            width: size.width,
            height: size.height,
            color: Colors.cyan.withOpacity(0.2),
            child: Center(
              child: Icon(
                Icons.storage_rounded,
                size: 70,
                color: Colors.cyan.withOpacity(0.5),
              ),
            ),
          ),
        ),
      ),
    ];
  }

  List<Widget> _buildGlitchBars(Size size, Color primaryColor, Color accentColor) {
    final bars = <Widget>[];

    // Only show glitch bars sometimes
    if (_shouldGlitch(0.3)) {
      final barCount = 1 + _random.nextInt(3);

      for (var i = 0; i < barCount; i++) {
        final barHeight = 5.0 + _random.nextDouble() * 20;
        final topPosition = _random.nextDouble() * size.height;
        final color = _random.nextBool() ? primaryColor : accentColor;
        final opacity = 0.3 + _random.nextDouble() * 0.5;
        final xOffset = (_random.nextDouble() * size.width * 0.3) - (size.width * 0.15);

        bars.add(
          Positioned(
            left: xOffset,
            top: topPosition,
            child: Container(
              width: size.width,
              height: barHeight,
              color: color.withOpacity(opacity),
            ),
          ),
        );
      }
    }

    return bars;
  }

  // Get animation value at specific phase
  double _getAnimationValue(double start, double end) {
    final normalizedValue = (_glitchAnimation.value - start) / (end - start);
    return normalizedValue.clamp(0.0, 1.0);
  }

  // Decide whether to apply glitch effect based on probability
  bool _shouldGlitch(double probability) {
    // More glitches at specific animation points
    final cyclePoint = _glitchAnimation.value % 1.0;
    final glitchPronePhase = (cyclePoint < 0.1) ||
        (cyclePoint > 0.4 && cyclePoint < 0.5) ||
        (cyclePoint > 0.75 && cyclePoint < 0.85);

    final effectiveProbability = glitchPronePhase ? probability * 2 : probability * 0.5;
    return _random.nextDouble() < effectiveProbability;
  }
}

class _GlitchyText extends StatelessWidget {
  final String text;
  final double glitchFactor;
  final Color baseColor;
  final Color glitchColor;

  const _GlitchyText({
    Key? key,
    required this.text,
    required this.glitchFactor,
    required this.baseColor,
    required this.glitchColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final random = Random();

    // Apply glitch effect to text if glitchFactor > 0
    String displayText = text;
    if (glitchFactor > 0) {
      final charArray = text.split('');
      for (int i = 0; i < charArray.length; i++) {
        if (random.nextDouble() < glitchFactor) {
          // Replace random chars with glitchy alternatives
          final glitchOptions = ['#', '?', '/', '_', '!', '*', '\$', '%'];
          charArray[i] = glitchOptions[random.nextInt(glitchOptions.length)];
        }
      }
      displayText = charArray.join();
    }

    // Create text with shadows for glitch effect
    return Stack(
      children: [
        // Text shadow layers for depth
        if (glitchFactor > 0)
          Positioned(
            left: random.nextDouble() * glitchFactor * 10,
            child: Text(
              displayText,
              style: TextStyle(
                color: glitchColor.withOpacity(0.7),
                fontSize: 24,
                fontWeight: FontWeight.bold,
                letterSpacing: 2.0,
              ),
            ),
          ),

        // Main text
        Text(
          displayText,
          style: TextStyle(
            color: baseColor,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            letterSpacing: 2.0,
            shadows: [
              Shadow(
                color: glitchColor.withOpacity(0.8),
                blurRadius: 5,
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _GlitchyIcon extends StatelessWidget {
  final IconData icon;
  final double size;
  final Color color;
  final double glitchFactor;

  const _GlitchyIcon({
    Key? key,
    required this.icon,
    required this.size,
    required this.color,
    required this.glitchFactor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final random = Random();

    return Stack(
      alignment: Alignment.center,
      children: [
        // Glitch shadow layers
        if (glitchFactor > 0) ...[
          Positioned(
            left: glitchFactor * random.nextDouble() * 4,
            top: glitchFactor * random.nextDouble() * 4,
            child: Icon(
              icon,
              size: size,
              color: Colors.red.withOpacity(0.5),
            ),
          ),
          Positioned(
            left: -glitchFactor * random.nextDouble() * 3,
            top: -glitchFactor * random.nextDouble() * 3,
            child: Icon(
              icon,
              size: size,
              color: Colors.cyan.withOpacity(0.5),
            ),
          ),
        ],

        // Main icon
        Icon(
          icon,
          size: size,
          color: color,
        ),

        // Optional: Additional glitch layer on extreme glitches
        if (glitchFactor > 3.0)
          Positioned(
            left: glitchFactor * (random.nextDouble() - 0.5) * 2,
            top: glitchFactor * (random.nextDouble() - 0.5) * 2,
            child: Icon(
              icon,
              size: size * (0.8 + random.nextDouble() * 0.4),
              color: Colors.white.withOpacity(random.nextDouble() * 0.4),
            ),
          ),
      ],
    );
  }
}