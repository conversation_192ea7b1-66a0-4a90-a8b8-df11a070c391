import 'package:flutter/material.dart';

class ConstrainedWidthAppBar extends StatelessWidget implements PreferredSizeWidget {
  final double maxWidth;
  final PreferredSizeWidget appBar;

  const ConstrainedWidthAppBar({
    super.key,
    required this.appBar,
    this.maxWidth = 600,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: appBar,
      ),
    );
  }

  @override
  Size get preferredSize => appBar.preferredSize;
}
