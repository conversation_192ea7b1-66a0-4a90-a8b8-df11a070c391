import 'package:flutter/material.dart';
import 'package:entities/test_progress.dart';

class TestModeSelectionDialog extends StatelessWidget {
  const TestModeSelectionDialog({super.key});

  static Future<TestModeEnum?> show(BuildContext context) {
    return showDialog<TestModeEnum>(
      context: context,
      builder: (context) => const TestModeSelectionDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Test Mode'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _ModeOption(
            mode: TestModeEnum.test,
            title: 'Test Mode',
            description: 'Traditional test experience with final results',
            icon: Icons.quiz_outlined,
          ),
          const SizedBox(height: 12),
          _ModeOption(
            mode: TestModeEnum.feedback,
            title: 'Feedback Mode',
            description: 'Instant feedback with multiple attempts and global stats',
            icon: Icons.psychology_outlined,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}

class _ModeOption extends StatelessWidget {
  final TestModeEnum mode;
  final String title;
  final String description;
  final IconData icon;

  const _ModeOption({
    required this.mode,
    required this.title,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Navigator.of(context).pop(mode),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 32,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
