import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:providers/common.dart';
import 'package:storage_service/storage_service.dart';

class CachedImage extends StatefulWidget {
  final String storagePath;
  final BoxConstraints constraints;
  final BoxFit fit;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final double? imageWidth;
  final double? imageHeight;
  final Duration transitionDuration;
  final Widget Function(Widget child, Animation<double> animation)? transitionBuilder;

  const CachedImage({
    super.key,
    required this.storagePath,
    this.constraints = const BoxConstraints(minWidth: 100, minHeight: 100, maxWidth: 500, maxHeight: 500),
    this.fit = BoxFit.contain,
    this.imageWidth,
    this.imageHeight,
    this.loadingWidget,
    this.errorWidget,
    this.transitionDuration = const Duration(milliseconds: 300),
    this.transitionBuilder,
  });

  @override
  State<CachedImage> createState() => _CachedImageState();
}

class _CachedImageState extends State<CachedImage> {
  final StorageService _storageService = StorageService();
  late Future<Uint8List> _imageFuture;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.storagePath != widget.storagePath) {
      _loadImage();
    }
  }

  void _loadImage() {
    _imageFuture = _fetchImage();
  }

  Future<Uint8List> _fetchImage() async {
    try {
      final imageData = await _storageService.getCachedFile(widget.storagePath);
      return imageData;
    } catch (e) {
      dbgPrint('CachedImage: Error loading image ${widget.storagePath}: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: widget.constraints,
      child: FutureBuilder<Uint8List>(
        future: _imageFuture,
        builder: (context, snapshot) {
          Widget child;
          if (snapshot.connectionState == ConnectionState.waiting) {
            child = _buildLoadingWidget();
          } else if (snapshot.hasError) {
            child = _buildErrorWidget(snapshot.error);
          } else if (snapshot.hasData) {
            child = Container(
              key: ValueKey('image-${widget.storagePath}'),
              constraints: widget.constraints,
              child: FittedBox(
                fit: widget.fit,
                child: Image.memory(
                  snapshot.data!,
                  width: widget.imageWidth,
                  height: widget.imageHeight,
                  errorBuilder: (context, error, stackTrace) => _buildErrorWidget(error),
                ),
              ),
            );
          } else {
            child = _buildErrorWidget('No image data');
          }

          return AnimatedSwitcher(
            duration: widget.transitionDuration,
            transitionBuilder: widget.transitionBuilder ?? _defaultTransitionBuilder,
            child: child,
          );
        },
      ),
    );
  }

  Widget _defaultTransitionBuilder(Widget child, Animation<double> animation) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  Widget _buildLoadingWidget() {
    return widget.loadingWidget ??
        Container(
          key: ValueKey('img-loading-${widget.storagePath}'),
          constraints: widget.constraints,
          child: FittedBox(
            fit: widget.fit,
            child: ShimmerImagePlaceholder(
              width: widget.imageWidth,
              height: widget.imageHeight,
            ),
          ),
        );
  }

  Widget _buildErrorWidget(dynamic error) {
    return widget.errorWidget ??
        Container(
          key: ValueKey('img-error-${widget.storagePath}'),
          constraints: widget.constraints,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withAlpha(77)), // 0.3 opacity (77/255)
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Icon(
              Icons.broken_image,
              size: 24,
              color: Colors.grey,
            ),
          ),
        );
  }
}

/// A shimmer effect placeholder for images that are loading
class ShimmerImagePlaceholder extends StatefulWidget {
  final double? width;
  final double? height;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerImagePlaceholder({
    super.key,
    this.width,
    this.height,
    this.baseColor = const Color(0xFFEEEEEE),
    this.highlightColor = const Color(0xFFFAFAFA),
  });

  @override
  State<ShimmerImagePlaceholder> createState() => _ShimmerImagePlaceholderState();
}

class _ShimmerImagePlaceholderState extends State<ShimmerImagePlaceholder> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();

    _animation = Tween<double>(begin: -2.0, end: 2.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.0),
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    widget.baseColor,
                    widget.highlightColor,
                    widget.baseColor,
                  ],
                  stops: [
                    0.0,
                    _animation.value.abs() / 2 + 0.5,
                    1.0,
                  ],
                  transform: GradientRotation(_animation.value),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}