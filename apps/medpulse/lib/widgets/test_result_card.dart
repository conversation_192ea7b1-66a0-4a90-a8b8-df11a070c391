import 'package:flutter/material.dart';
import 'package:entities/test_result.dart';
import 'package:intl/intl.dart';
import 'package:medpulse/theme/app_theme.dart';

/// Helper function to get the appropriate color based on score percentage
Color getScoreColor(BuildContext context, double scorePercentage) {
  final appColors = getAppColor(context);

  if (scorePercentage >= 90) {
    return appColors.excellentScore;
  } else if (scorePercentage >= 70) {
    return appColors.goodScore;
  } else if (scorePercentage >= 50) {
    return appColors.averageScore;
  } else {
    return appColors.poorScore;
  }
}

/// A widget that displays a single test result item with an improved design
class TestResultCard extends StatelessWidget {
  final TestResult result;
  final VoidCallback onTap;
  final bool showDate;

  const TestResultCard({super.key, required this.result, required this.onTap, this.showDate = true});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Create the card border color once
    final borderColor = colorScheme.outlineVariant.withAlpha(128); // 0.5 opacity (128/255)

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0, // Flatter design
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // More rounded corners
        side: BorderSide(color: borderColor, width: 1),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Score circle with improved design
              ScoreIndicator(scorePercentage: result.scorePercentage),
              const SizedBox(width: 16),

              // Result details with improved typography
              Expanded(child: TestResultDetails(result: result, showDate: showDate)),
            ],
          ),
        ),
      ),
    );
  }
}

/// Widget that displays the test result details
class TestResultDetails extends StatelessWidget {
  final TestResult result;
  final bool showDate;

  const TestResultDetails({super.key, required this.result, required this.showDate});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Test name with better semantic heading and free/premium badge
        Row(
          children: [
            Expanded(
              child: Text(
                result.testName,
                style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.onSurface),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 7.0, left: 8.0),
              child: result.free ? const FreeTag() : const PremiumTag(),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Result details in a more organized layout
        Row(
          children: [
            // Message chip
            ResultStatusChip(scorePercentage: result.scorePercentage, message: result.getResultMessage()),
            const SizedBox(width: 8),

            // Score text
            Text(
              '${result.correctAnswers} / ${result.questionCount}',
              style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500, color: colorScheme.onSurfaceVariant),
            ),
          ],
        ),

        // Date with better formatting
        if (showDate) ...[const SizedBox(height: 6), DateTimeDisplay(timestamp: result.timestamp)],
      ],
    );
  }
}

/// A dedicated widget for the score indicator with single color
class ScoreIndicator extends StatelessWidget {
  final double scorePercentage;

  // Using the shared kScoreColors map

  const ScoreIndicator({super.key, required this.scorePercentage});

  @override
  Widget build(BuildContext context) {
    final appColors = getAppColor(context);
    final scoreColor = getScoreColor(context, scorePercentage);

    // Pre-calculate the colors for better performance
    final backgroundColor = scoreColor.withAlpha(51); // 0.2 opacity (51/255)
    final borderColor = scoreColor;

    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
        border: Border.all(color: borderColor, width: 2),
      ),
      child: Center(
        child: Text(
          '${scorePercentage.toInt()}%',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: appColors.scoreIndicatorTextColor,
          ),
        ),
      ),
    );
  }

  // Using the shared getScoreColor function
}

/// A dedicated widget for the result status chip
class ResultStatusChip extends StatelessWidget {
  final double scorePercentage;
  final String message;

  // Using the shared kScoreColors map

  const ResultStatusChip({super.key, required this.scorePercentage, required this.message});

  @override
  Widget build(BuildContext context) {
    final scoreColor = getScoreColor(context, scorePercentage);

    // Pre-calculate colors for better performance
    final backgroundColor = scoreColor.withAlpha(26); // 0.1 opacity (26/255)
    final borderColor = scoreColor.withAlpha(128); // 0.5 opacity (128/255)

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor, width: 1),
      ),
      child: Text(message, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: scoreColor)),
    );
  }

  // Using the shared getScoreColor function
}

/// A dedicated widget for displaying the date and time
class DateTimeDisplay extends StatelessWidget {
  final DateTime timestamp;
  static final dateFormat = DateFormat('MMM d, yyyy');
  static final timeFormat = DateFormat('h:mm a');

  const DateTimeDisplay({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textColor = theme.colorScheme.onSurfaceVariant.withAlpha(179); // 0.7 opacity (179/255)

    return Row(
      children: [
        Icon(Icons.access_time_rounded, size: 12, color: textColor),
        const SizedBox(width: 4),
        Text(
          '${dateFormat.format(timestamp)} · ${timeFormat.format(timestamp)}',
          style: theme.textTheme.bodySmall?.copyWith(color: textColor),
        ),
      ],
    );
  }
}

/// A widget that shows the free tag
class FreeTag extends StatelessWidget {
  const FreeTag({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = getAppColor(context);

    // Create text style using theme colors
    final textStyle = TextStyle(
      color: appColors.freeTagTextColor,
      fontSize: 10,
      fontWeight: FontWeight.w800,
      letterSpacing: 0.5,
    );

    return Container(
      height: 22,
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(color: appColors.freeTagColor, borderRadius: BorderRadius.circular(12)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.check_circle, size: 12, color: appColors.freeTagTextColor),
          const SizedBox(width: 4),
          Text('FREE', style: textStyle),
        ],
      ),
    );
  }
}

/// A widget that shows the premium tag
class PremiumTag extends StatelessWidget {
  const PremiumTag({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final gold = colorScheme.tertiary;
    final textColor = colorScheme.onTertiary;

    // Create text style using theme colors
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 10,
      fontWeight: FontWeight.w800,
      letterSpacing: 0.5,
    );

    return Container(
      height: 22,
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(color: gold, borderRadius: BorderRadius.circular(12)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.star, size: 12, color: textColor),
          const SizedBox(width: 4),
          Text('PREMIUM', style: textStyle),
        ],
      ),
    );
  }
}
