import 'package:flutter/material.dart';
import 'package:providers/common.dart';

// Import for web platform views
import 'dart:ui_web' as ui_web;
import 'package:web/web.dart' as web;
import 'dart:js_interop';
import 'dart:js_interop_unsafe';

// This is a platform-agnostic interface for payment iframes
/*
abstract class PaymentIframe extends StatefulWidget {
  final String paymentUrl;
  final Function(bool success, String? message) onPaymentComplete;

  const PaymentIframe({super.key, required this.paymentUrl, required this.onPaymentComplete});
}
*/

// Factory to create the appropriate implementation
class PaymentIframeFactory {
  static Widget create({
    required String paymentUrl,
    required Function(bool success, String? message) onPaymentComplete,
  }) {
    // Import the appropriate implementation based on the platform
    // For web, we'll use our WebIframeWidget
    return _WebImplementation(paymentUrl: paymentUrl, onPaymentComplete: onPaymentComplete);
  }
}

// Web implementation directly in this file for simplicity
class _WebImplementation extends StatefulWidget {
  final String paymentUrl;
  final Function(bool success, String? message) onPaymentComplete;

  const _WebImplementation({required this.paymentUrl, required this.onPaymentComplete});

  @override
  State<_WebImplementation> createState() => _WebImplementationState();
}

class _WebImplementationState extends State<_WebImplementation> {
  bool _isLoading = true;
  late String iFrameId;

  @override
  void initState() {
    super.initState();
    iFrameId =_buildIframe();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // The iframe
        HtmlElementView(viewType: iFrameId),
        // Loading indicator
        if (_isLoading) const Center(child: CircularProgressIndicator()),
      ],
    );
  }

  String _buildIframe() {
    // Create a unique ID for this iframe
    iFrameId = 'payment-iframe-${DateTime.now().millisecondsSinceEpoch}';

    // Register the iframe in the DOM
    ui_web.platformViewRegistry.registerViewFactory(iFrameId, (int viewId) {
      // Create an iframe element using the web package
      final iframe =
          web.HTMLIFrameElement()
            ..src = widget.paymentUrl
            ..style.border = 'none'
            ..style.height = '100%'
            ..style.width = '100%';

      // Set allowFullscreen property using js_interop
      final jsIframe = iframe as JSObject;
      jsIframe['allowFullscreen'] = true.toJS;

      // Listen for load event to hide loading indicator
      final loadCallback =
          ((JSAny event) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          }).toJS;
      iframe.addEventListener('load', loadCallback);

      // Listen for messages from the iframe
      final messageCallback =
          ((JSAny event) {
            final dartEvent = event.dartify() as Map<String, dynamic>;
            _handleMessage(dartEvent['data']);
          }).toJS;
      web.window.addEventListener('message', messageCallback);

      return iframe;
    });

    dbgPrint('Registered iframe with ID: $iFrameId');
    return iFrameId;
  }

  void _handleMessage(dynamic data) {
    // Debug output to understand the actual type we're receiving
    dbgPrint('Payment message data type: ${data.runtimeType}');
    dbgPrint('Payment message data: $data');

    try {
      // Assume we'll always get a Map
      if (data is Map) {
        final status = data['status'];
        dbgPrint('Payment status: $status');

        if (status == 'success' || status == 'approved') {
          widget.onPaymentComplete(true, 'Payment successful');
        } else if (status == 'fail' || status == 'declined') {
          widget.onPaymentComplete(false, 'Payment failed');
        } else {
          // Unknown status
          dbgPrint('Unknown payment status: $status');
          widget.onPaymentComplete(false, 'Payment status unknown: $status');
        }
      } else {
        // If we don't get a Map, log the error and throw
        final errorMsg = 'Expected Map in _handleMessage but got ${data.runtimeType}';
        dbgPrint(errorMsg);
        throw TypeError();
      }
    } catch (e) {
      dbgPrint('Error parsing message from iframe: $e');
      widget.onPaymentComplete(false, 'Error processing payment response');
    }
  }
}
