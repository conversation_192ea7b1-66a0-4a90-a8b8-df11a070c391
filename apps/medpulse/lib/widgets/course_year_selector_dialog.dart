import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/course_entity.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:medpulse/providers/courses_provider.dart';
import 'package:medpulse/providers/user_provider.dart';

part 'course_year_selector_dialog.g.dart';

/// Provider for selected course in the dialog
@riverpod
class SelectedDialogCourse extends _$SelectedDialogCourse {
  @override
  CourseEntity? build() => null;

  void setCourse(CourseEntity? course) => state = course;
}

/// Provider for selected year in the dialog
@riverpod
class SelectedDialogYear extends _$SelectedDialogYear {
  @override
  String? build() => null;

  void setYear(String? year) => state = year;
}

/// Provider for available years in the selected course
@riverpod
List<String> availableDialogYears(Ref ref) {
  final selectedCourse = ref.watch(selectedDialogCourseProvider);
  if (selectedCourse == null) return [];

  final years = selectedCourse.years.keys.toList();
  years.sort(); // Sort the years alphabetically
  return years;
}

/// Enum to represent the different pages in the course selection dialog
enum CourseSelectionPage { subscribed, custom }

/// Shows a dialog for selecting course and year
/// Returns a Map with 'courseId' and 'yearId' keys if selection is made, null if canceled
Future<Map<String, String>?> showCourseYearSelectorDialog(BuildContext context, WidgetRef ref) async {
  return showDialog<Map<String, String>?>(context: context, builder: (context) => const CourseYearSelectorDialog());
}

/// Dialog widget for selecting course and year
class CourseYearSelectorDialog extends ConsumerStatefulWidget {
  const CourseYearSelectorDialog({super.key});

  @override
  ConsumerState<CourseYearSelectorDialog> createState() => _CourseYearSelectorDialogState();
}

class _CourseYearSelectorDialogState extends ConsumerState<CourseYearSelectorDialog> {
  final PageController _pageController = PageController();
  CourseSelectionPage _currentPage = CourseSelectionPage.subscribed;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _navigateToPage(CourseSelectionPage page) {
    _pageController.animateToPage(page.index, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    setState(() {
      _currentPage = page;
    });
  }

  void _handleSubscribedSelection(String courseId, String yearId) {
    Navigator.of(context).pop({'courseId': courseId, 'yearId': yearId});
  }

  void _handleCustomSelection() {
    final selectedCourse = ref.read(selectedDialogCourseProvider);
    final selectedYear = ref.read(selectedDialogYearProvider);

    if (selectedCourse != null && selectedYear != null) {
      Navigator.of(context).pop({'courseId': selectedCourse.id, 'yearId': selectedYear});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Dialog header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(child: Text('Select Course and Year', style: Theme.of(context).textTheme.titleLarge)),
                  IconButton(icon: const Icon(Icons.close), onPressed: () => Navigator.of(context).pop()),
                ],
              ),
            ),

            // Tab buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  Expanded(
                    child: _TabButton(
                      label: 'My Subscriptions',
                      isSelected: _currentPage == CourseSelectionPage.subscribed,
                      onTap: () => _navigateToPage(CourseSelectionPage.subscribed),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _TabButton(
                      label: 'Choose Course/Year',
                      isSelected: _currentPage == CourseSelectionPage.custom,
                      onTap: () => _navigateToPage(CourseSelectionPage.custom),
                    ),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Page content
            Expanded(
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // Subscribed courses page
                  _SubscribedCoursesPage(
                    onSubscriptionSelected: _handleSubscribedSelection,
                    onCustomSelection: () => _navigateToPage(CourseSelectionPage.custom),
                  ),

                  // Custom selection page
                  _CustomSelectionPage(onSelectionComplete: _handleCustomSelection),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Tab button widget
class _TabButton extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _TabButton({required this.label, required this.isSelected, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).colorScheme.primaryContainer : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            color:
                isSelected ? Theme.of(context).colorScheme.onPrimaryContainer : Theme.of(context).colorScheme.onSurface,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}

/// Page showing subscribed courses
class _SubscribedCoursesPage extends ConsumerWidget {
  final Function(String, String) onSubscriptionSelected;
  final VoidCallback onCustomSelection;

  const _SubscribedCoursesPage({required this.onSubscriptionSelected, required this.onCustomSelection});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<List<SubscribedCourseYear>>(
      future: ref.read(userProvider.notifier).getSubscribedCourseYears(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error loading subscriptions', style: Theme.of(context).textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Text(
                    snapshot.error.toString(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  FilledButton.tonal(onPressed: () => ref.refresh(userProvider), child: const Text('Retry')),
                ],
              ),
            ),
          );
        }

        final subscriptions = snapshot.data ?? [];

        if (subscriptions.isEmpty) {
          return _EmptySubscriptionsList(onCustomSelection: onCustomSelection);
        }

        return _SubscriptionsList(subscriptions: subscriptions, onSubscriptionSelected: onSubscriptionSelected);
      },
    );
  }
}

/// Widget to display when there are no subscriptions
class _EmptySubscriptionsList extends StatelessWidget {
  final VoidCallback onCustomSelection;

  const _EmptySubscriptionsList({required this.onCustomSelection});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.subscriptions_outlined, size: 64, color: Theme.of(context).colorScheme.secondary.withAlpha(128)),
            const SizedBox(height: 16),
            Text('No active subscriptions', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(
              'You don\'t have any active subscriptions. Choose a course and year to continue.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            FilledButton(onPressed: onCustomSelection, child: const Text('Choose Course and Year')),
          ],
        ),
      ),
    );
  }
}

/// Widget to display the list of subscriptions
class _SubscriptionsList extends StatelessWidget {
  final List<SubscribedCourseYear> subscriptions;
  final Function(String, String) onSubscriptionSelected;

  const _SubscriptionsList({required this.subscriptions, required this.onSubscriptionSelected});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: subscriptions.length,
      itemBuilder: (context, index) {
        final subscription = subscriptions[index];
        return _SubscriptionCard(
          subscription: subscription,
          onTap: () => onSubscriptionSelected(subscription.courseId, subscription.yearId),
        );
      },
    );
  }
}

/// Card widget for each subscription
class _SubscriptionCard extends StatelessWidget {
  final SubscribedCourseYear subscription;
  final VoidCallback onTap;

  const _SubscriptionCard({required this.subscription, required this.onTap});

  String _formatExpiryDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference <= 0) {
      return 'Expired';
    } else if (difference == 1) {
      return 'Expires tomorrow';
    } else if (difference < 30) {
      return 'Expires in $difference days';
    } else {
      final months = (difference / 30).floor();
      return 'Expires in $months ${months == 1 ? 'month' : 'months'}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(child: Icon(Icons.school, color: Theme.of(context).colorScheme.onPrimaryContainer)),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${subscription.courseId} - ${subscription.yearId}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatExpiryDate(subscription.expiryDate),
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Theme.of(context).colorScheme.onSurface.withAlpha(153)),
                    ),
                  ],
                ),
              ),
              Icon(Icons.chevron_right, color: Theme.of(context).colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }
}

/// Page for custom course and year selection
class _CustomSelectionPage extends ConsumerWidget {
  final VoidCallback onSelectionComplete;

  const _CustomSelectionPage({required this.onSelectionComplete});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCourse = ref.watch(selectedDialogCourseProvider);
    final selectedYear = ref.watch(selectedDialogYearProvider);
    final isSelectionComplete = selectedCourse != null && selectedYear != null;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const _CourseDropdown(),
          const SizedBox(height: 16),
          const _YearDropdown(),
          const Spacer(),
          FilledButton(
            onPressed: isSelectionComplete ? onSelectionComplete : null,
            child: const Padding(padding: EdgeInsets.symmetric(vertical: 12), child: Text('Select')),
          ),
        ],
      ),
    );
  }
}

/// Course dropdown component
class _CourseDropdown extends ConsumerWidget {
  const _CourseDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coursesAsync = ref.watch(coursesProvider);
    final selectedCourse = ref.watch(selectedDialogCourseProvider);

    return coursesAsync.when(
      data: (coursesData) {
        var coursesList = coursesData?.courses.values.toList() ?? [];
        coursesList.sort((a, b) => a.id.compareTo(b.id));

        return DropdownButtonFormField<CourseEntity>(
          value: selectedCourse,
          decoration: const InputDecoration(labelText: 'Select Course', border: OutlineInputBorder()),
          items:
              coursesList.map((course) {
                return DropdownMenuItem(value: course, child: Text(course.id));
              }).toList(),
          onChanged: (newValue) {
            ref.read(selectedDialogCourseProvider.notifier).setCourse(newValue);
            ref.read(selectedDialogYearProvider.notifier).setYear(null);
          },
        );
      },
      loading: () => const Center(child: LinearProgressIndicator()),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}

/// Year dropdown component
class _YearDropdown extends ConsumerWidget {
  const _YearDropdown();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCourse = ref.watch(selectedDialogCourseProvider);
    final selectedYear = ref.watch(selectedDialogYearProvider);
    final availableYears = ref.watch(availableDialogYearsProvider);

    if (selectedCourse == null) {
      return const SizedBox.shrink();
    }

    return DropdownButtonFormField<String>(
      value: selectedYear,
      decoration: const InputDecoration(labelText: 'Select Year', border: OutlineInputBorder()),
      items:
          availableYears.map((year) {
            return DropdownMenuItem(value: year, child: Text(year));
          }).toList(),
      onChanged: (newValue) {
        ref.read(selectedDialogYearProvider.notifier).setYear(newValue);
      },
    );
  }
}
