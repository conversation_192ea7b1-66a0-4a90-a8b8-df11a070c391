import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';

/// A dialog that displays legal documents (Terms & Conditions and Privacy Policy)
/// and allows the user to accept or decline them.
class LegalDocumentsDialog extends ConsumerStatefulWidget {
  /// Creates a dialog that displays legal documents.
  const LegalDocumentsDialog({super.key, this.initialTabIndex = 0, this.onAccept, this.onDecline});

  /// The initial page index to display (0 for T&C, 1 for Privacy Policy).
  final int initialTabIndex;

  /// Callback when the user accepts the documents.
  final void Function()? onAccept;

  /// Callback when the user declines the documents.
  final void Function()? onDecline;

  /// Shows the legal documents dialog.
  static Future<bool?> show({
    required BuildContext context,
    int initialTabIndex = 0,
    void Function()? onAccept,
    void Function()? onDecline,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => LegalDocumentsDialog(initialTabIndex: initialTabIndex, onAccept: onAccept, onDecline: onDecline),
    );
  }

  @override
  ConsumerState<LegalDocumentsDialog> createState() => _LegalDocumentsDialogState();
}

class _LegalDocumentsDialogState extends ConsumerState<LegalDocumentsDialog> {
  late PageController _pageController;
  String _termsContent = '';
  String _privacyContent = '';
  bool _isLoading = true;
  bool _isAccepting = false; // Flag to track when the accept operation is in progress
  bool _acceptedTerms = false;
  bool _acceptedPrivacy = false;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialTabIndex);
    _currentPage = widget.initialTabIndex;
    _loadDocuments();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadDocuments() async {
    try {
      final termsContent = await rootBundle.loadString('assets/legal/terms-conditions.md');
      final privacyContent = await rootBundle.loadString('assets/legal/privacy-policy.md');

      setState(() {
        _termsContent = termsContent;
        _privacyContent = privacyContent;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _termsContent = 'Error loading Terms & Conditions.';
        _privacyContent = 'Error loading Privacy Policy.';
        _isLoading = false;
      });
    }
  }

  bool get _canAccept => _acceptedTerms && _acceptedPrivacy;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final pageTitle = _currentPage == 0 ? 'Terms & Conditions' : 'Privacy Policy';

    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 600),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: Row(
                    children: [
                      Text(pageTitle, style: theme.textTheme.titleLarge),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          if (widget.onDecline != null) {
                            widget.onDecline!();
                          }
                          Navigator.of(context).pop(false);
                        },
                        tooltip: 'Close',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child:
                      _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : PageView(
                            controller: _pageController,
                            physics: const NeverScrollableScrollPhysics(),
                            onPageChanged: (index) {
                              setState(() {
                                _currentPage = index;
                              });
                            },
                            children: [_buildTermsTab(), _buildPrivacyTab()],
                          ),
                ),
                // Page indicator
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              _currentPage == 0 ? theme.colorScheme.primary : theme.colorScheme.onSurface.withAlpha(76),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              _currentPage == 1 ? theme.colorScheme.primary : theme.colorScheme.onSurface.withAlpha(76),
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (_currentPage == 0) Center(),
                      if (_currentPage == 0) _buildNextButton(),
                      if (_currentPage == 1) _buildBackButton(),
                      if (_currentPage == 1) _buildAcceptButton(context),
                    ],
                  ),
                ),
              ],
            ),
            // Overlay with loading spinner when accepting
            if (_isAccepting)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withAlpha(76),
                  child: const Center(child: CircularProgressIndicator()),
                ),
              ),
          ],
        ),
      ),
    );
  }

  FilledButton _buildAcceptButton(BuildContext context) {
    return FilledButton(
      onPressed:
          _canAccept
              ? () async {
                // Set loading state to prevent multiple clicks
                setState(() {
                  _isAccepting = true;
                });

                try {
                  // Store the context and callback for use after the async gap
                  final onAcceptCallback = widget.onAccept;

                  // Update the user entity with acceptance information using the notifier function
                  final success = await ref.read(userProvider.notifier).updateLegalDocumentsAcceptance();

                  if (success && onAcceptCallback != null) {
                    onAcceptCallback();
                  }
                  if (context.mounted) {
                    Navigator.of(context).pop(success);
                  }
                } finally {
                  // Reset loading state if the dialog is still mounted
                  if (mounted) {
                    setState(() {
                      _isAccepting = false;
                    });
                  }
                }
              }
              : null,
      child: const Text('Accept'),
    );
  }

  FilledButton _buildNextButton() {
    return FilledButton(
      onPressed:
          _acceptedTerms
              ? () {
                _pageController.animateToPage(1, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
              }
              : null,
      child: const Text('Next'),
    );
  }

  TextButton _buildBackButton() {
    return TextButton(
      onPressed: () {
        _pageController.animateToPage(0, duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
      },
      child: const Text('Back'),
    );
  }

  Widget _buildTermsTab() {
    return Column(
      children: [
        Expanded(
          child: Markdown(
            data: _termsContent,
            padding: const EdgeInsets.all(16),
            styleSheet: MarkdownStyleSheet(
              h1: Theme.of(context).textTheme.headlineMedium,
              h2: Theme.of(context).textTheme.titleLarge,
              h3: Theme.of(context).textTheme.titleMedium,
              p: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
        _buildAcceptanceCheckbox(
          label: 'I have read and accept the Terms & Conditions',
          value: _acceptedTerms,
          onChanged: (value) {
            setState(() {
              _acceptedTerms = value ?? false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildPrivacyTab() {
    return Column(
      children: [
        Expanded(
          child: Markdown(
            data: _privacyContent,
            padding: const EdgeInsets.all(16),
            styleSheet: MarkdownStyleSheet(
              h1: Theme.of(context).textTheme.headlineMedium,
              h2: Theme.of(context).textTheme.titleLarge,
              h3: Theme.of(context).textTheme.titleMedium,
              p: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
        _buildAcceptanceCheckbox(
          label: 'I have read and accept the Privacy Policy',
          value: _acceptedPrivacy,
          onChanged: (value) {
            setState(() {
              _acceptedPrivacy = value ?? false;
            });
          },
        ),
      ],
    );
  }

  Widget _buildAcceptanceCheckbox({
    required String label,
    required bool value,
    required ValueChanged<bool?> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Checkbox(value: value, onChanged: onChanged),
          Expanded(child: Text(label, style: Theme.of(context).textTheme.bodyMedium)),
        ],
      ),
    );
  }
}
