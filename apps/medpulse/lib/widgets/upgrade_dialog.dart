import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/screens/subscription_screen.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';

enum UpgradeScenario { general, freeTestEnd, premiumQuestion, bookmarkFeature }

/// Shows a dialog asking the user if they want to upgrade to premium
/// This is a simpler version of the previous SubscriptionDialog that only asks
/// if the user wants to upgrade now or later
void showUpgradeDialog(
  BuildContext context,
  WidgetRef ref,
  String message, {
  UpgradeScenario scenario = UpgradeScenario.general,
  VoidCallback? onDismiss,
  int? remainingQuestions,
  VoidCallback? onFinishTest,
}) {
  String title;
  String primaryButtonText;
  String secondaryButtonText;
  List<Widget>? additionalContent;

  // Configure dialog based on scenario
  switch (scenario) {
    case UpgradeScenario.freeTestEnd:
      title = 'Premium Content Available';
      primaryButtonText = 'Upgrade to Premium';
      secondaryButtonText = 'Finish Test';
      additionalContent =
          remainingQuestions != null
              ? [
                const SizedBox(height: 12),
                Text(
                  'There are $remainingQuestions more questions available in the premium version.',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 16),
                Text(
                  'Would you like to finish the test or upgrade to premium to access all questions?',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ]
              : null;
      break;

    case UpgradeScenario.premiumQuestion:
      title = 'Premium Question';
      primaryButtonText = 'Upgrade Now';
      secondaryButtonText = 'Maybe Later';
      break;

    case UpgradeScenario.bookmarkFeature:
      title = 'Premium Feature';
      primaryButtonText = 'Upgrade Now';
      secondaryButtonText = 'Maybe Later';
      break;

    case UpgradeScenario.general:
      title = 'Premium Content';
      primaryButtonText = 'Upgrade Now';
      secondaryButtonText = 'Maybe Later';
      break;
  }

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(message, style: Theme.of(context).textTheme.bodyLarge),
          if (additionalContent != null) ...additionalContent,
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            if (onDismiss != null) {
              onDismiss();
            } else if (scenario == UpgradeScenario.freeTestEnd && onFinishTest != null) {
              onFinishTest();
            }
          },
          child: Text(secondaryButtonText),
        ),
        FilledButton(
          onPressed: () async {
            Navigator.of(context).pop();

/*
            // Check legal documents
            final user = ref.read(userProvider).valueOrNull;
            if (user == null || context.mounted == false) return;

            final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
            if (!hasAccepted) {
              // Show the legal documents dialog
              final accepted = await LegalDocumentsDialog.show(context: context);
              if (accepted != true) return;
            }
*/

            if (context.mounted) {
              // Navigate to the subscription screen instead of showing a dialog
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            }
          },
          child: Text(primaryButtonText),
        ),
      ],
    ),
  );
}
