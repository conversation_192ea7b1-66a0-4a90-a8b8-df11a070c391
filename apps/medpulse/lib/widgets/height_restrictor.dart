import 'package:flutter/material.dart';

/// A widget that restricts the height of its child to be within specified minimum and maximum values.
///
/// This is useful for creating responsive layouts where you want to control the vertical size
/// of a widget based on the available screen space.
class HeightRestrictor extends StatelessWidget {
  final Widget child;
  final double minHeight;
  final double maxHeight;
  final Alignment alignment;

  const HeightRestrictor({
    super.key,
    required this.child,
    this.minHeight = 400.0,
    this.maxHeight = 800.0,
    this.alignment = Alignment.topCenter,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate the container height based on available space
        double containerHeight = constraints.maxHeight;
        bool needsScrolling = containerHeight < minHeight;

        // Apply min/max constraints to our container
        if (containerHeight > maxHeight) {
          containerHeight = maxHeight;
        } else if (containerHeight < minHeight) {
          containerHeight = minHeight;
        }

        // Create the content widget
        Widget content = SizedBox(
          height: containerHeight,
          child: child,
        );

        // If available height is less than minHeight, wrap in SingleChildScrollView
        if (needsScrolling) {
          content = SingleChildScrollView(
            child: SizedBox(
              height: minHeight,
              child: child,
            ),
          );
        }

        return content;
/*
        return Stack(
          alignment: alignment,
          children: [content],
        );
*/
      },
    );
  }
}
