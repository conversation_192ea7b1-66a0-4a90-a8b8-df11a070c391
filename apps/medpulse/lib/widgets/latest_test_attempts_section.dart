import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/test_result.dart';
import 'package:medpulse/features/mcq_test/widgets/answers_review_dialog.dart';
import 'package:medpulse/providers/test_attempt_provider.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/screens/test_attempts_screen.dart';
import 'package:providers/common.dart';

import 'test_result_card.dart';

/// Main widget for the Latest Test Attempts section
class LatestTestAttemptsSection extends StatelessWidget {
  const LatestTestAttemptsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        // Header with title and "Show All" button
        LatestTestAttemptsHeader(),
        SizedBox(height: 16),
        // Content with user's test attempts
        LatestTestAttemptsList(),
      ],
    );
  }
}

/// Header widget with title and "Show All" button
class LatestTestAttemptsHeader extends ConsumerWidget {
  const LatestTestAttemptsHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Only watch the specific data needed for this widget
    final hasMoreThanFiveResults = ref.watch(
      userProvider.select((asyncValue) {
        return asyncValue.whenOrNull(data: (user) => user?.results != null && user!.results!.length > 5) ?? false;
      }),
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(Icons.history, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text('Your Latest Attempts', style: Theme.of(context).textTheme.titleLarge),
          ],
        ),
        if (hasMoreThanFiveResults)
          TextButton(
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) => const TestAttemptsScreen()));
            },
            child: const Text('Show All'),
          ),
      ],
    );
  }
}

/// List widget that displays the user's test attempts
class LatestTestAttemptsList extends ConsumerWidget {
  const LatestTestAttemptsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return userAsync.when(
      data: (user) {
        if (user == null || user.results == null || user.results!.isEmpty) {
          return const LatestTestAttemptsEmpty();
        }

        // Display up to 5 most recent test results
        final results = user.results!.take(5).toList();
        return LatestTestAttemptsContent(results: results);
      },
      loading: () => const LatestTestAttemptsLoading(),
      error: (error, stackTrace) {
        dbgPrint('Error loading user: $error');
        return const LatestTestAttemptsError();
      },
    );
  }
}

/// Widget that displays the content when there are test attempts
class LatestTestAttemptsContent extends StatelessWidget {
  final List<TestResult> results;

  const LatestTestAttemptsContent({super.key, required this.results});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var result in results)
          TestResultCard(result: result, showDate: true, onTap: () => _showTestResultDetails(context, result)),
      ],
    );
  }

  /// Shows the test result details dialog
  Future<void> _showTestResultDetails(BuildContext context, TestResult result) async {
    try {
      // Load the test progress using ProviderContainer to avoid rebuilding the widget
      final container = ProviderScope.containerOf(context);
      final testProgress = await container.read(testAttemptProvider(result.id).future);

      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      // Show the answers review dialog
      AnswersReviewDialog.show(
        context: context,
        subscriptionId: testProgress.result!.subscriptionId,
        testId: testProgress.result!.testId,
        free: testProgress.result!.free,
        progress: testProgress,
        questionCount: result.questionCount,
      );
    } catch (error) {
      // Check if context is still mounted before proceeding
      if (!context.mounted) return;

      // Show error dialog
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Error'),
              content: Text('Failed to load test details: ${error.toString()}'),
              actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('OK'))],
            ),
      );
    }
  }
}

/// Widget for the empty state
class LatestTestAttemptsEmpty extends StatelessWidget {
  const LatestTestAttemptsEmpty({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Center(child: Text('No test attempts yet. Take a test to see your results here!')),
    );
  }
}

/// Widget for the loading state
class LatestTestAttemptsLoading extends StatelessWidget {
  const LatestTestAttemptsLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }
}

/// Widget for the error state
class LatestTestAttemptsError extends StatelessWidget {
  const LatestTestAttemptsError({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Error loading test attempts'));
  }
}
