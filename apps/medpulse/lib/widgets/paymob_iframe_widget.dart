import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:providers/common.dart';

class PaymobIframeWidget extends StatefulWidget {
  final String paymentUrl;
  final Function(bool success, String? message) onPaymentComplete;

  const PaymobIframeWidget({Key? key, required this.paymentUrl, required this.onPaymentComplete}) : super(key: key);

  @override
  State<PaymobIframeWidget> createState() => _PaymobIframeWidgetState();
}

class _PaymobIframeWidgetState extends State<PaymobIframeWidget> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  void _initWebView() {
    dbgPrint('Initializing WebView for Paymob iframe');
    dbgPrint('Loading payment URL: ${widget.paymentUrl}');

    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageStarted: (String url) {
                dbgPrint('WebView page started loading: $url');
                setState(() {
                  _isLoading = true;
                });
              },
              onPageFinished: (String url) {
                dbgPrint('WebView page finished loading: $url');
                setState(() {
                  _isLoading = false;
                });
              },
              onNavigationRequest: (NavigationRequest request) {
                dbgPrint('WebView navigation request: ${request.url}');

                // Check if the URL contains success or failure indicators
                // Paymob can use different patterns for success/failure
                if (request.url.contains('success=true') ||
                    request.url.contains('success=1') ||
                    request.url.contains('status=success') ||
                    request.url.contains('is_successful=true') ||
                    request.url.contains('is_successful=1') ||
                    request.url.contains('txn_response_code=APPROVED')) {
                  dbgPrint('Payment successful detected in URL');
                  widget.onPaymentComplete(true, 'Payment successful');
                  return NavigationDecision.prevent;
                } else if (request.url.contains('success=false') ||
                    request.url.contains('success=0') ||
                    request.url.contains('status=failed') ||
                    request.url.contains('is_successful=false') ||
                    request.url.contains('is_successful=0') ||
                    request.url.contains('txn_response_code=DECLINED') ||
                    request.url.contains('error_occured=true')) {
                  dbgPrint('Payment failure detected in URL');
                  widget.onPaymentComplete(false, 'Payment failed');
                  return NavigationDecision.prevent;
                }

                // Also check for callback URL which might indicate completion
                if (request.url.contains('handlePaymobCallback')) {
                  dbgPrint('Callback URL detected: ${request.url}');
                  // If we reach the callback URL, assume success unless explicitly marked as failure
                  if (!request.url.contains('failed=true') && !request.url.contains('error=true')) {
                    dbgPrint('Payment completion assumed from callback URL');
                    widget.onPaymentComplete(true, 'Payment completed');
                  } else {
                    dbgPrint('Payment failure detected in callback URL');
                    widget.onPaymentComplete(false, 'Payment processing failed');
                  }
                  return NavigationDecision.prevent;
                }

                dbgPrint('Allowing navigation to: ${request.url}');
                return NavigationDecision.navigate;
              },
            ),
          )
          ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        if (_isLoading) const Center(child: CircularProgressIndicator()),
      ],
    );
  }
}
