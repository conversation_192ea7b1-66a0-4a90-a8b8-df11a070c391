import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/question_entity.dart';
import '../../../widgets/cached_image.dart';
import '../../../theme/app_theme.dart';

/// Widget that displays a flip card for flip card type questions
class FlipCardWidget extends ConsumerStatefulWidget {
  final QuestionEntity question;
  final int questionIndex;
  final Function(int) onAnswerSelect;
  final VoidCallback? onNextQuestion;
  final bool allowFlip;

  const FlipCardWidget({
    super.key,
    required this.question,
    required this.questionIndex,
    required this.onAnswerSelect,
    this.onNextQuestion,
    this.allowFlip = true,
  });

  @override
  ConsumerState<FlipCardWidget> createState() => _FlipCardWidgetState();
}

class _FlipCardWidgetState extends ConsumerState<FlipCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _flipAnimation;
  bool _isFlipped = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _flip() {
    if (!widget.allowFlip) return;
    
    if (_isFlipped) {
      _animationController.reverse();
    } else {
      _animationController.forward();
    }
    setState(() {
      _isFlipped = !_isFlipped;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Main flip card
        GestureDetector(
          onTap: _flip,
          child: AnimatedBuilder(
            animation: _flipAnimation,
            builder: (context, child) {
              final isShowingFront = _flipAnimation.value < 0.5;
              
              return Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..setEntry(3, 2, 0.001)
                  ..rotateY(_flipAnimation.value * pi),
                child: SizedBox(
                  width: double.infinity,
                  height: 300,
                  child: isShowingFront 
                    ? _buildFrontCard() 
                    : Transform(
                        alignment: Alignment.center,
                        transform: Matrix4.identity()..rotateY(pi),
                        child: _buildBackCard(),
                      ),
                ),
              );
            },
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Action buttons when card is flipped
        if (_isFlipped) _buildActionButtons(),
        
        // Reveal button when not flipped
        if (!_isFlipped && widget.allowFlip) _buildRevealButton(),
      ],
    );
  }

  Widget _buildFrontCard() {
    return Card(
      elevation: 8,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withAlpha(25),
              Theme.of(context).colorScheme.primary.withAlpha(51),
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question header
              Row(
                children: [
                  Icon(
                    Icons.quiz_outlined,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Question ${widget.questionIndex + 1}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Question text
              Text(
                widget.question.question,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              
              // Question image if available
              if (widget.question.questionImage != null && widget.question.questionImage!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Center(
                  child: CachedImage(
                    key: ValueKey('flip-card-question-${widget.question.questionImage!}'),
                    imageWidth: widget.question.questionImageWidth,
                    imageHeight: widget.question.questionImageHeight,
                    storagePath: widget.question.questionImage!,
                    errorWidget: const Icon(Icons.image_not_supported, size: 64),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackCard() {
    return Card(
      elevation: 8,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).colorScheme.surface,
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Answer header
              Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Answer',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Answer text
              if (widget.question.answer != null && widget.question.answer!.isNotEmpty)
                Text(
                  widget.question.answer!,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              
              // Answer image if available
              if (widget.question.answerImage != null && widget.question.answerImage!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Center(
                  child: CachedImage(
                    key: ValueKey('flip-card-answer-${widget.question.answerImage!}'),
                    imageWidth: widget.question.answerImageWidth,
                    imageHeight: widget.question.answerImageHeight,
                    storagePath: widget.question.answerImage!,
                    errorWidget: const Icon(Icons.image_not_supported, size: 64),
                  ),
                ),
              ],
              
              // Explanation if available
              if (widget.question.explanation != null && widget.question.explanation!.isNotEmpty) ...[
                const SizedBox(height: 24),
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.secondary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Explanation',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  widget.question.explanation!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withAlpha(204),
                  ),
                ),
              ],
              
              // Reference if available
              if (widget.question.reference != null && widget.question.reference!.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Reference',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.question.reference!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Did you get it right?',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 16),
          // I got it wrong button
          OutlinedButton(
            onPressed: () {
              widget.onAnswerSelect(2); // 2 means wrong answer
              widget.onNextQuestion?.call(); // Move to next question
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: getAppColor(context).incorrectAnswerColor,
              side: BorderSide(color: getAppColor(context).incorrectAnswerColor),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.close, size: 20),
                const SizedBox(width: 8),
                Text('Wrong'),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // I got it right button
          FilledButton(
            onPressed: () {
              widget.onAnswerSelect(1); // 1 means correct answer
              widget.onNextQuestion?.call(); // Move to next question
            },
            style: FilledButton.styleFrom(
              backgroundColor: getAppColor(context).correctAnswerColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.check, size: 20),
                const SizedBox(width: 8),
                const Text('Right'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevealButton() {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: _flip,
        icon: const Icon(Icons.visibility),
        label: const Text('Reveal Answer'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }
}
