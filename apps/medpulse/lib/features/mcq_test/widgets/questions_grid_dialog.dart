import 'package:flutter/material.dart';
import 'package:medpulse/theme/app_theme.dart';

import 'question_button.dart';

/// Dialog showing all questions in a grid format
class QuestionsGridDialog extends StatefulWidget {
  final int totalQuestions;
  final int fullTotalQuestions;
  final int currentIndex;
  final Map<int, int> userAnswers;
  final Set<int> markedForReview;
  final Set<int> bookmarkedQuestions;
  final void Function(int) onQuestionSelected;
  final VoidCallback onLockedQuestionSelected;
  final bool free;

  // Questions per page in grid (5x5)
  static const int questionsPerPage = 25;
  static const int gridColumns = 5;

  const QuestionsGridDialog({
    super.key,
    required this.totalQuestions,
    required this.fullTotalQuestions,
    required this.currentIndex,
    required this.userAnswers,
    required this.markedForReview,
    required this.bookmarkedQuestions,
    required this.onQuestionSelected,
    required this.onLockedQuestionSelected,
    required this.free,
  });

  @override
  State<QuestionsGridDialog> createState() => _QuestionsGridDialogState();
}

class _QuestionsGridDialogState extends State<QuestionsGridDialog> {
  late int _currentPage;

  @override
  void initState() {
    super.initState();
    // Initialize to the page containing the current question
    _currentPage = widget.currentIndex ~/ QuestionsGridDialog.questionsPerPage;
  }

  int get _totalPages => (widget.fullTotalQuestions / QuestionsGridDialog.questionsPerPage).ceil();

  bool _isQuestionLocked(int index) {
    return index >= widget.totalQuestions && index < widget.fullTotalQuestions;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 300),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'All Questions',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),

              // Questions grid
              _buildQuestionsGrid(),

              const SizedBox(height: 16),

              // Legend popup button
              TextButton.icon(
                icon: const Icon(Icons.info_outline),
                label: const Text('Legend'),
                onPressed: () => _showLegendPopup(context),
              ),

              const SizedBox(height: 8),

              // Pagination controls
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: _currentPage > 0 ? () => setState(() => _currentPage--) : null,
                  ),
                  Text('Page ${_currentPage + 1} of $_totalPages'),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: _currentPage < _totalPages - 1 ? () => setState(() => _currentPage++) : null,
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Close button
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLegendPopup(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Question Legend'),
          content: Container(
            constraints: const BoxConstraints(maxWidth: 200),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [

                // unanswered question legend item
                _LegendItem(
                  color: getAppColor(context).unansweredDialogBackground,
                  label: 'Unanswered questions',
                ),

                // Answered question legend item
                _LegendItem(
                  color: Theme.of(context).colorScheme.primary,
                  label: 'Answered questions',
                ),

                // Review question legend item
                _LegendItem(
                  color: getAppColor(context).markedForReviewBackground,
                  label: 'Marked for review',
                ),

                // Premium question legend item (conditional)
                if (widget.free && widget.totalQuestions < widget.fullTotalQuestions)
                  _LegendItem(
                    color: Theme.of(context).colorScheme.tertiary,
                    label: 'Premium questions',
                  ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuestionsGrid() {
    final int startIndex = _currentPage * QuestionsGridDialog.questionsPerPage;
    final int endIndex =
    (startIndex + QuestionsGridDialog.questionsPerPage - 1).clamp(0, widget.fullTotalQuestions - 1);
    final int itemCount = endIndex - startIndex + 1;

    return Container(
      constraints: const BoxConstraints(maxWidth: 250, maxHeight: 250),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: QuestionsGridDialog.gridColumns,
          crossAxisSpacing: 8.0,
          mainAxisSpacing: 8.0,
        ),
        itemCount: itemCount,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemBuilder: (context, relativeIndex) {
          final index = startIndex + relativeIndex;
          return QuestionButton(
            index: index,
            isCurrent: index == widget.currentIndex,
            isAnswered: widget.userAnswers.containsKey(index),
            isMarkedForReview: widget.markedForReview.contains(index),
            isBookmarked: widget.bookmarkedQuestions.contains(index),
            isLocked: _isQuestionLocked(index),
            isDialog: true,
            onTap: () => widget.onQuestionSelected(index),
            onLockedTap: () => widget.onLockedQuestionSelected(),
          );
        },
      ),
    );
  }
}

/// A reusable legend item widget
class _LegendItem extends StatelessWidget {
  final Color color;
  final String label;

  const _LegendItem({
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
            ),
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }
}