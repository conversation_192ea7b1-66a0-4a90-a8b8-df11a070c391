import 'package:flutter/material.dart';
import 'package:entities/test_result.dart';

/// Widget displaying detailed test score information
class ScoreDetails extends StatelessWidget {
  final TestResult testResult;
  final int? testDurationMinutes;

  const ScoreDetails({
    super.key,
    required this.testResult,
    this.testDurationMinutes,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          _buildScoreRow(
            context,
            'Score',
            '${testResult.scorePercentage.toStringAsFixed(1)}%',
          ),
          const Divider(),
          _buildScoreRow(
            context,
            'Correct Answers',
            '${testResult.correctAnswers} / ${testResult.questionCount}',
          ),
          const Divider(),
          _buildScoreRow(
            context,
            'Completion',
            '${(testResult.answeredCount / testResult.questionCount * 100).toStringAsFixed(1)}%',
          ),
          const Divider(),
          ..._buildTimeRows(context),
        ],
      ),
    );
  }

  Widget _buildScoreRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTimeRows(BuildContext context) {
    final List<Widget> timeRows = [];
    
    if (testDurationMinutes != null) {
      // Test had a duration limit
      final allowedDuration = Duration(minutes: testDurationMinutes!);
      final isOvertime = testResult.timeTaken > allowedDuration;
      
      // Add allowed time row
      timeRows.add(
        _buildScoreRow(
          context,
          'Allowed Time',
          _formatDuration(allowedDuration),
        ),
      );
      
      timeRows.add(const Divider());
      
      // Add time taken row with overtime indication
      if (isOvertime) {
        final overtimeDuration = testResult.timeTaken - allowedDuration;
        timeRows.add(
          _buildOvertimeRow(
            context,
            'Time Taken',
            '${_formatDuration(testResult.timeTaken)} (${_formatDuration(overtimeDuration)} overtime)',
          ),
        );
      } else {
        timeRows.add(
          _buildScoreRow(
            context,
            'Time Taken',
            _formatDuration(testResult.timeTaken),
          ),
        );
      }
    } else {
      // Test had no duration limit - just show time taken
      timeRows.add(
        _buildScoreRow(
          context,
          'Time Taken',
          _formatDuration(testResult.timeTaken),
        ),
      );
    }
    
    return timeRows;
  }
  
  Widget _buildOvertimeRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    final parts = <String>[];
    if (hours > 0) {
      parts.add('$hours hr');
    }
    if (minutes > 0) {
      parts.add('$minutes min');
    }
    if (seconds > 0 || parts.isEmpty) {
      parts.add('$seconds sec');
    }

    return parts.join(' ');
  }
}
