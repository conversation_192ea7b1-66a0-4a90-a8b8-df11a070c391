import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/widgets/upgrade_dialog.dart';

import 'question_button.dart';
import 'questions_grid_dialog.dart';

/// Question navigation bar showing dots for questions
class QuestionNavigationBar extends ConsumerWidget {
  final int questionCount;
  final int fullQuestionCount;
  final int currentIndex;
  final Map<int, int> userAnswers;
  final Set<int> markedForReview;
  final Set<int> bookmarkedQuestions;
  final void Function(int) onTap;
  final VoidCallback onOptionsPressed;
  final String testId;
  final bool free;

  // Show +-3 questions around the current index
  static const int _visibleItemsEachSide = 3;

  const QuestionNavigationBar({
    super.key,
    required this.testId,
    required this.free,
    required this.questionCount,
    required this.fullQuestionCount,
    required this.currentIndex,
    required this.userAnswers,
    required this.onTap,
    required this.onOptionsPressed,
    this.markedForReview = const {},
    this.bookmarkedQuestions = const {},
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate visible range ensuring we always try to show 7 items
    final int startIdx = _calculateStartIndex();
    final int endIdx = _calculateEndIndex();
    final int visibleCount = endIdx - startIdx + 1;

    return Row(
      children: [
        // Question indicators
        Expanded(
          child: SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: visibleCount,
              itemBuilder: (context, relativeIndex) {
                final index = startIdx + relativeIndex;
                return QuestionButton(
                  index: index,
                  isCurrent: index == currentIndex,
                  isAnswered: userAnswers.containsKey(index),
                  isMarkedForReview: markedForReview.contains(index),
                  isBookmarked: bookmarkedQuestions.contains(index),
                  isLocked: _isQuestionLocked(index),
                  isDialog: false,
                  onTap: () => onTap(index),
                  onLockedTap: () => _showUpgradeDialog(context),
                );
              },
            ),
          ),
        ),

        // All questions button
        IconButton(
          icon: const Icon(Icons.grid_view),
          onPressed: () => _showQuestionsGridDialog(context, ref),
          tooltip: 'View all questions',
        ),

        // Options button
        IconButton(icon: const Icon(Icons.more_vert), onPressed: onOptionsPressed, tooltip: 'Test options'),
      ],
    );
  }

  // Calculate the start and end indices to always show 7 items when possible
  int _calculateStartIndex() {
    // Default start: current index - 3
    int startIdx = currentIndex - _visibleItemsEachSide;

    // If we're near the end, shift the window to show 7 items
    final int maxEndIdx = fullQuestionCount - 1;
    final int idealEndIdx = currentIndex + _visibleItemsEachSide;

    if (idealEndIdx > maxEndIdx) {
      // We're near the end, so shift the start earlier if possible
      startIdx = maxEndIdx - (2 * _visibleItemsEachSide);
    }

    // Ensure we don't go below 0
    return startIdx < 0 ? 0 : startIdx;
  }

  // Calculate the end index with boundary checks
  int _calculateEndIndex() {
    // Default end: current index + 3
    int endIdx = currentIndex + _visibleItemsEachSide;

    // If we're near the start, extend the end to show 7 items
    final int startIdx = _calculateStartIndex();

    if (startIdx == 0 && currentIndex < _visibleItemsEachSide) {
      // We're near the start, so extend the end if possible
      endIdx = (2 * _visibleItemsEachSide);
    }

    // Ensure we don't exceed the maximum
    final int maxEndIdx = fullQuestionCount - 1;
    return endIdx > maxEndIdx ? maxEndIdx : endIdx;
  }

  bool _isQuestionLocked(int index) {
    return index >= questionCount && index < fullQuestionCount;
  }

  // Show dialog with grid of all questions
  void _showQuestionsGridDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => QuestionsGridDialog(
            totalQuestions: questionCount,
            fullTotalQuestions: fullQuestionCount,
            currentIndex: currentIndex,
            userAnswers: userAnswers,
            markedForReview: markedForReview,
            bookmarkedQuestions: bookmarkedQuestions,
            onQuestionSelected: (index) {
              Navigator.of(context).pop();
              onTap(index);
            },
            onLockedQuestionSelected: () {
              Navigator.of(context).pop();
              _showUpgradeDialog(context);
            },
            free: free,
          ),
    );
  }

  // Show upgrade dialog
  void _showUpgradeDialog(BuildContext context) {
    // Use Consumer to get a WidgetRef
    showDialog(
      context: context,
      builder:
          (context) => Consumer(
            builder: (context, ref, _) {
              // Use the ref directly
              showUpgradeDialog(
                context,
                ref,
                'This feature is available only in the premium version. Would you like to upgrade?',
                scenario: UpgradeScenario.premiumQuestion,
              );
              // Return an empty container as we're immediately showing another dialog
              return Container();
            },
          ),
    );
  }
}
