import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../widgets/cached_image.dart';
import '../../../theme/app_theme.dart';
import '../../../providers/test_mode_provider.dart';
import '../../../providers/question_provider.dart';
import '../providers/test_progress_provider.dart';

/// Widget for displaying MCQ options
class OptionsWidget extends ConsumerStatefulWidget {
  final List<String> options;
  final List<String> optionImages;
  final List<double?> optionImagesWidth;
  final List<double?> optionImagesHeight;
  final int? selectedIndex;
  final Function(int) onSelect;
  final String subscriptionId;
  final String testId;
  final bool free;
  final int questionIndex;

  const OptionsWidget({
    super.key,
    required this.options,
    required this.optionImages,
    required this.optionImagesWidth,
    required this.optionImagesHeight,
    this.selectedIndex,
    required this.onSelect,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.questionIndex,
  });

  @override
  ConsumerState<OptionsWidget> createState() => _OptionsWidgetState();
}

class _OptionsWidgetState extends ConsumerState<OptionsWidget> {
  int? _tappedOptionIndex;
  
  @override
  Widget build(BuildContext context) {
    final testModeNotifier = ref.watch(testModeProvider.notifier);
    final progressAsync = ref.watch(testProgressNotifierProvider(widget.subscriptionId, widget.testId, free: widget.free));
    final questionAsync = ref.watch(questionProvider(widget.subscriptionId, widget.testId, widget.questionIndex, free: widget.free));
    
    // Return loading/error states if question not ready
    if (questionAsync.isLoading) return const Center(child: CircularProgressIndicator());
    if (questionAsync.hasError) return const Center(child: Text('Error loading question'));
    
    final correctOptionIndex = questionAsync.value!.correctOptionIndex;
    
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.options.length,
      itemBuilder: (context, index) {
        return _OptionItem(
          optionIndex: index,
          optionText: widget.options[index],
          optionImage: index < widget.optionImages.length ? widget.optionImages[index] : '',
          optionImageWidth: index < widget.optionImagesWidth.length ? widget.optionImagesWidth[index] : null,
          optionImageHeight: index < widget.optionImagesHeight.length ? widget.optionImagesHeight[index] : null,
          isSelected: widget.selectedIndex == index,
          isFeedbackMode: testModeNotifier.isFeedbackMode,
          isIncorrectAttempt: testModeNotifier.isFeedbackMode && 
                              (progressAsync.valueOrNull?.incorrectAttempts[widget.questionIndex]?.contains(index) ?? false),
          correctOptionIndex: correctOptionIndex,
          tappedOptionIndex: _tappedOptionIndex,
          onTap: () {
            setState(() {
              _tappedOptionIndex = index;
            });
            widget.onSelect(index);
          },
        );
      },
    );
  }
}

class _OptionItem extends StatelessWidget {
  final int optionIndex;
  final String optionText;
  final String optionImage;
  final double? optionImageWidth;
  final double? optionImageHeight;
  final bool isSelected;
  final bool isFeedbackMode;
  final bool isIncorrectAttempt;
  final int correctOptionIndex;
  final int? tappedOptionIndex;
  final VoidCallback onTap;

  const _OptionItem({
    required this.optionIndex,
    required this.optionText,
    required this.optionImage,
    this.optionImageWidth,
    this.optionImageHeight,
    required this.isSelected,
    required this.isFeedbackMode,
    required this.isIncorrectAttempt,
    required this.correctOptionIndex,
    this.tappedOptionIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final optionLabel = String.fromCharCode(65 + optionIndex); // A, B, C, D...
    
    // Determine if this option was just tapped
    final wasJustTapped = tappedOptionIndex == optionIndex;
    final isCorrectOption = optionIndex == correctOptionIndex;
    
    // Default styling - set first
    Color backgroundColor = Colors.transparent;
    Color borderColor = Theme.of(context).colorScheme.outline.withAlpha(128);
    Color circleColor = Theme.of(context).colorScheme.surfaceContainerHighest;
    Color textColor = Theme.of(context).colorScheme.onSurface;
    Widget? trailingIcon;
    
    // Regular selection styling (for non-feedback modes)
    if (isSelected) {
      backgroundColor = Theme.of(context).colorScheme.primaryContainer;
      borderColor = Theme.of(context).colorScheme.primary;
      circleColor = Theme.of(context).colorScheme.primary;
      textColor = Theme.of(context).colorScheme.onPrimary;
    }
    
    // Previously incorrect attempt in feedback mode
    if (isIncorrectAttempt) {
      backgroundColor = getAppColor(context).incorrectAnswerBackgroundColor;
      borderColor = getAppColor(context).incorrectAnswerColor;
      circleColor = getAppColor(context).incorrectAnswerColor;
      textColor = Colors.white;
      trailingIcon = Icon(
        Icons.cancel,
        color: getAppColor(context).incorrectAnswerColor,
      );
    }
    
    // User tapped incorrect answer - show incorrect styling immediately
    if (wasJustTapped && !isCorrectOption) {
      backgroundColor = getAppColor(context).incorrectAnswerBackgroundColor;
      borderColor = getAppColor(context).incorrectAnswerColor;
      circleColor = getAppColor(context).incorrectAnswerColor;
      textColor = Colors.white;
      trailingIcon = Icon(
        Icons.cancel,
        color: getAppColor(context).incorrectAnswerColor,
      );
    }
    
    // User tapped correct answer - show correct styling immediately (highest priority)
    if (wasJustTapped && isCorrectOption) {
      backgroundColor = getAppColor(context).correctAnswerBackgroundColor;
      borderColor = getAppColor(context).correctAnswerColor;
      circleColor = getAppColor(context).correctAnswerColor;
      textColor = Colors.white;
      trailingIcon = Icon(
        Icons.check_circle,
        color: getAppColor(context).correctAnswerColor,
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: borderColor,
              width: (wasJustTapped || isIncorrectAttempt || isSelected) ? 2 : 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Option letter circle (same style as review screen)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: circleColor,
                ),
                child: Center(
                  child: Text(
                    optionLabel,
                    style: TextStyle(
                      color: textColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              
              // Option content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      optionText,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                    // Option image if available
                    if (optionImage.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: CachedImage(
                          key: ValueKey('option-image-$optionIndex'),
                          imageWidth: optionImageWidth,
                          imageHeight: optionImageHeight,
                          storagePath: optionImage,
                          errorWidget: const SizedBox.shrink(),
                        ),
                      ),
                  ],
                ),
              ),
              
              // Show appropriate icon based on state
              if (trailingIcon != null) trailingIcon,
            ],
          ),
        ),
      ),
    );
  }
}
