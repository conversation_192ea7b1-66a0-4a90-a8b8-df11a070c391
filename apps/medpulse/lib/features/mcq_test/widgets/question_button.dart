import 'package:flutter/material.dart';

import '../../../theme/app_theme.dart';

/// A reusable button for displaying question indicators
class QuestionButton extends StatelessWidget {
  final int index;
  final bool isCurrent;
  final bool isAnswered;
  final bool isMarkedForReview;
  final bool isBookmarked;
  final bool isLocked;
  final bool isDialog;
  final VoidCallback onTap;
  final VoidCallback? onLockedTap;
  final double size;
  final double fontSize;
  final double bookmarkSize;

  const QuestionButton({
    super.key,
    required this.index,
    required this.isCurrent,
    required this.isAnswered,
    required this.isMarkedForReview,
    required this.isBookmarked,
    required this.isLocked,
    required this.onTap,
    required this.isDialog,
    this.onLockedTap,
    this.size = 32,
    this.fontSize = 12,
    this.bookmarkSize = 8,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (isLocked) {
          onLockedTap?.call();
        } else {
          onTap();
        }
      },
      borderRadius: BorderRadius.circular(size / 2),
      child: Container(
        width: size,
        height: size,
        margin: const EdgeInsets.all(4.0),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _getBackgroundColor(context),
          border: isCurrent
              ? Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                )
              : null,
        ),
        child: Stack(
          children: [
            // Question number
            Center(
              child: Text(
                '${index + 1}',
                style: TextStyle(
                  color: _getTextColor(context),
                  fontWeight: FontWeight.bold,
                  fontSize: fontSize,
                ),
              ),
            ),

            // Bookmark indicator
            if (isBookmarked)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: bookmarkSize,
                  height: bookmarkSize,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.blue,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    if (isLocked) {
      return Theme.of(context).colorScheme.tertiary;
    } else if (isMarkedForReview) {
      return getAppColor(context).markedForReviewBackground;
    } else if (isAnswered) {
      return Theme.of(context).colorScheme.primary;
    } else {
      return isDialog ? getAppColor(context).unansweredDialogBackground : getAppColor(context).unansweredBackground;
    }
  }

  Color _getTextColor(BuildContext context) {
    if (isLocked) {
      return Theme.of(context).colorScheme.onTertiary;
    } else if (isMarkedForReview) {
      return getAppColor(context).markedForReviewText;
    } else if (isAnswered) {
      return Theme.of(context).colorScheme.onPrimary;
    } else {
      return isDialog ? getAppColor(context).unansweredDialogText : getAppColor(context).unansweredText;
    }
  }
}
