import 'package:flutter/material.dart';
import 'package:entities/test_progress.dart';
import 'question_review_item.dart';

/// Widget for displaying the content of answers review
class AnswersReviewContent extends StatelessWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final TestProgress progress;
  final int questionCount;
  final AnswerFilterType filterType;
  const AnswersReviewContent({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.progress,
    required this.questionCount,
    required this.filterType,
  });

  @override
  Widget build(BuildContext context) {
    // Build the filtered list of question indices based on the selected filter
    final reviewList = _getFilteredQuestionIndices();

    if (reviewList.isEmpty) {
      return Center(
        key: const ValueKey('empty_review_state'),
        child: Text(
          'No ${filterType.toString().split('.').last} answers',
          style: Theme.of(context).textTheme.titleMedium,
        ),
      );
    }

    return ListView.separated(
      key: const <PERSON><PERSON><PERSON>('answers_review_list'),
      itemCount: reviewList.length,
      separatorBuilder: (context, index) => Divider(
        key: ValueKey('divider_$index'),
        height: 30,
        thickness: 1
      ),
      itemBuilder: (context, index) {
        // Use the question index from our filtered list
        final questionIndex = reviewList[index];
        return QuestionReviewItem(
          key: ValueKey('question_review_$questionIndex'),
          subscriptionId: subscriptionId,
          testId: testId,
          free: free,
          index: questionIndex,
          userAnswers: progress.userAnswers,
        );
      },
    );
  }

  // Helper method to get filtered question indices
  List<int> _getFilteredQuestionIndices() {
    final List<int> indices = [];

    // Check each question against our filter criteria
    for (int i = 0; i < questionCount; i++) {
      if (progress.answersCorrectness.length > i && progress.answersCorrectness[i] == filterType) {
        indices.add(i);
      }
    }

    return indices;
  }
}
