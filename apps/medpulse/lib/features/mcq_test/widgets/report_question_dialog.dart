import 'package:flutter/material.dart';
import 'package:providers/common.dart';

/// Types of question issues that can be reported
enum QuestionIssueType {
  incorrect('The answer is incorrect'),
  unclear('The question is unclear'),
  typo('There is a typo or grammar error'),
  duplicate('This is a duplicate question'),
  other('Other issue');
  
  final String label;
  const QuestionIssueType(this.label);
}

/// Dialog for reporting issues with questions
class ReportQuestionDialog {
  static Future<void> show({
    required BuildContext context, 
    required String testId,
    required int questionIndex,
    required Function(String testId, int questionIndex, QuestionIssueType issueType, String comments) onSubmit,
  }) {
    return showDialog(
      context: context,
      builder: (context) => _ReportQuestionDialog(
        testId: testId,
        questionIndex: questionIndex,
        onSubmit: onSubmit,
      ),
    );
  }
}

class _ReportQuestionDialog extends StatefulWidget {
  final String testId;
  final int questionIndex;
  final Function(String testId, int questionIndex, QuestionIssueType issueType, String comments) onSubmit;
  
  const _ReportQuestionDialog({
    required this.testId,
    required this.questionIndex,
    required this.onSubmit,
  });

  @override
  State<_ReportQuestionDialog> createState() => _ReportQuestionDialogState();
}

class _ReportQuestionDialogState extends State<_ReportQuestionDialog> {
  QuestionIssueType _selectedIssueType = QuestionIssueType.incorrect;
  final TextEditingController _commentsController = TextEditingController();
  bool _isSubmitting = false;
  
  @override
  void dispose() {
    _commentsController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Report Question'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Question ${widget.questionIndex + 1}'),
            const SizedBox(height: 16),
            
            const Text('What is the issue with this question?'),
            const SizedBox(height: 8),
            
            // Radio options
            ...QuestionIssueType.values.map((type) => 
              RadioListTile<QuestionIssueType>(
                title: Text(type.label),
                value: type,
                groupValue: _selectedIssueType,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedIssueType = value;
                    });
                  }
                },
                dense: true,
              ),
            ),
            
            const SizedBox(height: 16),
            TextField(
              controller: _commentsController,
              decoration: const InputDecoration(
                labelText: 'Additional comments (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isSubmitting ? null : _submitReport,
          child: _isSubmitting 
            ? const SizedBox(
                width: 20, 
                height: 20, 
                child: CircularProgressIndicator(strokeWidth: 2)
              )
            : const Text('Submit'),
        ),
      ],
    );
  }
  
  Future<void> _submitReport() async {
    if (_isSubmitting) return;
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      await widget.onSubmit(
        widget.testId,
        widget.questionIndex,
        _selectedIssueType,
        _commentsController.text,
      );
      
      if (!mounted) return;
      
      Navigator.of(context).pop();
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Thank you for your feedback!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      dbgPrint('Error submitting report: $e');
      
      if (!mounted) return;
      
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to submit report: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}