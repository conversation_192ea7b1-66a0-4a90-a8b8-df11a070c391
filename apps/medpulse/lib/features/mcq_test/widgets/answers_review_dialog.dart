import 'package:flutter/material.dart';
import 'package:entities/test_progress.dart';
import 'answers_review_content.dart';


/// Widget for the answers review dialog
class AnswersReviewDialog extends StatefulWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final TestProgress progress;
  final int questionCount;
  const AnswersReviewDialog({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.progress,
    required this.questionCount,
  });

  @override
  State<AnswersReviewDialog> createState() => _AnswersReviewDialogState();

  /// Show the answers review dialog
  static Future<void> show({
    required BuildContext context,
    required String subscriptionId,
    required String testId,
    required bool free,
    required TestProgress progress,
    required int questionCount,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return AnswersReviewDialog(
          subscriptionId: subscriptionId,
          testId: testId,
          free: free,
          progress: progress,
          questionCount: questionCount,
        );
      },
    );
  }
}

class _AnswersReviewDialogState extends State<AnswersReviewDialog> {
  // Default to showing incorrect answers
  AnswerFilterType _selectedFilter = AnswerFilterType.incorrect;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 600, // Maximum width constraint
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Dialog header
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Text(
                    'Answer Review',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Filter selection
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildFilterChip(AnswerFilterType.incorrect, 'Incorrect'),
                  _buildFilterChip(AnswerFilterType.correct, 'Correct'),
                  _buildFilterChip(AnswerFilterType.skipped, 'Skipped'),
                ],
              ),
            ),

            const Divider(),

            // Expanded list with answers
            Expanded(
              child: AnswersReviewContent(
                subscriptionId: widget.subscriptionId,
                testId: widget.testId,
                free: widget.free,
                progress: widget.progress,
                questionCount: widget.questionCount,
                filterType: _selectedFilter,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(AnswerFilterType type, String label) {
    return ChoiceChip(
      label: Text(label),
      selected: _selectedFilter == type,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedFilter = type;
          });
        }
      },
    );
  }
}