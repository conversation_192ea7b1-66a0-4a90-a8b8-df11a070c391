import 'package:entities/question_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/screens/error_screen.dart';
import 'package:providers/common.dart';
import '../../../providers/question_provider.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/cached_image.dart';
import '../providers/test_progress_provider.dart';
import 'question_options.dart';
import 'flip_card_widget.dart';

/// Widget that displays a question and its options
class QuestionView extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final int questionIndex;
  final int? selectedAnswer;
  final Function(int) onSelect;
  final ContentType questionType;
  final Function(BuildContext)? onRevealedAnswerTap;
  final VoidCallback? onNextQuestion;

  const QuestionView({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.questionIndex,
    this.selectedAnswer,
    required this.onSelect,
    required this.questionType,
    this.onRevealedAnswerTap,
    this.onNextQuestion,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the question provider to fetch and decode the current question on-demand
    final questionAsync = ref.watch(questionProvider(subscriptionId, testId, questionIndex, free: free));

    // Check if this answer has been revealed
    final isRevealed = ref.watch(testProgressNotifierProvider(subscriptionId, testId, free: free)).whenOrNull(
      data: (progress) => progress.revealedAnswers.contains(questionIndex),
    ) ?? false;
    if (questionAsync.hasError) {
      dbgPrint('Error loading question: ${questionAsync.error}\n${questionAsync.stackTrace}');
    }

    return questionAsync.when(
      data: (question) => _buildQuestionContent(context, question, isRevealed),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => ErrorScreen(context: context, description: 'There was a problem loading the question.', onPressed: () {
        ref.invalidate(questionProvider(subscriptionId, testId, questionIndex, free: free));
      }),
    );
  }

  Widget _buildQuestionContent(BuildContext context, QuestionEntity question, bool isRevealed) {
    // Verify that the question type matches what we expect
    if (question.type != questionType) {
      return Center(
        child: Text('Error: Expected ${questionType.name} question but got ${question.type.name}'),
      );
    }

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Content based on question type
            if (questionType == ContentType.mcq) ...[
              // Question number and text for MCQ
              Text(
                'Question ${questionIndex + 1}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                question.question,
                style: Theme.of(context).textTheme.bodyLarge,
              ),

              // Question image if available for MCQ
              if (question.questionImage != null && question.questionImage!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  child: Center(
                    child: CachedImage(
                      key: ValueKey('question-view-${question.questionImage!}'),
                      imageWidth: question.questionImageWidth,
                      imageHeight: question.questionImageHeight,
                      storagePath: question.questionImage!,
                      errorWidget: const Icon(
                        Icons.image_not_supported,
                        size: 64,
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 24),
              
              // Show warning if answer has been revealed
              if (isRevealed)
                _buildRevealedWarning(context),

              // MCQ Options
              OptionsWidget(
                options: question.options,
                optionImages: question.optionImages,
                optionImagesWidth: question.optionImagesWidth,
                optionImagesHeight: question.optionImagesHeight,
                selectedIndex: selectedAnswer,
                subscriptionId: subscriptionId,
                testId: testId,
                free: free,
                questionIndex: questionIndex,
                onSelect: (index) {
                  if (isRevealed) {
                    // Show dialog explaining why they can't answer
                    _showRevealedAnswerDialog(context);
                  } else {
                    // Normal selection
                    onSelect(index);
                  }
                },
              )
            ] else if (questionType == ContentType.flipCard) ...[
              // FlipCard - question and answer are both inside the card
              _buildFlipCardAnswer(context, question),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFlipCardAnswer(BuildContext context, QuestionEntity question) {
    return FlipCardWidget(
      question: question,
      questionIndex: questionIndex,
      onAnswerSelect: onSelect,
      onNextQuestion: onNextQuestion,
    );
  }

  // The _showAnswerDialog method has been moved to TestContentScreen

  // Widget to show a warning when an answer has been revealed
  Widget _buildRevealedWarning(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: getAppColor(context).incorrectAnswerBackgroundColor.withAlpha(51),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: getAppColor(context).incorrectAnswerColor.withAlpha(128)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: getAppColor(context).incorrectAnswerColor),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'You have revealed the answer for this question. You cannot change your answer now.',
              style: TextStyle(color: getAppColor(context).incorrectAnswerColor),
            ),
          ),
        ],
      ),
    );
  }

  // Dialog to show when a user tries to answer after revealing
  void _showRevealedAnswerDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cannot Change Answer'),
        content: const Text(
          'You have already revealed the answer for this question. '
          'Once an answer is revealed, you cannot change your answer.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}