import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/test_progress.dart';
import 'answers_review_dialog.dart';
import 'score_details.dart';

/// Widget displaying test results
class TestResultsScreen extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final TestProgress progress;
  final int questionCount;
  final int? testDurationMinutes;

  const TestResultsScreen({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.progress,
    required this.questionCount,
    this.testDurationMinutes,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if result is available
    if (progress.result == null) {
      return const Center(child: CircularProgressIndicator());
    }

    // Use the result from TestProgress
    final result = progress.result!;
    final resultMessage = result.getResultMessage();

    // Determine color based on score percentage
    final Color resultColor = _getResultColor(result.scorePercentage);

    return Center(
      child: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(24.0),
          constraints: BoxConstraints(maxWidth: 700),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Result icon
              Icon(
                result.scorePercentage >= 70 ? Icons.check_circle : Icons.info,
                size: 80,
                color: resultColor,
              ),
              const SizedBox(height: 24),
        // Score heading
              Text(
                resultMessage,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: resultColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Score details
              ScoreDetails(
                testResult: result,
                testDurationMinutes: testDurationMinutes,
              ),

              const SizedBox(height: 32),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Back to Tests'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton.icon(
                    onPressed: () {
                      _showAnswersDialog(context, ref);
                    },
                    icon: const Icon(Icons.assignment),
                    label: const Text('Review Answers'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }



  // Helper method to get result color based on score
  Color _getResultColor(double scorePercentage) {
    if (scorePercentage >= 90) {
      return Colors.green;
    } else if (scorePercentage >= 70) {
      return Colors.blue;
    } else if (scorePercentage >= 50) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  void _showAnswersDialog(BuildContext context, WidgetRef ref) {
    AnswersReviewDialog.show(
      context: context,
      subscriptionId: subscriptionId,
      testId: testId,
      free: free,
      progress: progress,
      questionCount: questionCount,
    );
  }
}