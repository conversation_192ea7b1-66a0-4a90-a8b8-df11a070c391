import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';

/// Widget for displaying timer in the test screen
class TestTimerWidget extends ConsumerStatefulWidget {
  final DateTime startTime;
  final bool isCompleted;
  final int? testDurationMinutes;

  const TestTimerWidget({
    super.key,
    required this.startTime,
    this.isCompleted = false,
    this.testDurationMinutes,
  });

  @override
  ConsumerState<TestTimerWidget> createState() => _TestTimerWidgetState();
}

class _TestTimerWidgetState extends ConsumerState<TestTimerWidget> {
  late DateTime _startTime;
  Duration _elapsed = Duration.zero;
  bool _hasShownTimeUpDialog = false;

  @override
  void initState() {
    super.initState();
    _startTime = widget.startTime;
    dbgPrint('TestTimer: Initialized with duration: ${widget.testDurationMinutes} minutes');
    _startTimer();
  }

  @override
  void didUpdateWidget(TestTimerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the test was just completed, update the UI one last time
    if (widget.isCompleted && !oldWidget.isCompleted) {
      setState(() {
        _elapsed = DateTime.now().difference(_startTime);
      });
    }
    // Reset dialog flag if test duration changed
    if (widget.testDurationMinutes != oldWidget.testDurationMinutes) {
      _hasShownTimeUpDialog = false;
    }
  }

  void _startTimer() {
    // Don't start a new timer if the test is completed
    if (widget.isCompleted) {
      return;
    }

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        // Check again if the test is completed before updating
        if (widget.isCompleted) {
          return;
        }

        setState(() {
          _elapsed = DateTime.now().difference(_startTime);
        });
        
        // Check if time is up for tests with duration
        if (widget.testDurationMinutes != null && !_hasShownTimeUpDialog) {
          final testDuration = Duration(minutes: widget.testDurationMinutes!);
          if (_elapsed >= testDuration) {
            _hasShownTimeUpDialog = true;
            dbgPrint('TestTimer: Time limit reached, showing time up dialog');
            _showTimeUpDialog();
          }
        }
        
        _startTimer();
      }
    });
  }

  void _showTimeUpDialog() {
    if (!mounted) return;
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Time\'s Up!'),
        content: const Text(
          'The allocated time for this test has ended. You can continue in overtime mode or finish the test now.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue (Overtime)'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate back to close the test
              Navigator.of(context).pop();
            },
            child: const Text('Finish Test'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours.abs());
    final minutes = twoDigits(duration.inMinutes.remainder(60).abs());
    final seconds = twoDigits(duration.inSeconds.remainder(60).abs());
    return '${duration.isNegative ? '-' : ''}$hours:$minutes:$seconds';
  }

  Duration _getDisplayDuration() {
    if (widget.testDurationMinutes == null) {
      // Count up mode: show elapsed time
      return _elapsed;
    } else {
      // Countdown mode: show remaining time (can be negative for overtime)
      final testDuration = Duration(minutes: widget.testDurationMinutes!);
      return testDuration - _elapsed;
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayDuration = _getDisplayDuration();
    final isOvertime = widget.testDurationMinutes != null && displayDuration.isNegative;
    
    return Padding(
      padding: const EdgeInsets.only(right: 16.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.testDurationMinutes == null 
                ? Icons.timer_outlined 
                : (isOvertime ? Icons.timer_off_outlined : Icons.hourglass_empty_outlined),
            size: 18,
            color: isOvertime ? Colors.red : null,
          ),
          const SizedBox(width: 4),
          Text(
            _formatDuration(displayDuration),
            style: TextStyle(
              color: isOvertime ? Colors.red : null,
              fontWeight: isOvertime ? FontWeight.bold : null,
            ),
          ),
        ],
      ),
    );
  }
}