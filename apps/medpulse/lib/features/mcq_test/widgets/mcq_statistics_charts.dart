import 'package:entities/question_stats_entity.dart';
import 'package:entities/test_progress.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/question_entity.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../providers/question_provider.dart';
import '../../../providers/test_provider.dart';
import '../../../theme/app_theme.dart';
import '../providers/test_progress_provider.dart';

/// Enhanced MCQ Statistics section with three visual charts
class MCQStatisticsSection extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;

  const MCQStatisticsSection({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progress = ref
        .watch(
          testProgressNotifierProvider(subscriptionId, testId, free: free),
        )
        .valueOrNull;
    final statsAsync = ref.watch(testStatsProvider(subscriptionId, testId));

    if (progress == null) return const SizedBox();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'MCQ Statistics',
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 16),
          statsAsync.when(
            data: (stats) => MCQChartsDisplay(
              stats: stats[progress.currentIndex],
              progress: progress,
              subscriptionId: subscriptionId,
              testId: testId,
              free: free,
            ),
            loading: () => const Padding(
              padding: EdgeInsets.all(32),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (_, __) => const Padding(
              padding: EdgeInsets.all(16),
              child: Text('Statistics will be available after more responses'),
            ),
          ),
        ],
      ),
    );
  }
}

/// Main display widget with three charts
class MCQChartsDisplay extends ConsumerWidget {
  final QuestionStatsEntity? stats;
  final TestProgress progress;
  final String subscriptionId;
  final String testId;
  final bool free;

  const MCQChartsDisplay({
    super.key,
    required this.stats,
    required this.progress,
    required this.subscriptionId,
    required this.testId,
    required this.free,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (stats == null) {
      return const Text('Statistics will be available after more responses');
    }

    final questionAsync = ref.watch(
      questionProvider(
        subscriptionId,
        testId,
        progress.currentIndex,
        free: free,
      ),
    );

    return questionAsync.when(
      data: (question) {
        return Column(
          children: [
            // 1. First-Attempt Choices (Bar Chart) - Categorical data comparison
            FirstAttemptChoicesChart(
              stats: stats!,
              question: question,
            ),
            const SizedBox(height: 24),

            // 2. Personal Performance Timeline (Line Chart) - Continuous data over time
            PersonalPerformanceChart(
              progress: progress,
              questionIndex: progress.currentIndex,
            ),
            const SizedBox(height: 24),

            // 3. Total Attempts & Success Rate (Pie Chart) - Parts of a whole
            TotalAttemptsAndSuccessRateChart(
              stats: stats!,
              totalAttempts: stats!.totalAttempts,
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Text('Unable to load question statistics'),
    );
  }
}

/// Section 1: Total Attempts & Success Rate with Pie Chart
class TotalAttemptsAndSuccessRateChart extends StatefulWidget {
  final QuestionStatsEntity stats;
  final int totalAttempts;

  const TotalAttemptsAndSuccessRateChart({
    super.key,
    required this.stats,
    required this.totalAttempts,
  });

  @override
  State<TotalAttemptsAndSuccessRateChart> createState() =>
      _TotalAttemptsAndSuccessRateChartState();
}

class _TotalAttemptsAndSuccessRateChartState
    extends State<TotalAttemptsAndSuccessRateChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final firstAttemptRate = widget.stats.getSuccessRateByPosition(1);
    final secondAttemptRate = widget.stats.getSuccessRateByPosition(2);
    final thirdAttemptRate = widget.stats.getSuccessRateByPosition(3);
    final appColors = getAppColor(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📊 Total Attempts: ${widget.totalAttempts.toString().replaceAllMapped(RegExp(r'\d{1,3}(?=(\d{3})+(?!\d))'), (match) => '${match[0]},')}',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimaryContainer,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // Pie Chart
              SizedBox(
                height: 160,
                width: 160,
                child: AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    return PieChart(
                      PieChartData(
                        pieTouchData: PieTouchData(
                          touchCallback: (FlTouchEvent event, pieTouchResponse) {
                            setState(() {
                              if (!event.isInterestedForInteractions ||
                                  pieTouchResponse == null ||
                                  pieTouchResponse.touchedSection == null) {
                                touchedIndex = -1;
                                return;
                              }
                              touchedIndex = pieTouchResponse
                                  .touchedSection!.touchedSectionIndex;
                            });
                          },
                        ),
                        borderData: FlBorderData(show: false),
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                        sections: _buildPieChartSections(
                          firstAttemptRate,
                          secondAttemptRate,
                          thirdAttemptRate,
                          _animation.value,
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 24),
              // Legend
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLegendItem(
                      '1st Attempt',
                      '${firstAttemptRate.round()}%',
                      const Color(0xFF4CAF50),
                      widget.stats.correctOnFirstAttempt,
                    ),
                    const SizedBox(height: 8),
                    _buildLegendItem(
                      '2nd Attempt',
                      '${secondAttemptRate.round()}%',
                      const Color(0xFFFFC107),
                      widget.stats.correctOnSecondAttempt,
                    ),
                    const SizedBox(height: 8),
                    _buildLegendItem(
                      '3rd Attempt',
                      '${thirdAttemptRate.round()}%',
                      const Color(0xFFFF9800),
                      widget.stats.correctOnThirdAttempt,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(
    double firstRate,
    double secondRate,
    double thirdRate,
    double animationValue,
  ) {
    return [
      PieChartSectionData(
        color: const Color(0xFF4CAF50),
        value: firstRate * animationValue,
        title: '${firstRate.round()}%',
        radius: touchedIndex == 0 ? 65 : 55,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: const Color(0xFFFFC107),
        value: secondRate * animationValue,
        title: '${secondRate.round()}%',
        radius: touchedIndex == 1 ? 65 : 55,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: const Color(0xFFFF9800),
        value: thirdRate * animationValue,
        title: '${thirdRate.round()}%',
        radius: touchedIndex == 2 ? 65 : 55,
        titleStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    ];
  }

  Widget _buildLegendItem(
      String label, String percentage, Color color, int count) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            '$label: $percentage ($count)',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}

/// Section 2: Personal Performance with Line Chart
class PersonalPerformanceChart extends StatefulWidget {
  final TestProgress progress;
  final int questionIndex;

  const PersonalPerformanceChart({
    super.key,
    required this.progress,
    required this.questionIndex,
  });

  @override
  State<PersonalPerformanceChart> createState() =>
      _PersonalPerformanceChartState();
}

class _PersonalPerformanceChartState extends State<PersonalPerformanceChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get user's attempt data for this question
    final userIncorrectAttempts =
        widget.progress.incorrectAttempts[widget.questionIndex] ?? <int>{};
    final userAttemptPosition = userIncorrectAttempts.length + 1;
    final userWasCorrect =
        widget.progress.answersCorrectness.length > widget.questionIndex &&
            widget.progress.answersCorrectness[widget.questionIndex] ==
                AnswerFilterType.correct;

    // Create attempt history (simulate last 3 + current)
    final attemptData = _generateAttemptHistory(userAttemptPosition, userWasCorrect);
    final appColors = getAppColor(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: appColors.excellentScore.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: appColors.excellentScore.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '👤 Your Performance Timeline',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green.shade800,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 120,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return LineChart(
                  LineChartData(
                    gridData: const FlGridData(show: false),
                    titlesData: FlTitlesData(
                      leftTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      topTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: const AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                attemptData[value.toInt()]['label'] as String,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    borderData: FlBorderData(show: false),
                    minX: 0,
                    maxX: (attemptData.length - 1).toDouble(),
                    minY: -0.5,
                    maxY: 1.5,
                    lineBarsData: [
                      LineChartBarData(
                        spots: attemptData
                            .asMap()
                            .entries
                            .map((entry) => FlSpot(
                                  entry.key.toDouble(),
                                  ((entry.value['isCorrect'] as bool) ? 1.0 : 0.0) *
                                      _animation.value,
                                ))
                            .toList(),
                        isCurved: true,
                        color: Colors.blue.shade600,
                        barWidth: 3,
                        isStrokeCapRound: true,
                        dotData: FlDotData(
                          show: true,
                          getDotPainter: (spot, percent, barData, index) {
                            final isCorrect = attemptData[index]['isCorrect'] as bool;
                            final isCurrent = attemptData[index]['isCurrent'] as bool;
                            return FlDotCirclePainter(
                              radius: isCurrent ? 8 : 6,
                              color: isCorrect ? Colors.green : Colors.red,
                              strokeWidth: isCurrent ? 3 : 2,
                              strokeColor: Colors.white,
                            );
                          },
                        ),
                        belowBarData: BarAreaData(show: false),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '📈 Your Success Rate: ${userWasCorrect ? 'Correct' : 'Attempting'} on ${_getOrdinal(userAttemptPosition)} try',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.green.shade700,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _generateAttemptHistory(int currentPosition, bool wasCorrect) {
    // Generate simulated attempt history for visualization
    final List<Map<String, dynamic>> attempts = [];
    
    // Add previous attempts (simulated)
    for (int i = 1; i < currentPosition; i++) {
      attempts.add({
        'label': '${i == 1 ? '3' : i == 2 ? '2' : '1'} ${i == 1 ? 'days' : i == 2 ? 'days' : 'day'} ago',
        'isCorrect': false,
        'isCurrent': false,
      });
    }
    
    // Add current attempt
    attempts.add({
      'label': 'Now',
      'isCorrect': wasCorrect,
      'isCurrent': true,
    });
    
    // Ensure we always have at least 4 data points for better visualization
    while (attempts.length < 4) {
      attempts.insert(0, {
        'label': '${attempts.length + 1} days ago',
        'isCorrect': false,
        'isCurrent': false,
      });
    }
    
    return attempts.take(4).toList();
  }

  String _getOrdinal(int number) {
    switch (number) {
      case 1:
        return '1st';
      case 2:
        return '2nd';
      case 3:
        return '3rd';
      default:
        return '${number}th';
    }
  }
}

/// Section 3: First-Attempt Choices with Bar Chart
class FirstAttemptChoicesChart extends StatefulWidget {
  final QuestionStatsEntity stats;
  final QuestionEntity question;

  const FirstAttemptChoicesChart({
    super.key,
    required this.stats,
    required this.question,
  });

  @override
  State<FirstAttemptChoicesChart> createState() =>
      _FirstAttemptChoicesChartState();
}

class _FirstAttemptChoicesChartState extends State<FirstAttemptChoicesChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the maximum percentage for scaling
    double maxPercentage = 0;
    for (int i = 0; i < widget.question.options.length; i++) {
      final percentage = widget.stats.getFirstAttemptPercentage(i.toString());
      if (percentage > maxPercentage) maxPercentage = percentage;
    }
    if (maxPercentage == 0) maxPercentage = 1;
    
    final appColors = getAppColor(context);
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🎯 What Students Chose First',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onTertiaryContainer,
            ),
          ),
          const SizedBox(height: 16),
          // Bar Chart
          SizedBox(
            height: 120,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    maxY: maxPercentage * 1.1,
                    barTouchData: BarTouchData(
                      touchCallback: (FlTouchEvent event, barTouchResponse) {
                        setState(() {
                          if (!event.isInterestedForInteractions ||
                              barTouchResponse == null ||
                              barTouchResponse.spot == null) {
                            touchedIndex = -1;
                            return;
                          }
                          touchedIndex = barTouchResponse.spot!.touchedBarGroupIndex;
                        });
                      },
                    ),
                    titlesData: const FlTitlesData(show: false),
                    borderData: FlBorderData(show: false),
                    barGroups: widget.question.options.asMap().entries.map((entry) {
                      final index = entry.key;
                      final percentage = widget.stats.getFirstAttemptPercentage(index.toString());
                      final isCorrect = index == widget.question.correctOptionIndex;
                      final isTouched = index == touchedIndex;
                      
                      return BarChartGroupData(
                        x: index,
                        barRods: [
                          BarChartRodData(
                            toY: percentage * _animation.value,
                            color: isCorrect ? Colors.green : Colors.purple.shade400,
                            width: isTouched ? 24 : 20,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(6),
                              topRight: Radius.circular(6),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          // Simple Legend
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: widget.question.options.asMap().entries.map((entry) {
              final index = entry.key;
              final percentage = widget.stats.getFirstAttemptPercentage(index.toString());
              final isCorrect = index == widget.question.correctOptionIndex;
              
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isCorrect
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isCorrect ? Colors.green : Colors.purple,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: isCorrect
                            ? Colors.green.shade700
                            : Colors.purple.shade700,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${percentage.round()}%',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
