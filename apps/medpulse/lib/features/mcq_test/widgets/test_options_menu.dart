import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../theme/app_theme.dart';
import '../../../widgets/upgrade_dialog.dart';

/// Menu for test options
class TestOptionsMenu {
  /// Show the test options menu
  static Future<void> show({
    required BuildContext context,
    required WidgetRef ref,
    required int currentQuestionIndex,
    required bool isMarkedForReview,
    required bool isBookmarked,
    required bool isPremiumUser,
    required VoidCallback onFinishTest,
    required VoidCallback onToggleMarkForReview,
    required VoidCallback onToggleBookmark,
    required VoidCallback onReportQuestion,
  }) {
    return showModalBottomSheet(
      context: context,
      builder:
          (context) => _TestOptionsBottomSheet(
            currentQuestionIndex: currentQuestionIndex,
            isMarkedForReview: isMarkedForReview,
            isBookmarked: isBookmarked,
            isPremiumUser: isPremiumUser,
            onFinishTest: () {
              Navigator.of(context).pop();
              onFinishTest();
            },
            onToggleMarkForReview: () {
              Navigator.of(context).pop();
              onToggleMarkForReview();
            },
            onToggleBookmark: () {
              Navigator.of(context).pop();
              onToggleBookmark();
            },
            onReportQuestion: () {
              Navigator.of(context).pop();
              onReportQuestion();
            },
          ),
    );
  }
}

class _TestOptionsBottomSheet extends ConsumerWidget {
  final int currentQuestionIndex;
  final bool isMarkedForReview;
  final bool isBookmarked;
  final bool isPremiumUser;
  final VoidCallback onFinishTest;
  final VoidCallback onToggleMarkForReview;
  final VoidCallback onToggleBookmark;
  final VoidCallback onReportQuestion;

  const _TestOptionsBottomSheet({
    required this.currentQuestionIndex,
    required this.isMarkedForReview,
    required this.isBookmarked,
    required this.isPremiumUser,
    required this.onFinishTest,
    required this.onToggleMarkForReview,
    required this.onToggleBookmark,
    required this.onReportQuestion,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title bar
          Container(
            width: 40,
            height: 5,
            margin: const EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(color: Colors.grey.shade300, borderRadius: BorderRadius.circular(8)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Text('Test Options', style: Theme.of(context).textTheme.titleLarge),
          ),
          const Divider(),

          ListTile(
            leading: const Icon(Icons.check_circle_outline, color: Colors.green),
            title: const Text('Finish test'),
            subtitle: const Text('Submit your answers and view results'),
            onTap: onFinishTest,
          ),

          const Divider(),

          ListTile(
            leading: const Icon(Icons.report_problem_outlined),
            title: const Text('Report question'),
            subtitle: const Text('Report an issue with this question'),
            onTap: onReportQuestion,
          ),

          ListTile(
            leading: const Icon(Icons.bookmark_border),
            title: Text(isBookmarked ? 'Remove bookmark' : 'Bookmark question'),
            subtitle: Text(
              isPremiumUser ? 'Save for review outside the test' : 'Premium feature - save for later review',
            ),
            trailing:
                isBookmarked
                    ? const Icon(Icons.bookmark, color: Colors.blue)
                    : isPremiumUser
                    ? null
                    : const Icon(Icons.lock_outline, color: Colors.grey),
            onTap: isPremiumUser ? onToggleBookmark : _showPremiumDialog(context, ref),
          ),

          ListTile(
            leading: const Icon(Icons.flag_outlined),
            title: Text(isMarkedForReview ? 'Unmark for review' : 'Mark for review'),
            subtitle: const Text('Mark this question to revisit during the test'),
            trailing:
                isMarkedForReview
                    ? Icon(Icons.check_circle, color: getAppColor(context).markedForReviewBackground)
                    : null,
            onTap: onToggleMarkForReview,
          ),
        ],
      ),
    );
  }

  VoidCallback? _showPremiumDialog(BuildContext context, WidgetRef ref) {
    return isPremiumUser
        ? null
        : () {
          Navigator.of(context).pop();

          showUpgradeDialog(
            context,
            ref,
            'Bookmarking questions is a premium feature.\n'
            'Upgrade to premium to access this and other features.',
            scenario: UpgradeScenario.bookmarkFeature,
          );
        };
  }
}
