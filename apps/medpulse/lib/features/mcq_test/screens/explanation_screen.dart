import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/question_entity.dart';
import '../../../providers/question_provider.dart';
import '../../../providers/test_provider.dart';
import '../../../theme/app_theme.dart';
import '../providers/test_progress_provider.dart';
import '../widgets/mcq_statistics_charts.dart';

class ExplanationScreen extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;

  const ExplanationScreen({
    super.key,
    required this.subscriptionId,
    required this.testId,
    required this.free,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progressAsync = ref.watch(
      testProgressNotifierProvider(subscriptionId, testId, free: free),
    );

    return progressAsync.when(
      data:
          (progress) => _ExplanationView(
            subscriptionId: subscriptionId,
            testId: testId,
            free: free,
            questionIndex: progress.currentIndex,
          ),
      loading:
          () => Scaffold(
            appBar: AppBar(title: const Text('Loading...')),
            body: const Center(child: CircularProgressIndicator()),
          ),
      error:
          (error, _) => Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(child: Text('Error: ${error.toString()}')),
          ),
    );
  }
}

/// Main explanation view
class _ExplanationView extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;
  final int questionIndex;

  const _ExplanationView({
    required this.subscriptionId,
    required this.testId,
    required this.free,
    required this.questionIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final questionAsync = ref.watch(
      questionProvider(subscriptionId, testId, questionIndex, free: free),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('Question ${questionIndex + 1} - Explanation'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: questionAsync.when(
        data:
            (question) => _QuestionExplanation(
              question: question,
              subscriptionId: subscriptionId,
              testId: testId,
              free: free,
            ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, _) => Center(child: Text('Error: $error')),
      ),
    );
  }
}

/// Complete question explanation with clean learning-focused design
class _QuestionExplanation extends ConsumerWidget {
  final QuestionEntity question;
  final String subscriptionId;
  final String testId;
  final bool free;

  const _QuestionExplanation({
    required this.question,
    required this.subscriptionId,
    required this.testId,
    required this.free,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progress =
        ref
            .watch(
              testProgressNotifierProvider(subscriptionId, testId, free: free),
            )
            .valueOrNull;
    final incorrectCount =
        progress?.incorrectAttempts[progress.currentIndex]?.length ?? 0;
    final userAttempts = 1 + incorrectCount;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          _QuestionSection(question: question.question),
          const SizedBox(height: 24),

          // Correct Answer
          _AnswerSection(
            answer:
                question.type == ContentType.mcq &&
                        question.correctOptionIndex >= 0 &&
                        question.correctOptionIndex < question.options.length
                    ? question.options[question.correctOptionIndex]
                    : question.answer ?? '',
            attempts: userAttempts,
          ),
          const SizedBox(height: 24),

          // Explanation (if available)
          if (question.explanation?.isNotEmpty == true) ...[
            _ExplanationSection(explanation: question.explanation!),
            const SizedBox(height: 24),
          ],

          // Reference (if available)
          if (question.reference?.isNotEmpty == true) ...[
            _ReferenceSection(reference: question.reference!),
            const SizedBox(height: 24),
          ],

          // Enhanced MCQ Statistics with Charts
          MCQStatisticsSection(
            subscriptionId: subscriptionId,
            testId: testId,
            free: free,
          ),

          const SizedBox(height: 32),
          _NextButton(
            subscriptionId: subscriptionId,
            testId: testId,
            free: free,
          ),
        ],
      ),
    );
  }
}

/// Question section widget with subtle visual appeal
class _QuestionSection extends StatelessWidget {
  final String question;
  const _QuestionSection({required this.question});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.help_outline,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Question',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            question,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(height: 1.4),
          ),
        ],
      ),
    );
  }
}

/// Answer section widget - simple and clean
class _AnswerSection extends StatelessWidget {
  final String answer;
  final int attempts;

  const _AnswerSection({required this.answer, required this.attempts});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: getAppColor(context).correctAnswerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: getAppColor(context).correctAnswerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: getAppColor(context).correctAnswerColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Correct Answer',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: getAppColor(context).correctAnswerColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            answer,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }
}

/// Explanation section widget with subtle visual appeal
class _ExplanationSection extends StatelessWidget {
  final String explanation;
  const _ExplanationSection({required this.explanation});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Colors.blue.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Explanation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            explanation,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(height: 1.4),
          ),
        ],
      ),
    );
  }
}

/// Reference section widget with subtle visual appeal
class _ReferenceSection extends StatelessWidget {
  final String reference;
  const _ReferenceSection({required this.reference});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.book_outlined,
                color: Colors.indigo.shade700,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Reference',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            reference,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}


/// Next button widget
class _NextButton extends ConsumerWidget {
  final String subscriptionId;
  final String testId;
  final bool free;

  const _NextButton({
    required this.subscriptionId,
    required this.testId,
    required this.free,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final progress =
        ref
            .watch(
              testProgressNotifierProvider(subscriptionId, testId, free: free),
            )
            .valueOrNull;
    final test =
        ref.watch(testProvider(subscriptionId, testId, free: free)).valueOrNull;

    final isLastQuestion =
        (progress?.currentIndex ?? 0) >= ((test?.questions.length ?? 1) - 1);
    final buttonText = isLastQuestion ? 'Finish Test' : 'Next Question';
    final icon = isLastQuestion ? Icons.check : Icons.arrow_forward;

    return SizedBox(
      width: double.infinity,
      child: FilledButton.icon(
        onPressed: () {
          // Pop the explanation screen
          Navigator.of(context).pop();

          if (!isLastQuestion && progress != null) {
            // Navigate to next question
            final questionCount = test?.questions.length ?? 1;
            ref
                .read(
                  testProgressNotifierProvider(
                    subscriptionId,
                    testId,
                    free: free,
                  ).notifier,
                )
                .goToNextQuestion(questionCount);
          }
        },
        style: FilledButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        icon: Icon(icon),
        label: Text(buttonText),
      ),
    );
  }
}
