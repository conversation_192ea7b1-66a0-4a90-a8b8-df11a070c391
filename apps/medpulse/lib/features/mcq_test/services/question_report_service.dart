import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:providers/common.dart';
import '../widgets/report_question_dialog.dart';

/// Service for reporting question issues
class QuestionReportService {
  final FirebaseFunctions _functions;

  QuestionReportService({FirebaseFunctions? functions})
      : _functions = functions ?? FirebaseFunctions.instanceFor(region: 'asia-southeast1');

  /// Submit a report for a question
  Future<void> reportQuestion({
    required String testId,
    required int questionIndex,
    required QuestionIssueType issueType,
    String? comments,
    required int version,
    required String courseId,
    required String yearId,
    required Map<String, dynamic> questionData,
  }) async {
    try {
      // convert all timestamps in questionData to ISO 8601 format
      questionData.forEach((key, value) {
        if (value is Timestamp) {
          questionData[key] = value.toDate().toIso8601String();
        }
      });

      // make a structured debug print with line breaks
      dbgPrint('Reporting question issue:\n'
          'testId: $testId\n'
          'questionIndex: $questionIndex\n'
          'issueType: $issueType\n'
          'comments: $comments\n'
          'version: $version\n'
          'courseId: $courseId\n'
          'yearId: $yearId\n'
          'questionData: $questionData\n');

      // Call the Cloud Function to report the question
      final result = await _functions.httpsCallable('reportQuestionIssue').call({
        'testId': testId,
        'questionIndex': questionIndex,
        'issueType': issueType.name,
        'comments': comments ?? '',
        'timestamp': DateTime.now().toIso8601String(),
        'version': version,
        'courseId': courseId,
        'yearId': yearId,
        'questionData': questionData,
      });

      dbgPrint('Report submitted successfully: ${result.data}');
    } catch (e) {
      dbgPrint('Error reporting question: $e');
      throw Exception('Failed to report question: $e');
    }
  }
}