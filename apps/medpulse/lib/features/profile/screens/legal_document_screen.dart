import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:medpulse/widgets/constrained_width_app_bar.dart';

/// A screen that displays a legal document (Terms & Conditions or Privacy Policy)
/// for viewing only, without acceptance functionality.
class LegalDocumentScreen extends StatefulWidget {
  /// Creates a screen that displays a legal document.
  const LegalDocumentScreen({
    super.key,
    required this.title,
    required this.assetPath,
  });

  /// The title of the document to display.
  final String title;

  /// The asset path to the markdown file.
  final String assetPath;

  @override
  State<LegalDocumentScreen> createState() => _LegalDocumentScreenState();
}

class _LegalDocumentScreenState extends State<LegalDocumentScreen> {
  String _documentContent = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDocument();
  }

  Future<void> _loadDocument() async {
    try {
      final content = await rootBundle.loadString(widget.assetPath);
      setState(() {
        _documentContent = content;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _documentContent = 'Error loading document: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: ConstrainedWidthAppBar(
        appBar: AppBar(title: Text(widget.title)),
      ),
      body: Center(
        child: _isLoading
            ? const CircularProgressIndicator()
            : Container(
              constraints: const BoxConstraints(maxWidth: 600),
              padding: const EdgeInsets.all(16),
              child: Markdown(
                data: _documentContent,
                padding: EdgeInsets.zero,
                styleSheet: MarkdownStyleSheet(
                  h1: theme.textTheme.headlineMedium,
                  h2: theme.textTheme.titleLarge,
                  h3: theme.textTheme.titleMedium,
                  p: theme.textTheme.bodyMedium,
                ),
              ),
            ),
      ),
    );
  }
}
