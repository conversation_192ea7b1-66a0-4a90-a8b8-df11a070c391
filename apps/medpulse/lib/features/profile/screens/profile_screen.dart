import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/login_provider.dart';
import 'package:medpulse/providers/user_provider.dart';

import 'legal_document_screen.dart';
import 'transactions_screen.dart';
import 'change_display_name_screen.dart';
import '../../../widgets/header_widget.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with image and welcome message
            HeaderWidget(
              imagePath: 'assets/images/onboarding_1.png',
              title: 'Profile',
              subtitle: userAsync.when(
                data: (user) => user != null ? 'Hello, ${user.displayName}' : 'Customize your settings',
                loading: () => 'Loading profile...',
                error: (_, __) => 'Customize your settings',
              ),
            ),
            const SizedBox(height: 32),

            // Profile options
            Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 450),
                child: Column(
                  children: [
                    // Display Name Card
                    _ProfileOptionCard(
                      title: 'Display Name',
                      subtitle: 'Change how your name appears',
                      icon: Icons.person_outline,
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const ChangeDisplayNameScreen(),
                        ),
                      ),
                    ),

                    // Transactions Card
                    _ProfileOptionCard(
                      title: 'My Transactions',
                      subtitle: 'View your payment history',
                      icon: Icons.receipt_long,
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const TransactionsScreen(),
                        ),
                      ),
                    ),

                    // Terms & Conditions Card
                    _ProfileOptionCard(
                      title: 'Terms & Conditions',
                      subtitle: 'View our terms of service',
                      icon: Icons.description_outlined,
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const LegalDocumentScreen(
                            title: 'Terms & Conditions',
                            assetPath: 'assets/legal/terms-conditions.md',
                          ),
                        ),
                      ),
                    ),

                    // Privacy Policy Card
                    _ProfileOptionCard(
                      title: 'Privacy Policy',
                      subtitle: 'View our privacy policy',
                      icon: Icons.privacy_tip_outlined,
                      onTap: () => Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const LegalDocumentScreen(
                            title: 'Privacy Policy',
                            assetPath: 'assets/legal/privacy-policy.md',
                          ),
                        ),
                      ),
                    ),

                    // Sign Out Option
                    _ProfileOptionCard(
                      title: 'Sign Out',
                      subtitle: 'Log out of your account',
                      icon: Icons.logout,
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Sign Out'),
                            content: const Text('Are you sure you want to sign out?'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                child: const Text('CANCEL'),
                              ),
                              FilledButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  ref.read(loginProvider.notifier).signOut();
                                },
                                child: const Text('SIGN OUT'),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ProfileOptionCard extends StatelessWidget {
  const _ProfileOptionCard({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
  });

  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(
                icon,
                size: 24,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium,
                    ),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(153),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: theme.colorScheme.onSurface.withAlpha(128),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
