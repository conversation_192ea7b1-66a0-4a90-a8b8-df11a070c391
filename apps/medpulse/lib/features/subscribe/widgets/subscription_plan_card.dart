import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:medpulse/features/subscribe/widgets/plan_badge.dart';

/// A card that displays a subscription plan
class SubscriptionPlanCard extends StatelessWidget {
  final String title;
  final double price;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isPopular;
  final bool isBestValue;
  final bool hasWorryFreeTag;

  const SubscriptionPlanCard({
    super.key,
    required this.title,
    required this.price,
    required this.icon,
    this.onPressed,
    this.isLoading = false,
    this.isPopular = false,
    this.isBestValue = false,
    this.hasWorryFreeTag = false,
  });

  @override
  Widget build(BuildContext context) {
    final formattedPrice = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(price);
    final theme = Theme.of(context);

    // Calculate per month pricing for comparison
    double monthlyPrice = 0;
    String perMonthText = '';

    if (title == '3 Months') {
      monthlyPrice = price / 3;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    } else if (title == '6 Months') {
      monthlyPrice = price / 6;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    } else if (title == '12 Months') {
      monthlyPrice = price / 12;
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isBestValue
                  ? theme.colorScheme.primary
                  : isPopular
                  ? theme.colorScheme.secondary
                  : Colors.transparent,
          width: isBestValue || isPopular ? 2 : 0,
        ),
      ),
      child: InkWell(
        onTap: isLoading ? null : onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(icon, size: 28, color: theme.colorScheme.primary),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(title, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                        if (perMonthText.isNotEmpty)
                          Text(
                            perMonthText,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurface.withAlpha(179), // 0.7 * 255 = 178.5, rounded to 179
                            ),
                          ),
                      ],
                    ),
                  ),
                  Text(
                    formattedPrice,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
              if (isBestValue || isPopular || hasWorryFreeTag)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (isBestValue)
                        PlanBadge(label: 'BEST VALUE', color: theme.colorScheme.primary, icon: Icons.star),
                      if (isPopular)
                        PlanBadge(label: 'POPULAR', color: theme.colorScheme.secondary, icon: Icons.thumb_up),
                      if (hasWorryFreeTag) PlanBadge(label: 'WORRY FREE', color: Colors.teal, icon: Icons.security),
                    ],
                  ),
                ),
              if (isLoading)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2)),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
