import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';

/// Widget that displays the status of a transaction
class TransactionStatusWidget extends ConsumerWidget {
  final String transactionId;
  final VoidCallback onClose;

  const TransactionStatusWidget({
    super.key,
    required this.transactionId,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(userProvider);

    return userAsync.when(
      data: (user) {
        if (user == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, color: Colors.red, size: 48),
                SizedBox(height: 16),
                Text('Error: User not found'),
              ],
            ),
          );
        }

        final transaction = user.transactions?[transactionId];
        if (transaction == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 24),
                const Text(
                  'Verifying Payment',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'Please complete the payment in the browser tab and wait while we verify your transaction.',
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 8),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'This may take a few moments.',
                    style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 24),
                FilledButton(
                  onPressed: onClose,
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }

        final isSuccess = transaction['status'] == 'success';
        final isRefunded = transaction['isRefunded'] == true;
        final isVoided = transaction['isVoided'] == true;

        String statusText;
        Color statusColor;
        if (isRefunded) {
          statusText = 'Refunded';
          statusColor = Colors.orange;
        } else if (isVoided) {
          statusText = 'Voided';
          statusColor = Colors.grey;
        } else if (isSuccess) {
          statusText = 'Success';
          statusColor = Colors.green;
        } else {
          statusText = 'Failed';
          statusColor = Colors.red;
        }

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(isSuccess ? Icons.check_circle : Icons.error, color: statusColor, size: 48),
              const SizedBox(height: 24),
              Text(
                statusText,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: statusColor),
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  transaction['message'] ?? statusText,
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
              FilledButton(
                onPressed: onClose,
                child: Text(isSuccess ? 'Continue' : 'Try Again'),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 24),
            Text(
              'Verifying Payment',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'Please complete the payment in the browser tab and wait while we verify your transaction.',
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 8),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'This may take a few moments.',
                style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            const Text(
              'Error',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'Error: $error',
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: onClose,
              child: const Text('Close'),
            ),
          ],
        ),
      ),
    );
  }
}
