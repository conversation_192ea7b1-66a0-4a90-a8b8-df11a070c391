import 'package:flutter/material.dart';

/// A card that can be expanded to show more details about a feature
class ExpandableFeatureCard extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const ExpandableFeatureCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  @override
  State<ExpandableFeatureCard> createState() => _ExpandableFeatureCardState();
}

class _ExpandableFeatureCardState extends State<ExpandableFeatureCard> with SingleTickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _controller;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: const Duration(milliseconds: 200), vsync: this);
    _expandAnimation = CurvedAnimation(parent: _controller, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: widget.color.withAlpha(77), width: 1), // 0.3 * 255 = 76.5, rounded to 77
      ),
      child: InkWell(
        onTap: _toggleExpanded,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: widget.color.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(widget.icon, size: 18, color: widget.color),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: Text(widget.title, style: const TextStyle(fontWeight: FontWeight.w500))),
                  RotationTransition(
                    turns: Tween(begin: 0.0, end: 0.25).animate(_expandAnimation),
                    child: Icon(
                      Icons.keyboard_arrow_right,
                      size: 18,
                      color: Theme.of(context).colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                    ),
                  ),
                ],
              ),
              ClipRect(
                child: SizeTransition(
                  sizeFactor: _expandAnimation,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12, left: 36),
                    child: Text(
                      widget.description,
                      style: TextStyle(
                        fontSize: 13,
                        color: Theme.of(context).colorScheme.onSurface.withAlpha(179),
                      ), // 0.7 * 255 = 178.5, rounded to 179
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
