import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/widgets/subscription_plan_card.dart';

/// Screen that displays available subscription plans
class PlansScreen extends ConsumerWidget {
  final Function(String) onPlanSelected;

  const PlansScreen({
    super.key,
    required this.onPlanSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Choose your subscription plan:'),
                    const SizedBox(height: 16),
                    SubscriptionPlanCard(
                      title: '3 Months',
                      price: 998,
                      icon: Icons.calendar_today,
                      onPressed: () => onPlanSelected('3_months'),
                    ),
                    const SizedBox(height: 12),
                    SubscriptionPlanCard(
                      title: '6 Months',
                      price: 1798,
                      icon: Icons.calendar_month,
                      isPopular: true,
                      onPressed: () => onPlanSelected('6_months'),
                    ),
                    const SizedBox(height: 12),
                    SubscriptionPlanCard(
                      title: '12 Months',
                      price: 2998,
                      icon: Icons.event_available,
                      isBestValue: true,
                      onPressed: () => onPlanSelected('12_months'),
                    ),
                    const SizedBox(height: 12),
                    SubscriptionPlanCard(
                      title: 'Lifetime',
                      price: 4998,
                      icon: Icons.all_inclusive,
                      onPressed: () => onPlanSelected('lifetime'),
                      hasWorryFreeTag: true,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
