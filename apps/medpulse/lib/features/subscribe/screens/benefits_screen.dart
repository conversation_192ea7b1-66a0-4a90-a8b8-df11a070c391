import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/widgets/expandable_feature_card.dart';
import 'package:medpulse/providers/user_provider.dart';

/// Screen that displays the benefits of premium subscription
class BenefitsScreen extends ConsumerWidget {
  final VoidCallback onContinue;

  const BenefitsScreen({
    super.key,
    required this.onContinue,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider).valueOrNull;
    
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (user?.courseId != null && user?.yearId != null) ...[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: RichText(
                          text: TextSpan(
                            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                            children: [
                              const TextSpan(text: 'Course: ', style: TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(text: user?.courseId ?? ''),
                              const TextSpan(text: '  '),
                              const TextSpan(text: 'Year: ', style: TextStyle(fontWeight: FontWeight.bold)),
                              TextSpan(text: user?.yearId ?? ''),
                            ],
                          ),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(bottom: 16),
                        child: Text(
                          'This subscription will grant you full access to all premium content for the selected course and year, including:',
                          style: TextStyle(fontSize: 14),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ExpandableFeatureCard(
                              title: 'Complete MCQ questions and answers',
                              icon: Icons.question_answer,
                              description:
                                  'Access our extensive database of Multiple Choice Questions with detailed explanations for right and wrong answers to solidify your understanding.',
                              color: Colors.blue,
                            ),
                            const SizedBox(height: 8),
                            ExpandableFeatureCard(
                              title: 'Complete Flipcard questions and answers',
                              icon: Icons.flip,
                              description:
                                  'Practice with interactive flashcards designed to help you master key concepts through active recall techniques.',
                              color: Colors.purple,
                            ),
                            const SizedBox(height: 8),
                            ExpandableFeatureCard(
                              title: 'Bookmark questions for recap',
                              icon: Icons.bookmark,
                              description:
                                  'Save challenging questions to revisit later. Create your personalized study list for efficient exam preparation.',
                              color: Colors.orange,
                            ),
                            const SizedBox(height: 8),
                            ExpandableFeatureCard(
                              title: 'Access to all new and updated content',
                              icon: Icons.new_releases,
                              description:
                                  'Automatically receive access to new tests, questions, and content updates throughout your subscription period at no additional cost.',
                              color: Colors.green,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: FilledButton.icon(
                  onPressed: onContinue,
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text("Let's do this"),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
