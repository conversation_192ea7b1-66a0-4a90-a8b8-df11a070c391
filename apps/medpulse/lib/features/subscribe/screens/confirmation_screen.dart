import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/widgets/transaction_status_widget.dart';

/// Screen that displays payment confirmation or error
class ConfirmationScreen extends ConsumerWidget {
  final bool success;
  final String? transactionId;
  final String? error;
  final VoidCallback onClose;

  const ConfirmationScreen({
    super.key,
    required this.success,
    this.transactionId,
    this.error,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    if (transactionId != null && success) {
      // Show transaction status widget if we have a transaction ID
      return TransactionStatusWidget(
        transactionId: transactionId!,
        onClose: onClose,
      );
    }

    // Show success or error message
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? Colors.green : Colors.red,
              size: 60,
            ),
            const SizedBox(height: 24),
            Text(
              success ? 'Payment Completed' : 'Payment Failed',
              style: theme.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                success
                    ? 'Your payment has been successfully processed. Thank you for your subscription!'
                    : (error ?? 'An error occurred while processing your payment.'),
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            FilledButton(
              onPressed: onClose,
              child: Text(success ? 'Continue' : 'Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}
