import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/features/subscribe/widgets/social_button.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';
import 'package:providers/login_provider.dart';

/// Screen that handles user authentication for subscription
class AuthenticationScreen extends ConsumerStatefulWidget {
  final VoidCallback onAuthenticationComplete;

  const AuthenticationScreen({
    super.key,
    required this.onAuthenticationComplete,
  });

  @override
  ConsumerState<AuthenticationScreen> createState() => _AuthenticationScreenState();
}

class _AuthenticationScreenState extends ConsumerState<AuthenticationScreen> {
  bool _isLoading = false;
  String? _error;

  Future<void> _handleGoogleSignIn() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Use the loginProvider to sign in with Google
      await ref.read(loginProvider.notifier).signInWithGoogle();

      // Check if the sign-in was successful
      final loginState = ref.read(loginProvider);
      if (loginState.hasError) {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          _error = 'Failed to sign in with Google: ${loginState.error}';
        });
        return;
      }

      // Check if the user has accepted the legal documents
      if (mounted) {
        final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
        if (!hasAccepted) {
          // Show the legal documents dialog
          final accepted = await LegalDocumentsDialog.show(context: context);
          if (accepted != true) {
            // Reset auth state
            setState(() {
              _isLoading = false;
              _error = null;
            });
            return;
          }
        }
      }

      // Reset auth state
      setState(() {
        _isLoading = false;
        _error = null;
      });

      // Notify parent that authentication is complete
      widget.onAuthenticationComplete();
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _error = 'Failed to sign in: ${e.toString()}';
      });
    }
  }

  Future<void> _handleAppleSignIn() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Use the loginProvider to sign in with Apple
      await ref.read(loginProvider.notifier).signInWithApple();

      // Check if the sign-in was successful
      final loginState = ref.read(loginProvider);
      if (loginState.hasError) {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
          _error = 'Failed to sign in with Apple: ${loginState.error}';
        });
        return;
      }

      // Check if the user has accepted the legal documents
      if (mounted) {
        final hasAccepted = ref.read(userProvider.notifier).hasAcceptedLegalDocuments();
        if (!hasAccepted) {
          // Show the legal documents dialog
          final accepted = await LegalDocumentsDialog.show(context: context);
          if (accepted != true) {
            // Reset auth state
            setState(() {
              _isLoading = false;
              _error = null;
            });
            return;
          }
        }
      }

      // Reset auth state
      setState(() {
        _isLoading = false;
        _error = null;
      });

      // Notify parent that authentication is complete
      widget.onAuthenticationComplete();
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _error = 'Failed to sign in: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_circle, size: 48, color: theme.colorScheme.primary),
            const SizedBox(height: 24),
            const Text(
              'Sign In Required',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Text(
                'To make a purchase, you need to sign in with a Google or Apple account.',
                textAlign: TextAlign.center,
              ),
            ),
            if (_error != null) ...[
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(_error!, style: TextStyle(color: theme.colorScheme.error), textAlign: TextAlign.center),
              ),
            ],
            const SizedBox(height: 32),
            SocialButton(
              text: 'Continue with Google',
              icon: Icons.g_mobiledata,
              onPressed: _isLoading ? null : _handleGoogleSignIn,
            ),
            const SizedBox(height: 16),
            SocialButton(text: 'Continue with Apple', icon: Icons.apple, onPressed: _isLoading ? null : _handleAppleSignIn),
            if (_isLoading) ...[const SizedBox(height: 24), const CircularProgressIndicator()],
          ],
        ),
      ),
    );
  }
}
