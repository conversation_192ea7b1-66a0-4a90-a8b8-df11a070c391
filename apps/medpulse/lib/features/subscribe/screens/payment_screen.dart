import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/services/paymob_service.dart';
import 'package:medpulse/widgets/payment_iframe.dart';

/// Screen that handles the payment process
class PaymentScreen extends ConsumerStatefulWidget {
  final String planType;
  final Function({
    required bool success,
    String? transactionId,
    String? error,
  }) onPaymentComplete;

  const PaymentScreen({
    super.key,
    required this.planType,
    required this.onPaymentComplete,
  });

  @override
  ConsumerState<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends ConsumerState<PaymentScreen> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  bool _processingComplete = false;
  bool _processingSuccess = false;
  bool _showingIframe = false;
  String? _error;
  String? _transactionId;
  String? _paymentUrl;

  // For processing animation
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentStep = 0;
  final List<String> _processingSteps = [
    'Initializing payment...',
    'Connecting to payment gateway...',
    'Preparing subscription details...',
    'Securing transaction...',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 1500));
    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);
    
    // Start the payment process
    _initializePayment();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startProcessingAnimation() {
    _animationController.repeat();
    _currentStep = 0;
    _advanceProcessingStep();
  }

  void _advanceProcessingStep() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _isLoading && _currentStep < _processingSteps.length - 1) {
        setState(() {
          _currentStep++;
        });
        _advanceProcessingStep();
      }
    });
  }

  Future<void> _initializePayment() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    // Start the processing animation
    _startProcessingAnimation();

    try {
      final user = ref.read(userProvider).value;
      if (user == null) {
        if (!mounted) return;
        setState(() {
          _error = 'Please sign in to subscribe';
          _isLoading = false;
        });
        return;
      }

      final paymobService = PaymobService();
      final subscriptionId = '${user.courseId}:${user.yearId}';
      final callbackUrl = 'https://us-central1-medpulse-8d1b7.cloudfunctions.net/handlePaymobCallback';

      // Initiate payment (async operation)
      final result = await paymobService.initiatePayment(
        user: user,
        subscriptionType: widget.planType,
        callbackUrl: callbackUrl,
        subscriptionId: subscriptionId,
      );

      // Update processing state to complete
      if (mounted) {
        setState(() {
          _processingComplete = true;
          _processingSuccess = true;
          _transactionId = result['transactionId'];
          _paymentUrl = result['paymentUrl'];
          // Keep _isLoading true to continue showing the processing UI
        });

        // Stop the animation
        _animationController.stop();
      }

      // Set state to show iframe
      if (mounted) {
        setState(() {
          _showingIframe = true;
        });
      }
    } catch (e) {
      // After async gap, check if still mounted
      if (!mounted) return;

      // Update state to show error in the processing UI
      setState(() {
        _processingComplete = true;
        _processingSuccess = false;
        _error = 'Failed to initiate payment: $e';
        _isLoading = false;
      });

      // Stop the animation
      _animationController.stop();
      
      // Notify parent of payment failure
      widget.onPaymentComplete(
        success: false,
        error: 'Failed to initiate payment: $e',
      );
    }
  }

  void _handlePaymentComplete(bool success, String? message) {
    if (!mounted) return;

    setState(() {
      _showingIframe = false;
      _processingComplete = true;
      _processingSuccess = success;
      if (!success) {
        _error = message ?? 'Payment failed';
      }
    });

    // Stop the animation
    _animationController.stop();
    
    // Notify parent of payment completion
    widget.onPaymentComplete(
      success: success,
      transactionId: _transactionId,
      error: success ? null : (message ?? 'Payment failed'),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final String planTypeFormatted = widget.planType.replaceAll('_', ' ').toUpperCase();

    // If showing iframe, display the payment iframe
    if (_showingIframe && _paymentUrl != null) {
      return Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            children: [
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Complete your payment',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: PaymentIframeFactory.create(
                  paymentUrl: _paymentUrl!,
                  onPaymentComplete: _handlePaymentComplete,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: () {
                    _handlePaymentComplete(false, 'Payment cancelled');
                  },
                  child: const Text('Cancel Payment'),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Show processing UI
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 20),
            if (_processingComplete && _processingSuccess)
              Icon(Icons.check_circle, color: Colors.green, size: 60)
            else if (_processingComplete && !_processingSuccess)
              Icon(Icons.error, color: Colors.red, size: 60)
            else
              RotationTransition(
                turns: _animation,
                child: Container(
                  width: 60,
                  height: 60,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(color: theme.colorScheme.primary.withAlpha(26), shape: BoxShape.circle),
                  child: Icon(Icons.sync, color: theme.colorScheme.primary, size: 32),
                ),
              ),
            const SizedBox(height: 30),
            Text(
              _processingComplete
                  ? (_processingSuccess
                      ? 'Payment processing initiated successfully'
                      : (_error ?? 'An error occurred while processing your payment.'))
                  : 'Preparing your $planTypeFormatted subscription',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            if (!_processingComplete) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.outline.withAlpha(77),
                  ),
                ),
                child: Column(
                  children: [
                    for (int i = 0; i < _processingSteps.length; i++)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            if (i < _currentStep)
                              Icon(Icons.check_circle, color: Colors.green, size: 18)
                            else if (i == _currentStep)
                              SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2))
                            else
                              Icon(Icons.circle_outlined, color: Colors.grey, size: 18),
                            const SizedBox(width: 12),
                            Text(
                              _processingSteps[i],
                              style: TextStyle(
                                color: i <= _currentStep ? theme.colorScheme.onSurface : Colors.grey,
                                fontWeight: i == _currentStep ? FontWeight.bold : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Please do not close this window',
                style: TextStyle(color: theme.colorScheme.error, fontSize: 12),
              ),
            ],
            if (_processingComplete && !_processingSuccess) ...[
              const SizedBox(height: 20),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Go Back'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
