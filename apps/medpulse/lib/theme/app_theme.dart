import 'package:flutter/material.dart';

class AppTheme {
  // Light Theme Colors
  static const Color _primary = Color(0xFF3B44C3);
  static const Color _onPrimary = Color(0xFFFFFFFF);
  static const Color _secondary = Color(0xFF464646);
  static const Color _onSecondary = Color(0xFFFFFFFF);
  static const Color _tertiary = Colors.amber; //  Color(0xFF00C7BE);
  static const Color _error = Color(0xFFFF3B30);
  static const Color _onError = Color(0xFFFFFFFF);
  static const Color _bodyText = Color(0xFF817A7A);
  static const Color _surface = Color(0xFFFFFFFF);

  // Dark Theme Colors
  static const Color _primaryDark = Color(0xFF3B44C3); // Same as light theme
  static const Color _onPrimaryDark = Color(0xFFFFFFFF); // Same as light theme
  static const Color _secondaryDark = Color(0xFFB3B3B3);
  static const Color _onSecondaryDark = Color(0xFF464646);
  static const Color _tertiaryDark = Colors.amber; // Color(0xFF00C7BE);
  static const Color _errorDark = Color(0xFFFF3B30); // Same as light theme
  static const Color _onErrorDark = Color(0xFFFFFFFF); // Same as light theme
  static const Color _bodyTextDark = Color(0xFFA8A8A8);
  static const Color _surfaceDark = Color(0xFF121212);
  static const Color _onSurfaceDark = Color(0xFFFFFFFF);

  // Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: "Cabin",
      colorScheme: ColorScheme.fromSeed(seedColor: _primary).copyWith(
        primary: _primary,
        onPrimary: _onPrimary,
        secondary: _secondary,
        onSecondary: _onSecondary,
        tertiary: _tertiary,
        error: _error,
        onError: _onError,
        surface: _surface,
        onSurface: _secondary,
        // Headings
        onSurfaceVariant: _bodyText, // Body text
      ),
      textTheme: _textTheme,
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: _surface,
        indicatorColor: _primary.withAlpha(31),
      ),
      extensions: [AppColors.light()],
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: "Cabin",
      colorScheme: ColorScheme.dark().copyWith(
        primary: _primaryDark,
        onPrimary: _onPrimaryDark,
        secondary: _secondaryDark,
        onSecondary: _onSecondaryDark,
        tertiary: _tertiaryDark,
        error: _errorDark,
        onError: _onErrorDark,
        surface: _surfaceDark,
        onSurface: _onSurfaceDark,
        onSurfaceVariant: _bodyTextDark,
      ),
      textTheme: _textTheme,
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: _surfaceDark,
        indicatorColor: _primaryDark.withAlpha(31),
      ),
      extensions: [AppColors.dark()],
    );
  }

  // Shared text styles
  static const TextTheme _textTheme = TextTheme(
    displayLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.bold,
    ),
    displayMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.bold,
    ),
    displaySmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.bold,
    ),
    // Body text colors are handled by onSurfaceVariant in colorScheme
  );
}

class AppColors extends ThemeExtension<AppColors> {
  final Color markedForReviewText;
  final Color markedForReviewBackground;
  final Color unansweredText;
  final Color unansweredBackground;
  final Color unansweredDialogBackground;
  final Color unansweredDialogText;

  // Score colors for test results
  final Color excellentScore;
  final Color goodScore;
  final Color averageScore;
  final Color poorScore;
  final Color scoreIndicatorTextColor;

  // Tag colors
  final Color freeTagColor;
  final Color freeTagTextColor;

  // Question review colors
  final Color correctAnswerColor;
  final Color correctAnswerBackgroundColor;
  final Color incorrectAnswerColor;
  final Color incorrectAnswerBackgroundColor;
  final Color notAnsweredColor;
  final Color notAnsweredBackgroundColor;

  AppColors({
    required this.markedForReviewText,
    required this.markedForReviewBackground,
    required this.unansweredBackground,
    required this.unansweredText,
    required this.unansweredDialogBackground,
    required this.unansweredDialogText,
    required this.excellentScore,
    required this.goodScore,
    required this.averageScore,
    required this.poorScore,
    required this.scoreIndicatorTextColor,
    required this.freeTagColor,
    required this.freeTagTextColor,
    required this.correctAnswerColor,
    required this.correctAnswerBackgroundColor,
    required this.incorrectAnswerColor,
    required this.incorrectAnswerBackgroundColor,
    required this.notAnsweredColor,
    required this.notAnsweredBackgroundColor,
  });

  @override
  ThemeExtension<AppColors> copyWith({
    Color? markedForReviewText,
    Color? markedForReviewBackground,
    Color? unansweredBackground,
    Color? unansweredText,
    Color? unansweredDialogBackground,
    Color? unansweredDialogText,
    Color? excellentScore,
    Color? goodScore,
    Color? averageScore,
    Color? poorScore,
    Color? scoreIndicatorTextColor,
    Color? freeTagColor,
    Color? freeTagTextColor,
    Color? correctAnswerColor,
    Color? correctAnswerBackgroundColor,
    Color? incorrectAnswerColor,
    Color? incorrectAnswerBackgroundColor,
    Color? notAnsweredColor,
    Color? notAnsweredBackgroundColor,
  }) {
    return AppColors(
      markedForReviewText: markedForReviewText ?? this.markedForReviewText,
      markedForReviewBackground: markedForReviewBackground ?? this.markedForReviewBackground,
      unansweredBackground: unansweredBackground ?? this.unansweredBackground,
      unansweredText: unansweredText ?? this.unansweredText,
      unansweredDialogBackground: unansweredDialogBackground ?? this.unansweredDialogBackground,
      unansweredDialogText: unansweredDialogText ?? this.unansweredDialogText,
      excellentScore: excellentScore ?? this.excellentScore,
      goodScore: goodScore ?? this.goodScore,
      averageScore: averageScore ?? this.averageScore,
      poorScore: poorScore ?? this.poorScore,
      scoreIndicatorTextColor: scoreIndicatorTextColor ?? this.scoreIndicatorTextColor,
      freeTagColor: freeTagColor ?? this.freeTagColor,
      freeTagTextColor: freeTagTextColor ?? this.freeTagTextColor,
      correctAnswerColor: correctAnswerColor ?? this.correctAnswerColor,
      correctAnswerBackgroundColor: correctAnswerBackgroundColor ?? this.correctAnswerBackgroundColor,
      incorrectAnswerColor: incorrectAnswerColor ?? this.incorrectAnswerColor,
      incorrectAnswerBackgroundColor: incorrectAnswerBackgroundColor ?? this.incorrectAnswerBackgroundColor,
      notAnsweredColor: notAnsweredColor ?? this.notAnsweredColor,
      notAnsweredBackgroundColor: notAnsweredBackgroundColor ?? this.notAnsweredBackgroundColor,
    );
  }

  @override
  ThemeExtension<AppColors> lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) return this;
    return AppColors(
      markedForReviewText: Color.lerp(markedForReviewText, other.markedForReviewText, t)!,
      markedForReviewBackground: Color.lerp(markedForReviewBackground, other.markedForReviewBackground, t)!,
      unansweredText: Color.lerp(unansweredText, other.unansweredText, t)!,
      unansweredBackground: Color.lerp(unansweredBackground, other.unansweredBackground, t)!,
      unansweredDialogBackground: Color.lerp(unansweredDialogBackground, other.unansweredDialogBackground, t)!,
      unansweredDialogText: Color.lerp(unansweredDialogText, other.unansweredDialogText, t)!,
      excellentScore: Color.lerp(excellentScore, other.excellentScore, t)!,
      goodScore: Color.lerp(goodScore, other.goodScore, t)!,
      averageScore: Color.lerp(averageScore, other.averageScore, t)!,
      poorScore: Color.lerp(poorScore, other.poorScore, t)!,
      scoreIndicatorTextColor: Color.lerp(scoreIndicatorTextColor, other.scoreIndicatorTextColor, t)!,
      freeTagColor: Color.lerp(freeTagColor, other.freeTagColor, t)!,
      freeTagTextColor: Color.lerp(freeTagTextColor, other.freeTagTextColor, t)!,
      correctAnswerColor: Color.lerp(correctAnswerColor, other.correctAnswerColor, t)!,
      correctAnswerBackgroundColor: Color.lerp(correctAnswerBackgroundColor, other.correctAnswerBackgroundColor, t)!,
      incorrectAnswerColor: Color.lerp(incorrectAnswerColor, other.incorrectAnswerColor, t)!,
      incorrectAnswerBackgroundColor: Color.lerp(incorrectAnswerBackgroundColor, other.incorrectAnswerBackgroundColor, t)!,
      notAnsweredColor: Color.lerp(notAnsweredColor, other.notAnsweredColor, t)!,
      notAnsweredBackgroundColor: Color.lerp(notAnsweredBackgroundColor, other.notAnsweredBackgroundColor, t)!,
    );
  }

  static light() => AppColors(
    markedForReviewText: const Color(0xFF000000),
    markedForReviewBackground: const Color(0xFF00C7BE),
    unansweredText: const Color(0xFF817A7A),
    unansweredBackground: const Color(0xFFE4E1E9),
    unansweredDialogBackground: const Color(0xFFB6B4BA), // 20% darker than unansweredBackground
    unansweredDialogText: const Color(0xFF676262), // 20% darker than unansweredText
    excellentScore: const Color(0xFF4CAF50), // Green
    goodScore: const Color(0xFF2196F3),      // Blue
    averageScore: const Color(0xFFFF9800),    // Orange
    poorScore: const Color(0xFFF44336),       // Red
    scoreIndicatorTextColor: Colors.white,    // White text on colored background
    freeTagColor: const Color(0xFF4CAF50),    // Green
    freeTagTextColor: Colors.white,
    correctAnswerColor: Colors.green,
    correctAnswerBackgroundColor: Colors.green.withAlpha(26),
    incorrectAnswerColor: Colors.red,
    incorrectAnswerBackgroundColor: Colors.red.withAlpha(26),
    notAnsweredColor: Colors.grey,
    notAnsweredBackgroundColor: Colors.grey.withAlpha(51),
  );

  static dark() => AppColors(
    markedForReviewText: const Color(0xFF000000),
    markedForReviewBackground: const Color(0xFF00C7BE),
    unansweredText: const Color(0xFF817A7A),
    unansweredBackground: const Color(0xFFE4E1E9),
    unansweredDialogBackground: const Color(0xFFE9E7ED), // 20% lighter than unansweredBackground
    unansweredDialogText: const Color(0xFF9A9595), // 20% lighter than unansweredText
    excellentScore: const Color(0xFF66BB6A), // Slightly lighter green for dark theme
    goodScore: const Color(0xFF42A5F5),      // Slightly lighter blue for dark theme
    averageScore: const Color(0xFFFFA726),    // Slightly lighter orange for dark theme
    poorScore: const Color(0xFFEF5350),       // Slightly lighter red for dark theme
    scoreIndicatorTextColor: Colors.white,    // White text on colored background
    freeTagColor: const Color(0xFF66BB6A),    // Slightly lighter green for dark theme
    freeTagTextColor: Colors.white,
    correctAnswerColor: const Color(0xFF66BB6A),  // Lighter green for dark theme
    correctAnswerBackgroundColor: const Color(0xFF66BB6A).withAlpha(26),
    incorrectAnswerColor: const Color(0xFFEF5350),  // Lighter red for dark theme
    incorrectAnswerBackgroundColor: const Color(0xFFEF5350).withAlpha(26),
    notAnsweredColor: Colors.grey,
    notAnsweredBackgroundColor: Colors.grey.withAlpha(51),
  );
}

AppColors getAppColor(BuildContext context) => Theme.of(context).extension<AppColors>()!;
