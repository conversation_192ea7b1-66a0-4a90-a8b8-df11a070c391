import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tab_page_index_provider.g.dart';

const pageIndexHome = 0;
const pageIndexMCQ = 1;
const pageIndexFlip = 2;
const pageIndexProfile = 3;

@Riverpod(keepAlive: true)
class TabPageIndex extends _$TabPageIndex {
  @override
  int build() {
    return 0; // Default to the first page
  }

  String setPageIndex(int index) {
    const pageNamesAnalytics = ['Home', 'MCQ', 'Flip', 'Profile'];
    dbgPrint('TabPageIndexProvider: new TabPage index $index');

/*
    if (index == 0) {
      // since the home widget does not change, the providers will not be closed at any time and keep returning
      // the last provider-cached value. Invalidating them will cause them to start again and check db cache and
      // online (if expired)
      dbgPrint('TabPageIndexProvider: clearing pages... providers to check for updated home page');
      ref.invalidate(pagesArtistsProvider);
      ref.invalidate(pagesNasheedsProvider);
    }
*/

    state = index;
    return pageNamesAnalytics[index];
  }
}
