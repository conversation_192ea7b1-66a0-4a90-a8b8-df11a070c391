import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/services/analytics_service.dart';
import 'package:providers/common.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'tracking_transparency_provider.g.dart';

bool done = false;

@riverpod
Future<TrackingStatus> trackingTransparency(Ref ref) async {
  dbgPrint('trackingTransparencyProvider: init: waiting for FirebaseAppProvider');
  await ref.watch(firebaseAppProvider.future);

  if (kIsWeb) {
    return TrackingStatus.notSupported;
  }

  var status = await AppTrackingTransparency.trackingAuthorizationStatus;
  debugPrint('trackingTransparencyProvider: $status');
  AnalyticsService().setTrackingStatus(status);
  return status;
}
