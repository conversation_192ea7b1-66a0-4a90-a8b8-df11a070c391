import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:entities/user_entity.dart';
import 'package:entities/test_result.dart';
import 'package:entities/course_entity.dart';
import 'package:medpulse/providers/firebase_user_provider.dart';
import 'package:medpulse/providers/courses_provider.dart';
import 'package:medpulse/services/error_reporting_service.dart';
import 'package:medpulse/widgets/legal_documents_dialog.dart';
import 'package:providers/common.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:entities/test_progress.dart';

part 'user_provider.g.dart';

@riverpod
Stream<UserEntity?> userStream(Ref ref) async* {
  dbgPrint('userProvider: start');
  await ref.watch(firebaseAppProvider.future);
  final user = ref.watch(firebaseUserProvider).valueOrNull;
  if (user == null) {
    yield null;
    return;
  }

  yield* FirebaseFirestore.instance.collection('users').doc(user.uid).snapshots().map((snapshot) {
    dbgPrint('userProvider: received new snapshot: ${snapshot.data()}');
    if (!snapshot.exists) {
      return UserEntity(displayName: user.displayName ?? 'Guest', uid: user.uid, signedUp: DateTime.now());
    }
    try {
      final userEntity = UserEntity.fromJson(snapshot.data()!);
      return userEntity;
    } catch (e, s) {
      reportError(e, s);
      dbgPrint('Error parsing user: $e');
      rethrow;
    }
  });
}

/// Model class to represent a subscribed course:year combination
class SubscribedCourseYear {
  final String courseId;
  final String yearId;
  final DateTime expiryDate;

  SubscribedCourseYear({
    required this.courseId,
    required this.yearId,
    required this.expiryDate,
  });

  String get subscriptionId => '$courseId:$yearId';
}

@Riverpod(keepAlive: true)
class User extends _$User {
  /// List of fields that cannot be directly modified by the user
  /// These fields can only be updated by Firebase functions
  static const List<String> _restrictedFields = [
    'subscriptions',
    'transactions',
    'acceptedTerms',
    'acceptedPrivacyPolicy',
    'acceptedTermsAt',
    'acceptedPrivacyPolicyAt',
  ];

  /// Removes restricted fields from a user JSON object
  /// Returns a new map with the restricted fields removed
  Map<String, dynamic> _removeRestrictedFields(Map<String, dynamic> json) {
    dbgPrint('userProvider: _removeRestrictedFields');
    for (final field in _restrictedFields) {
      json.remove(field);
      dbgPrint('userProvider: removed $field');
    }
    return json;
  }

  @override
  Future<UserEntity?> build() async {
    return ref.watch(userStreamProvider.future);
  }

  /// subscriptionId is courseId:yearId
  bool isSubscribedTo(String subscriptionId) {
    final user = state.valueOrNull;
    if (user == null) return false;
    final subscriptions = user.subscriptions ?? {};
    // check if we are within the end date of the subscription
    if (subscriptions.containsKey(subscriptionId)) {
      final expiryTime = subscriptions[subscriptionId];
      if (expiryTime != null && expiryTime.isAfter(DateTime.now())) {
        return true;
      }
    }
    return false;
  }

  /// Returns a list of subscribed course:year combinations for the current user
  Future<List<SubscribedCourseYear>> getSubscribedCourseYears() async {
    final user = state.valueOrNull;

    if (user == null || user.subscriptions == null || user.subscriptions!.isEmpty) {
      return [];
    }

    final List<SubscribedCourseYear> result = [];

    // Loop through user subscriptions
    for (final entry in user.subscriptions!.entries) {
      // Skip expired subscriptions
      if (entry.value.isBefore(DateTime.now())) {
        continue;
      }

      // Parse the subscription ID (format: courseId:yearId)
      final parts = entry.key.split(':');
      if (parts.length != 2) {
        continue;
      }

      final courseId = parts[0];
      final yearId = parts[1];

      // Add to result list
      result.add(SubscribedCourseYear(
        courseId: courseId,
        yearId: yearId,
        expiryDate: entry.value,
      ));
    }

    return result;
  }

  /// Checks if the user has accepted the legal documents.
  /// Returns true if the user has accepted both T&C and Privacy Policy.
  bool hasAcceptedLegalDocuments() {
    if (!state.hasValue || state.value == null) return false;
    final user = state.value!;
    return user.acceptedTerms == true && user.acceptedPrivacyPolicy == true;
  }

  /// Shows the legal documents dialog if the user hasn't accepted them.
  /// Returns true if the user accepts the documents, false otherwise.
  Future<bool> checkAndShowLegalDocuments(BuildContext context) async {
    // Wait for the user entity to be available
    if (!state.hasValue || state.value == null) {
      return false;
    }

    final user = state.value!;

    // If the user has already accepted both documents, return true
    if (hasAcceptedLegalDocuments()) {
      return true;
    }

    // Determine which tab to show first based on what the user hasn't accepted yet
    final initialTabIndex = user.acceptedTerms == true ? 1 : 0;

    // Show the legal documents dialog
    final accepted = await LegalDocumentsDialog.show(context: context, initialTabIndex: initialTabIndex);

    return accepted ?? false;
  }

  /// Updates the user entity with legal documents acceptance information.
  /// Returns true if the update was successful.
  ///
  /// This method calls a Firebase function to update the user's acceptance fields
  /// because Firestore security rules prevent direct updates to these fields.
  Future<bool> updateLegalDocumentsAcceptance() async {
    if (!state.hasValue || state.value == null) return false;

    try {
      // Get Firebase user
      final firebaseUser = FirebaseAuth.instance.currentUser;
      if (firebaseUser == null) return false;
      if (state.hasValue == false) {
        dbgPrint('updateLegalDocumentsAcceptance: no user');
        return false;
      }

      // Make sure the user doc exists in Firestore by saving it
      var json = _removeRestrictedFields(state.value!.toJson());
      final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
      await userDocRef.set(json, SetOptions(merge: true));

      // Call the Firebase function to update the user's acceptance fields
      final callable = FirebaseFunctions.instanceFor(region: 'asia-southeast1').httpsCallable('acceptTerms');
      final result = await callable.call();
      dbgPrint('updateLegalDocumentsAcceptance: call: ${result.data}');
      // The provider watches the snapshot changes and will update the user object automatically.
      return result.data['success'] == true;

      /*
      if (result.data['success'] == true) {
        // Get the updated user document from Firestore to ensure we have the latest data
        final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
        final userDoc = await userDocRef.get();

        dbgPrint('updateLegalDocumentsAcceptance: ${userDoc.data()}');
        if (userDoc.exists) {
          // Update the user provider state with the latest data
          final updatedUser = UserEntity.fromJson(userDoc.data()!);
          state = AsyncData(updatedUser);
          return true;
        }
      }

      return false;
*/
    } catch (error) {
      dbgPrint('Error updating user acceptance: $error');
      return false;
    }
  }

  /// Update the user's course and year selection and save to Firebase
  Future<void> updateCourseAndYear(String courseId, String yearId) async {
    // Get Firebase user
    final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
    if (firebaseUser == null) return;
    // Get the user settings
    final medpulseUser = state.value;
    if (medpulseUser == null) return;

    Map<String, dynamic>? json;

    try {
      // Update the user entity
      final updatedUser = medpulseUser.copyWith(courseId: courseId, yearId: yearId);

      // Convert to JSON and remove restricted fields
      json = _removeRestrictedFields(updatedUser.toJson());

      final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
      await userDocRef.set(json, SetOptions(merge: true));

      // Update state
      state = AsyncData(updatedUser);
    } catch (error, stackTrace) {
      reportError(error, stackTrace);
      dbgPrint('saving user: ${firebaseUser.uid} ------------ $error\n$json');
      state = AsyncError<UserEntity?>(error, StackTrace.current).copyWithPrevious(state);
    }
  }

  /// Check if a question is bookmarked
  bool isQuestionBookmarked(String publishedTestId, int questionIndex) {
    final user = state.valueOrNull;
    if (user == null || user.bookmarks == null) return false;

    return user.bookmarks!.any(
      (bookmark) => bookmark.publishedTestId == publishedTestId && bookmark.questionIndex == questionIndex,
    );
  }

  /// Toggle bookmark status for a question
  Future<void> toggleQuestionBookmark(
    String publishedTestId,
    int questionIndex,
    String courseId,
    String subjectId,
    String subscriptionId,
    int version,
  ) async {
    final user = state.valueOrNull;
    if (user == null) return;

    try {
      // Create a new list from the existing bookmarks or an empty list
      final bookmarks = List<BookmarkEntity>.from(user.bookmarks ?? []);

      // Check if this question is already bookmarked
      final existingIndex = bookmarks.indexWhere(
        (b) => b.publishedTestId == publishedTestId && b.questionIndex == questionIndex,
      );

      if (existingIndex >= 0) {
        // Remove existing bookmark
        bookmarks.removeAt(existingIndex);
      } else {
        // Add new bookmark
        bookmarks.add(
          BookmarkEntity(
            publishedTestId: publishedTestId,
            courseId: courseId,
            subjectId: subjectId,
            subscriptionId: subscriptionId,
            version: version,
            questionIndex: questionIndex,
          ),
        );
      }

      // Update the user entity
      final updatedUser = user.copyWith(bookmarks: bookmarks);

      // Prepare JSON for Firestore update (without restricted fields)
      var json = _removeRestrictedFields(updatedUser.toJson());

      // Get Firebase user
      final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
      if (firebaseUser == null) return;

      // Update Firestore
      final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
      await userDocRef.set(json, SetOptions(merge: true));

      // Update state
      state = AsyncData(updatedUser);
    } catch (error) {
      state = AsyncError<UserEntity?>(error, StackTrace.current).copyWithPrevious(state);
      rethrow;
    }
  }

  /// Get all bookmarked questions
  List<BookmarkEntity> getBookmarkedQuestions() {
    return state.valueOrNull?.bookmarks ?? [];
  }

  /// Get bookmarked questions for a specific test
  List<BookmarkEntity> getBookmarkedQuestionsForTest(String publishedTestId) {
    final user = state.valueOrNull;
    if (user == null || user.bookmarks == null) return [];

    return user.bookmarks!.where((bookmark) => bookmark.publishedTestId == publishedTestId).toList();
  }

  /// Add a test result to the user's results list
  /// The result is added at the beginning of the list to maintain a time-descending order
  Future<void> addTestResult(TestProgress test) async {
    final user = state.valueOrNull;
    if (user == null) return;

    try {
      // Create a new list from the existing results or an empty list
      final results = List<TestResult>.from(user.results ?? []);

      // Add the new result at the beginning of the list (newest first)
      results.insert(0, test.result!);

      // Update the user entity
      final updatedUser = user.copyWith(results: results);

      // Prepare JSON for Firestore update (without restricted fields)
      var json = _removeRestrictedFields(updatedUser.toJson());

      // Get Firebase user
      final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
      if (firebaseUser == null) return;

      // output formatted json to console
      final prettyJson = JsonEncoder.withIndent('  ').convert(test.toJson());
      dbgPrint('saving test to users/${firebaseUser.uid}/attempts/${test.result!.id}:\n$prettyJson');

      // Save the complete test object in attempts
      await FirebaseFirestore.instance.doc('users/${firebaseUser.uid}/attempts/${test.result!.id}').set(test.toJson());

      dbgPrint('saving: ${firebaseUser.uid}');
      // Update the user object with the test result summary
      final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
      await userDocRef.set(json, SetOptions(merge: true));

      // Update state
      state = AsyncData(updatedUser);
    } catch (error) {
      state = AsyncError<UserEntity?>(error, StackTrace.current).copyWithPrevious(state);
      rethrow;
    }
  }

  /// Update the user's display name
  Future<void> updateDisplayName(String newDisplayName) async {
    // Get Firebase user
    final firebaseUser = ref.read(firebaseUserProvider).valueOrNull;
    if (firebaseUser == null) return;

    // Get the user settings
    final medpulseUser = state.value;
    if (medpulseUser == null) return;

    try {
      // Update Firebase Auth display name
      await firebaseUser.updateDisplayName(newDisplayName);

      // Update the user entity
      final updatedUser = medpulseUser.copyWith(displayName: newDisplayName);

      // Convert to JSON and remove restricted fields
      final json = _removeRestrictedFields(updatedUser.toJson());

      // Update Firestore
      final userDocRef = FirebaseFirestore.instance.doc('users/${firebaseUser.uid}');
      await userDocRef.set(json, SetOptions(merge: true));

      // Update state
      state = AsyncData(updatedUser);
    } catch (error, stackTrace) {
      reportError(error, stackTrace);
      dbgPrint('Error updating display name: $error');
      state = AsyncError<UserEntity?>(error, StackTrace.current).copyWithPrevious(state);
      rethrow;
    }
  }
}
