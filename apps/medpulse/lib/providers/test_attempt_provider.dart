import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/test_progress.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'firebase_user_provider.dart';

part 'test_attempt_provider.g.dart';

/// Provider for loading a test attempt (TestProgress) from Firebase
@riverpod
Future<TestProgress> testAttempt(Ref ref, String attemptId) async {
  // Get the current user
  final firebaseUser = ref.watch(firebaseUserProvider).valueOrNull;
  if (firebaseUser == null) {
    throw Exception('User not logged in');
  }

  // Fetch the test attempt from Firebase
  final docSnapshot = await FirebaseFirestore.instance
      .doc('users/${firebaseUser.uid}/attempts/$attemptId')
      .get();

  if (!docSnapshot.exists) {
    throw Exception('Test attempt not found');
  }

  // Convert to TestProgress
  return TestProgress.fromJson(docSnapshot.data()!);
}
