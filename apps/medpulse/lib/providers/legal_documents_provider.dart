import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'legal_documents_provider.g.dart';

/// Provider that checks if the user has accepted the legal documents.
@riverpod
bool hasAcceptedLegalDocuments(Ref ref) {
  final userAsync = ref.watch(userProvider);

  return userAsync.when(
    data: (user) => user != null && user.acceptedTerms == true && user.acceptedPrivacyPolicy == true,
    loading: () => false,
    error: (_, __) => false,
  );
}
