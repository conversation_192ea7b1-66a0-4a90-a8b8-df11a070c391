import 'package:medpulse/providers/firebase_user_provider.dart';
import 'package:providers/cached_doc_provider.dart';
import 'package:entities/courses_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'courses_provider.g.dart';

@Riverpod(keepAlive: true)
Future<CoursesEntity?> courses(Ref ref) async {
  // only read the docs once we have a valid firebase user (anonymous login is also a user)
  dbgPrint('coursesProvider: start');
  await ref.watch(firebaseUserProvider.future);
  final json = await ref.watch(CachedDocProvider('app_config/courses').future);
  if (json == null) {
    dbgPrint('coursesProvider: could not load courses');
    throw (Exception('could not load courses'));
  }
  return CoursesEntity.fromJson(json);
}

