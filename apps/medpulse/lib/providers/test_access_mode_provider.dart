import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'test_access_mode_provider.g.dart';

/// Represents the access mode for a test
enum TestAccessMode {
  free,
  subscribed,
}

/// Provider to check the access mode for a specific test
@riverpod
Future<TestAccessMode> testAccessMode(
    Ref ref,
    String testId, {
      int? version,
    }) async {

  // Get the current user
  final userAsync = await ref.watch(userProvider.future);

  // If no user is available, fall back to free mode
  if (userAsync == null) {
    return TestAccessMode.free;
  }

  // Extract subscription ID from the test ID
  // Expected format: "${courseId}:${yearId}:${originalTestId}"
  final parts = testId.split(':');

  // We need 3 parts (courseId, yearId, originalTestId)
  if (parts.length != 3) {
    return TestAccessMode.free;
  }

  // The subscription ID is courseId:yearId
  final subscriptionId = "${parts[0]}:${parts[1]}";

  // Check if the user has an active subscription for this test
  final subscriptions = userAsync.subscriptions;
  if (subscriptions == null || subscriptions.isEmpty) {
    // No subscriptions, fall back to free mode
    return TestAccessMode.free;
  }

  // Check if the subscription ID exists and is not expired
  final expiryDate = subscriptions[subscriptionId];

  if (expiryDate != null) {
    final now = DateTime.now();
    final hasValidSubscription = expiryDate.isAfter(now);

    if (hasValidSubscription) {
      // Has valid subscription, use subscribed mode
      return TestAccessMode.subscribed;
    }
  }

  // No valid subscription found, fall back to free mode
  return TestAccessMode.free;
}