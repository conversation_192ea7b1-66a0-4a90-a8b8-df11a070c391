import 'package:entities/published_test_entity.dart';
import 'package:entities/question_stats_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/cached_doc_provider.dart';
import 'package:providers/common.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';


part 'test_provider.g.dart';

/// Provider for fetching published tests
@riverpod
Future<PublishedTestEntity> test(Ref ref, String subscriptionId, String testId, {int? version, bool free = true}) async {

  // Determine which document to fetch
  final docId = version != null ? '$testId.$version' : testId;

  final collection = free ? 'published_free' : 'published_test';

  final json = await ref.watch(cachedDocProvider('$collection/$subscriptionId/tests/$docId').future);
  if (json == null) {
    final msg = 'testProvider: could not load ${free ? "free" : "premium"} test $testId';
    dbgPrint(msg);
    throw (Exception(msg));
  }

  // Parse the document into a PublishedTestEntity
  return PublishedTestEntity.fromJson(json);
}

/// Provider for fetching test statistics
@riverpod
Future<Map<int, QuestionStatsEntity>> testStats(Ref ref, String subscriptionId, String testId) async {
  final json = await ref.watch(cachedDocProvider('stats/$subscriptionId/tests/$testId').future);
  
  if (json == null) {
    return {}; // No stats available, return empty map
  }
  
  final questions = json['questions'] as Map<String, dynamic>? ?? {};
  final Map<int, QuestionStatsEntity> stats = {};
  
  questions.forEach((key, value) {
    final index = int.tryParse(key);
    if (index != null) {
      stats[index] = QuestionStatsEntity.fromJson(value);
    }
  });
  
  return stats;
}

