import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:providers/common.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:storage_service/storage_service.dart';

part 'preferences_provider.freezed.dart';
part 'preferences_provider.g.dart';

@freezed
class PreferencesState with _$PreferencesState {
  const factory PreferencesState({
    required bool onboardingCompleted,
  }) = _PreferencesState;
}

@Riverpod(keepAlive: true)
class Preferences extends _$Preferences {
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const f = "preferencesProvider";

  final _storage = StorageService();

  @override
  Future<PreferencesState> build() async {

    await ref.watch(firebaseAppProvider.future);

    dbgPrint('$f: init');
    final prefs = PreferencesState(
      onboardingCompleted: (await _getBoolPref(_onboardingCompletedKey, 'none')) ?? false,
    );
    dbgPrint('$f: $prefs');
    return prefs;
  }

  Future<void> setOnboardingCompleted(bool value) async {
    dbgPrint('$f: setOnboardingCompleted to $value');
    state = AsyncLoading<PreferencesState>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      await _storage.setPref(_onboardingCompletedKey, value.toString(), 'none');
      return state.value!.copyWith(onboardingCompleted: value);
    });
  }

  Future<String?> _getStringPref(String id) async {
    return await _storage.getPref(id);
  }

  Future<bool?> _getBoolPref(String id, [String? prefix]) async {
    dbgPrint('$f: getPref $id');
    final value = await _storage.getPref(id, prefix);
    dbgPrint('$f: getPref $id got $value');
    if (value == null) return null;
    return value.toLowerCase() == 'true';
  }

  Future<void> _setPref(String id, String? value) async {
    await _storage.setPref(id, value);
  }
}