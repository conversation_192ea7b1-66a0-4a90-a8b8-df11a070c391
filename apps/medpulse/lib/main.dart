import 'dart:async';
import 'dart:ui';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:medpulse/providers/courses_provider.dart';
import 'package:medpulse/providers/firebase_user_provider.dart';
import 'package:medpulse/providers/preferences_provider.dart';
import 'package:medpulse/screens/login_screen.dart';
import 'package:medpulse/screens/select_course_screen.dart';
import 'package:medpulse/services/error_reporting_service.dart';
import 'package:medpulse/theme/app_theme.dart';
import 'package:providers/common.dart';
import 'package:providers/connectivity_provider.dart';
import 'package:providers/firebase_app_provider.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pwa_install/pwa_install.dart';

import 'common/common.dart';
import 'firebase_options.dart';
import 'providers/scaffold_messenger_key_provider.dart';
import 'providers/tracking_transparency_provider.dart';
import 'providers/user_provider.dart';
import 'screens/base_screen.dart';
import 'screens/error_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/tracking_consent_screen.dart';
import 'services/google_sign_in_service.dart';
import 'widgets/cached_image.dart';
import 'widgets/database_loading_animation.dart';

bool? isDefault;

Future<void> main() async {

  runZonedGuarded<void>(() async {
    ProvidersFirebaseOptions(DefaultFirebaseOptions.currentPlatform);
    WidgetsFlutterBinding.ensureInitialized();

    FlutterError.onError = (FlutterErrorDetails details) {
      reportError(details.exception, details.stack ?? StackTrace.current, errorType: 'Unhandled Flutter Error');
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      reportError(error, stack, errorType: 'Unhandled Platform Error');
      return true;
    };

    PWAInstall().setup(installCallback: () {
      debugPrint('APP INSTALLED!');
    });
    // await GoogleSignInService().init();

    runApp(
      ProviderScope(
        child: MainMaterialAppWidget(),
      ),
    );
  }, (error, stackTrace) async {
    reportError(error, stackTrace, errorType: 'Unhandled Zoned Error');
  });

}

class MainMaterialAppWidget extends ConsumerWidget {
  const MainMaterialAppWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (tabletDevice(context)) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }

    return MaterialApp(
      scaffoldMessengerKey: ref.watch(scaffoldMessengerKeyProvider),
      title: 'MedPulse',
      themeMode: ThemeMode.system,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: Scaffold(
        body: MainBodyWidget(),
      ),
    );
  }
}

class MainBodyWidget extends ConsumerWidget {
  const MainBodyWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseApp = ref.watch(firebaseAppProvider);
    final transparency = ref.watch(trackingTransparencyProvider);
    final onboardingCompleted =
        ref.watch(preferencesProvider.select((state) => state.valueOrNull?.onboardingCompleted ?? false));
    final loginUser = ref.watch(firebaseUserProvider).valueOrNull;
    final medpulseUser = ref.watch(userProvider).valueOrNull;
    // watch courses early to make sure they are available ASAP
    ref.watch(coursesProvider).valueOrNull;
    // rebuild screen when going online/offline
    ref.watch(connectivityProvider);

    // return CachedImage(storagePath: 'https://firebasestorage.googleapis.com/v0/b/medpulse-prod.appspot.com/o/questions%2F9ecvGH6xd1h6hRoLxGr7%2Fquestion.jpeg?alt=media&token=84a3138d-a9c9-4f51-a8b4-4b924377fb10');

    switch (firebaseApp) {
      case AsyncLoading():
        return const DatabaseLoadingAnimation();
/*
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 16,
            children: [
              CircularProgressIndicator(),
              Text('Database init...'),
            ],
          ),
        );
*/
      case AsyncError():
        dbgPrint("MainBodyWidget: build: error: ${firebaseApp.error}");
        return ErrorScreen(
          context: context,
          description: 'Something went wrong here. Please try again.',
          onPressed: () {
            ref.invalidate(firebaseAppProvider);
          },
        );
    }

    switch (transparency) {
      case AsyncLoading():
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            spacing: 16,
            children: [
              CircularProgressIndicator(),
              Text('Transparency...'),
            ],
          ),
        );
      case AsyncError():
        dbgPrint(transparency.error.toString());
        return ErrorScreen(
          context: context,
          description: "Something went wrong there. Please try again.\n${transparency.error}",
          onPressed: () {
            ref.invalidate(trackingTransparencyProvider);
          },
        );
    }

    if (!onboardingCompleted) {
      return OnboardingScreen();
    }

    if (transparency.valueOrNull == TrackingStatus.notDetermined) {
      return TrackingConsentScreen();
    }

    if (loginUser == null) {
      return LoginScreen();
    }

    if (medpulseUser?.courseId == null || medpulseUser?.yearId == null) {
      return SelectCourseScreen();
    }

    return BaseScreen();
  }
}
