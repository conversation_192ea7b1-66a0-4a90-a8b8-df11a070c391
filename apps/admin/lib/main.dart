import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/connectivity_provider.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:providers/login_provider.dart';

import 'features/auth/providers/admin_user_provider.dart';
import 'features/auth/screens/login_screen.dart';
import 'features/dashboard/screens/admin_dashboard_screen.dart';
import 'firebase_options_dev.dart' as dev;
import 'firebase_options_prod.dart' as prod;
import 'providers/firebase_user_provider.dart';
import 'core/utils/app_messages.dart';
import 'core/theme.dart';

Future<void> main() async {
  runZonedGuarded<void>(() async {
    WidgetsFlutterBinding.ensureInitialized();

    const environment = String.fromEnvironment(
      'ENVIRONMENT',
      defaultValue: 'dev',
    );

    final firebaseOptions =
        environment == 'prod'
            ? prod.DefaultFirebaseOptions.currentPlatform
            : dev.DefaultFirebaseOptions.currentPlatform;

    ProvidersFirebaseOptions(firebaseOptions);

    runApp(const ProviderScope(child: MedPulseAdminApp()));
  }, (error, stackTrace) async {});
}

class MedPulseAdminApp extends ConsumerWidget {
  const MedPulseAdminApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scaffoldMessengerKey = GlobalKey<ScaffoldMessengerState>();
    AppMessages.initialize(scaffoldMessengerKey);

    return MaterialApp(
      scaffoldMessengerKey: scaffoldMessengerKey,
      title: 'MedPulse Admin',
      theme: AppTheme.lightTheme,
      home: const Scaffold(body: AdminBodyWidget()),
      debugShowCheckedModeBanner: false,
    );
  }
}

class AdminBodyWidget extends ConsumerWidget {
  const AdminBodyWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseApp = ref.watch(firebaseAppProvider);
    final userAsync = ref.watch(firebaseUserProvider);
    final user = userAsync.valueOrNull;
    final isAdminAsync = ref.watch(isAdminProvider);
    ref.watch(connectivityProvider);

    // Firebase app loading
    switch (firebaseApp) {
      case AsyncLoading():
        return const LoadingScreen();
      case AsyncError():
        return ErrorScreen(
          description: 'Something went wrong. Please try again.',
          onPressed: () {
            ref.invalidate(firebaseAppProvider);
          },
        );
    }

    // No user - show login
    if (user == null) {
      return const LoginScreen();
    }

    // Check admin status
    return switch (isAdminAsync) {
      AsyncLoading() => const LoadingScreen(),
      AsyncError(:final error) => ErrorScreen(
        description: 'Failed to verify admin status: ${error.toString()}',
        onPressed: () {
          ref.invalidate(isAdminProvider);
        },
      ),
      AsyncData(:final value) =>
        value ? const AdminDashboardScreen() : const AccessDeniedScreen(),
      _ => const LoadingScreen(),
    };
  }
}

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.admin_panel_settings_outlined,
              size: 64,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 24),
            CircularProgressIndicator(color: theme.colorScheme.primary),
            const SizedBox(height: 16),
            Text(
              'Loading MedPulse Admin...',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ErrorScreen extends StatelessWidget {
  final String description;
  final VoidCallback onPressed;

  const ErrorScreen({
    super.key,
    required this.description,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 24),
          Text(
            'Error',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              description,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(onPressed: onPressed, child: const Text('Try Again')),
        ],
      ),
    );
  }
}

class AccessDeniedScreen extends ConsumerWidget {
  const AccessDeniedScreen({super.key});

  Future<void> _handleSignOut(WidgetRef ref) async {
    try {
      await ref.read(loginProvider.notifier).signOut();
    } catch (e) {
      // Error handling
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loginState = ref.watch(loginProvider);

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.admin_panel_settings_outlined,
              size: 80,
              color: Colors.red[300],
            ),
            const SizedBox(height: 24),
            const Text(
              'Access Denied',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'You do not have administrator privileges',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed:
                  loginState.isLoading ? null : () => _handleSignOut(ref),
              child:
                  loginState.isLoading
                      ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text('Sign Out'),
            ),
          ],
        ),
      ),
    );
  }
}
