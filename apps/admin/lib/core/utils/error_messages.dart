/// Error message formatter for user-friendly error display
/// Converts technical errors into readable messages for users
class ErrorMessages {
  /// Get user-friendly error message for any error
  static String getErrorMessage(Object error) {
    final errorString = error.toString().toLowerCase();

    // Network errors
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection error. Please check your internet connection and try again.';
    }

    // Permission errors
    if (errorString.contains('permission') ||
        errorString.contains('unauthorized')) {
      return 'You don\'t have permission to perform this action. Please contact your administrator.';
    }

    // Timeout errors
    if (errorString.contains('timeout')) {
      return 'The operation timed out. Please try again.';
    }

    // Firebase errors
    if (errorString.contains('firebase')) {
      return _getFirebaseErrorMessage(errorString);
    }

    // Default fallback
    return 'An unexpected error occurred. Please try again.';
  }

  /// Get user-friendly error message for import-specific errors
  static String getImportErrorMessage(Object error) {
    final errorString = error.toString().toLowerCase();

    // Import size limit errors
    if (errorString.contains('import too large') ||
        errorString.contains('too many writes')) {
      return 'Import file is too large. Please split your data into smaller files and try again.';
    }

    // Excel parsing errors
    if (errorString.contains('failed to parse excel') ||
        errorString.contains('excel file')) {
      return 'Unable to read Excel file. Please ensure it\'s a valid .xlsx or .xls file and try again.';
    }

    // File format errors
    if (errorString.contains('invalid file format') ||
        errorString.contains('unsupported format')) {
      return 'Invalid file format. Please upload an Excel file (.xlsx or .xls).';
    }

    // File size errors
    if (errorString.contains('file too large') ||
        errorString.contains('size limit')) {
      return 'File is too large. Please upload a file smaller than 10MB.';
    }

    // Missing columns
    if (errorString.contains('missing required column') ||
        errorString.contains('column not found')) {
      return 'Missing required columns in your Excel file. Please ensure you have all required columns.';
    }

    // Validation errors
    if (errorString.contains('validation failed')) {
      return _formatValidationError(error.toString());
    }

    // Empty file
    if (errorString.contains('empty file') ||
        errorString.contains('no data') ||
        errorString.contains('no questions found')) {
      return 'No data found in the Excel file. Please add questions and try again.';
    }

    // Duplicate data
    if (errorString.contains('duplicate')) {
      return 'Duplicate data found in your Excel file. Please remove duplicates and try again.';
    }

    // Question format errors
    if (errorString.contains('invalid question format') ||
        errorString.contains('question structure')) {
      return 'Some questions have invalid format. Please check your question structure and try again.';
    }

    // Answer format errors
    if (errorString.contains('invalid answer') ||
        errorString.contains('correct answer') ||
        errorString.contains('answer options')) {
      return 'Some questions have invalid or missing answer options. Please check and try again.';
    }

    // Transaction errors
    if (errorString.contains('transaction failed') ||
        errorString.contains('transaction aborted')) {
      return 'Import was interrupted. Please try again.';
    }

    // Permission errors
    if (errorString.contains('permission denied') ||
        errorString.contains('unauthorized')) {
      return 'You don\'t have permission to import data. Please contact your administrator.';
    }

    // Network/connection errors
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return 'Connection error occurred during import. Please check your internet connection and try again.';
    }

    // Firebase/Firestore errors
    if (errorString.contains('firestore') || errorString.contains('firebase')) {
      return 'Database error occurred while saving your data. Please try again.';
    }

    // Generic import error - clean and user-friendly
    return 'Import failed. Please check your file and try again.';
  }

  /// Get user-friendly error message for authentication errors
  static String getAuthErrorMessage(Object error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('user-not-found')) {
      return 'No account found with this email address.';
    }

    if (errorString.contains('wrong-password')) {
      return 'Incorrect password. Please try again.';
    }

    if (errorString.contains('email-already-in-use')) {
      return 'An account with this email already exists.';
    }

    if (errorString.contains('weak-password')) {
      return 'Password is too weak. Please choose a stronger password.';
    }

    if (errorString.contains('invalid-email')) {
      return 'Invalid email address format.';
    }

    if (errorString.contains('too-many-requests')) {
      return 'Too many failed attempts. Please try again later.';
    }

    return 'Authentication failed. Please try again.';
  }

  /// Get user-friendly error message for validation errors
  static String getValidationErrorMessage(List<String> errors) {
    if (errors.isEmpty) return 'Validation failed.';

    if (errors.length == 1) {
      return 'Validation error: ${errors.first}';
    }

    return 'Validation failed with ${errors.length} errors:\n${errors.take(3).map((e) => '• $e').join('\n')}${errors.length > 3 ? '\n• ... and ${errors.length - 3} more errors' : ''}';
  }

  // Private helper methods

  static String _getFirebaseErrorMessage(String errorString) {
    if (errorString.contains('permission-denied')) {
      return 'Access denied. Please check your permissions.';
    }

    if (errorString.contains('not-found')) {
      return 'The requested data was not found.';
    }

    if (errorString.contains('already-exists')) {
      return 'This data already exists.';
    }

    if (errorString.contains('quota-exceeded')) {
      return 'Storage quota exceeded. Please contact support.';
    }

    return 'Database error occurred. Please try again.';
  }

  static String _formatValidationError(String error) {
    // Extract validation details if available
    if (error.contains('Validation failed:')) {
      final details = error.split('Validation failed:').last.trim();
      return 'Validation failed: $details';
    }

    return 'Data validation failed. Please check your Excel file format and try again.';
  }
}
