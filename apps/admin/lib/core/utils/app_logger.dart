import 'dart:developer' as developer;

/// Global logging system for the admin app
/// Provides structured logging with different levels and contexts
class AppLogger {
  static const String _appName = 'AdminApp';
  
  /// Log info message
  static void info(String message, [Map<String, dynamic>? data]) {
    developer.log(
      message,
      name: _appName,
      level: 800, // Info level
      error: data,
    );
  }
  
  /// Log warning message
  static void warning(String message, [Map<String, dynamic>? data]) {
    developer.log(
      message,
      name: _appName,
      level: 900, // Warning level
      error: data,
    );
  }
  
  /// Log error message
  static void error(String message, Object? error, [StackTrace? stackTrace, Map<String, dynamic>? data]) {
    developer.log(
      message,
      name: _appName,
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );
    
    if (data != null) {
      developer.log(
        'Additional data: $data',
        name: _appName,
        level: 1000,
      );
    }
  }
  
  /// Log debug message (only in debug mode)
  static void debug(String message, [Map<String, dynamic>? data]) {
    assert(() {
      developer.log(
        message,
        name: _appName,
        level: 700, // Debug level
        error: data,
      );
      return true;
    }());
  }
  
  // Specific logging methods for different features
  
  /// Log import-related errors
  static void importError(String operation, Object error, StackTrace? stackTrace, [Map<String, dynamic>? additionalData]) {
    final data = <String, dynamic>{
      'operation': operation,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    AppLogger.error('Import Error: $operation failed', error, stackTrace, data);
  }
  
  /// Log import success
  static void importSuccess(String operation, Map<String, dynamic> data) {
    final logData = <String, dynamic>{
      'operation': operation,
      'timestamp': DateTime.now().toIso8601String(),
      ...data,
    };
    
    AppLogger.info('Import Success: $operation completed', logData);
  }
  
  /// Log authentication errors
  static void authError(String operation, Object error, StackTrace? stackTrace, [Map<String, dynamic>? additionalData]) {
    final data = <String, dynamic>{
      'operation': operation,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    AppLogger.error('Auth Error: $operation failed', error, stackTrace, data);
  }
  
  /// Log validation errors
  static void validationError(String context, List<String> errors, [Map<String, dynamic>? additionalData]) {
    final data = <String, dynamic>{
      'context': context,
      'errors': errors,
      'error_count': errors.length,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    AppLogger.warning('Validation Error: $context', data);
  }
  
  /// Log network errors
  static void networkError(String endpoint, Object error, [StackTrace? stackTrace, Map<String, dynamic>? additionalData]) {
    final data = <String, dynamic>{
      'endpoint': endpoint,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };
    
    AppLogger.error('Network Error: $endpoint failed', error, stackTrace, data);
  }
  
  /// Log user actions for analytics
  static void userAction(String action, [Map<String, dynamic>? data]) {
    final logData = <String, dynamic>{
      'action': action,
      'timestamp': DateTime.now().toIso8601String(),
      ...?data,
    };
    
    AppLogger.info('User Action: $action', logData);
  }
  
  /// Track events for analytics
  static void trackEvent(String event, Map<String, dynamic> properties) {
    final data = <String, dynamic>{
      'event': event,
      'timestamp': DateTime.now().toIso8601String(),
      ...properties,
    };
    
    AppLogger.info('Event: $event', data);
  }
}
