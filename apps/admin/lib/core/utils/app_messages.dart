import 'package:flutter/material.dart';

/// Global message system for showing success/error messages
/// Uses global ScaffoldMessenger key for app-wide access
class AppMessages {
  static GlobalKey<ScaffoldMessengerState>? _scaffoldMessengerKey;
  
  /// Initialize with global ScaffoldMessenger key
  static void initialize(GlobalKey<ScaffoldMessengerState> key) {
    _scaffoldMessengerKey = key;
  }
  
  /// Show success message - auto-dismiss after 3 seconds
  static void showSuccess(String message) {
    _scaffoldMessengerKey?.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3), // Auto-dismiss after 3 seconds
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  /// Show error message - user must dismiss manually
  static void showError(String message) {
    _scaffoldMessengerKey?.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(days: 365), // Stays until dismissed
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            _scaffoldMessengerKey?.currentState?.hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  /// Show warning message - auto-dismiss after 4 seconds
  static void showWarning(String message) {
    _scaffoldMessengerKey?.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  /// Show info message - auto-dismiss after 3 seconds
  static void showInfo(String message) {
    _scaffoldMessengerKey?.currentState?.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  /// Hide current SnackBar
  static void hideCurrentMessage() {
    _scaffoldMessengerKey?.currentState?.hideCurrentSnackBar();
  }
  
  /// Clear all SnackBars
  static void clearAllMessages() {
    _scaffoldMessengerKey?.currentState?.clearSnackBars();
  }
}
