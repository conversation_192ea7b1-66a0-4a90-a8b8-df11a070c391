import 'package:flutter/material.dart';

/// Stanford Medicine-inspired theme for MedPulse Admin
/// Designed for medical education with MCQ and FlashCard features
class AppTheme {
  // Stanford Medicine Color Palette
  static const Color medicalBlue = Color(0xFF1976D2);      // Primary - trust, professionalism
  static const Color academicNavy = Color(0xFF0D47A1);     // Deep knowledge, authority
  static const Color stanfordCardinal = Color(0xFF8C1515); // Academic excellence
  static const Color successGreen = Color(0xFF2E7D32);     // Correct answers, progress
  static const Color studyOrange = Color(0xFFFF9800);      // FlashCards, highlights
  static const Color warningAmber = Color(0xFFF57C00);     // Attention, review needed
  static const Color errorRed = Color(0xFFD32F2F);         // Incorrect answers, alerts
  static const Color cleanGray = Color(0xFFF5F5F5);        // Clean backgrounds
  static const Color pureWhite = Color(0xFFFFFFFF);        // Clarity, cleanliness

  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: medicalBlue,
        brightness: Brightness.light,
        primary: medicalBlue,
        secondary: successGreen,
        tertiary: studyOrange,
        error: errorRed,
        surface: pureWhite,
        surfaceContainerHighest: cleanGray,
      ),
      useMaterial3: true,

      // AppBar Theme - Professional medical header
      appBarTheme: AppBarTheme(
        centerTitle: false,
        elevation: 0,
        scrolledUnderElevation: 1,
        backgroundColor: pureWhite,
        foregroundColor: academicNavy,
        titleTextStyle: const TextStyle(
          color: academicNavy,
          fontSize: 20,
          fontWeight: FontWeight.w600,
          fontFamily: 'Source Sans Pro',
        ),
      ),

      // Card Theme - Clean, medical-appropriate cards
      cardTheme: CardTheme(
        elevation: 2,
        color: pureWhite,
        shadowColor: medicalBlue.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: cleanGray,
            width: 1,
          ),
        ),
      ),

      // Button Themes - Professional medical actions
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: medicalBlue,
          foregroundColor: pureWhite,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: medicalBlue,
          side: const BorderSide(color: medicalBlue),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // Typography - Clean, readable medical text
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          color: academicNavy,
          fontSize: 32,
          fontWeight: FontWeight.bold,
          fontFamily: 'Source Sans Pro',
        ),
        headlineMedium: TextStyle(
          color: academicNavy,
          fontSize: 28,
          fontWeight: FontWeight.w600,
          fontFamily: 'Source Sans Pro',
        ),
        headlineSmall: TextStyle(
          color: academicNavy,
          fontSize: 24,
          fontWeight: FontWeight.w600,
          fontFamily: 'Source Sans Pro',
        ),
        titleLarge: TextStyle(
          color: academicNavy,
          fontSize: 22,
          fontWeight: FontWeight.w500,
          fontFamily: 'Source Sans Pro',
        ),
        bodyLarge: TextStyle(
          color: academicNavy,
          fontSize: 16,
          fontWeight: FontWeight.normal,
          fontFamily: 'Source Sans Pro',
        ),
        bodyMedium: TextStyle(
          color: academicNavy,
          fontSize: 14,
          fontWeight: FontWeight.normal,
          fontFamily: 'Source Sans Pro',
        ),
      ),

      // Input Decoration - Clean medical forms
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: cleanGray),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: medicalBlue, width: 2),
        ),
        filled: true,
        fillColor: pureWhite,
      ),
    );
  }

  // Specialized colors for MCQ and FlashCard features
  static const Color mcqCorrect = successGreen;
  static const Color mcqIncorrect = errorRed;
  static const Color mcqNeutral = cleanGray;
  static const Color flashcardFront = studyOrange;
  static const Color flashcardBack = medicalBlue;
  static const Color progressComplete = successGreen;
  static const Color progressPending = warningAmber;
}
