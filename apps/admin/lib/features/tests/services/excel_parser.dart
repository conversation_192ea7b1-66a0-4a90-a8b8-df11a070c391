import 'dart:typed_data';
import 'package:excel/excel.dart';
import 'package:entities/entities.dart';

class ExcelParser {
  /// Parse Excel directly to entities - returns record directly
  static Future<
    ({
      List<QuestionEntity> questions,
      List<TestEntity> tests,
      CoursesEntity courses,
    })
  >
  parseExcelFile(Uint8List bytes, String fileName) async {
    final excel = Excel.decodeBytes(bytes);

    if (excel.tables.isEmpty) {
      throw Exception('Excel file contains no sheets');
    }

    final sheet = excel.tables.values.first;
    if (sheet.rows.isEmpty) {
      throw Exception('Excel sheet is empty');
    }

    // Get headers
    final headers =
        sheet.rows.first
            .map((cell) => cell?.value?.toString().trim().toUpperCase() ?? '')
            .toList();

    // Validate required columns
    const required = ['COURSE', 'YEAR', 'SUBJECT', 'QUIZ NAME'];
    for (final col in required) {
      if (!headers.contains(col)) {
        throw Exception('Missing required column: $col');
      }
    }

    final questions = <QuestionEntity>[];
    final testGroups = <String, List<QuestionEntity>>{};

    // Process each data row
    for (int i = 1; i < sheet.rows.length; i++) {
      final row = sheet.rows[i];
      if (_isEmptyRow(row)) continue;

      final rowData = _extractRowData(row, headers);

      // Get hierarchy
      final courseId = rowData['COURSE'];
      final yearId = rowData['YEAR'];
      final subjectId = rowData['SUBJECT'];
      final testName = rowData['QUIZ NAME'] ?? 'Default Test';

      if (courseId == null || yearId == null || subjectId == null) continue;

      // Create question directly
      final question = _createQuestion(rowData, courseId, yearId, subjectId);
      if (question != null) {
        questions.add(question);

        // Group by test - collect all questions first to determine grouping strategy
        final testKey = '$courseId|$yearId|$subjectId|$testName';
        testGroups.putIfAbsent(testKey, () => []).add(question);
      }
    }

    // Check if we have multiple subjects for smart test grouping
    final isMultipleSubjects = hasMultipleSubjects(questions);

    // Create tests with smart grouping
    final tests = <TestEntity>[];

    if (isMultipleSubjects) {
      // For multi-subject: Group by quiz name only (combine all subjects)
      final quizGroups = <String, List<QuestionEntity>>{};

      for (final entry in testGroups.entries) {
        final parts = entry.key.split('|');
        final quizKey = '${parts[0]}|${parts[1]}|${parts[3]}'; // courseId|yearId|testName
        quizGroups.putIfAbsent(quizKey, () => []).addAll(entry.value);
      }

      for (final entry in quizGroups.entries) {
        final parts = entry.key.split('|');
        final test = TestEntity(
          courseId: parts[0],
          yearId: parts[1],
          subjectId: 'Mock Tests', // Always Mock Tests for multi-subject
          name: parts[2],
          type: entry.value.first.type,
          duration: null,
          difficulty: QuestionDifficulty.easy,
          status: ContentStatus.draft,
          questionIds: [],
        );
        tests.add(test);
      }
    } else {
      // For single subject: Group by subject as before
      for (final entry in testGroups.entries) {
        final parts = entry.key.split('|');
        final test = TestEntity(
          courseId: parts[0],
          yearId: parts[1],
          subjectId: parts[2],
          name: parts[3],
          type: entry.value.first.type,
          duration: null,
          difficulty: QuestionDifficulty.easy,
          status: ContentStatus.draft,
          questionIds: [],
        );
        tests.add(test);
      }
    }

    // Create courses structure
    final courses = _createCoursesStructure(questions);

    return (questions: questions, tests: tests, courses: courses);
  }

  /// Create question
  static QuestionEntity? _createQuestion(
    Map<String, String> rowData,
    String courseId,
    String yearId,
    String subjectId,
  ) {
    // FlipCard?
    final front = rowData['FRONT'];
    final back = rowData['BACK'];

    if (front != null && back != null) {
      return QuestionEntity(
        courseId: courseId,
        yearId: yearId,
        subjectId: subjectId,
        question: front,
        answer: back,
        type: ContentType.flipCard,
        explanation: rowData['DETAILED EXPLANATION'],
        reference: rowData['REFERENCE'],
        difficulty: _parseDifficulty(rowData['DIFFICULTY RATING']),
        status: ContentStatus.published,
      );
    }

    // MCQ?
    final questionText = rowData['QUESTION'];
    if (questionText != null) {
      final options = <String>[];
      for (int i = 1; i <= 5; i++) {
        final option = rowData['ANSWER $i'];
        if (option != null) options.add(option);
      }

      if (options.length >= 2) {
        return QuestionEntity(
          courseId: courseId,
          yearId: yearId,
          subjectId: subjectId,
          question: questionText,
          type: ContentType.mcq,
          options: options,
          correctOptionIndex: _findCorrectIndex(rowData, options),
          explanation: rowData['DETAILED EXPLANATION'],
          reference: rowData['REFERENCE'],
          difficulty: _parseDifficulty(rowData['DIFFICULTY RATING']),
          status: ContentStatus.published,
        );
      }
    }

    return null;
  }

  /// Create courses structure
  static CoursesEntity _createCoursesStructure(List<QuestionEntity> questions) {
    final coursesMap = <String, CourseEntity>{};

    for (final question in questions) {
      final courseId = question.courseId;
      final yearId = question.yearId;
      final subjectId = question.subjectId;

      // Create course if not exists
      if (!coursesMap.containsKey(courseId)) {
        coursesMap[courseId] = CourseEntity(id: courseId, years: {});
      }

      // Create year if not exists
      final course = coursesMap[courseId]!;
      if (!course.years.containsKey(yearId)) {
        coursesMap[courseId] = course.copyWith(
          years: {
            ...course.years,
            yearId: YearEntity(id: yearId, subjects: {}),
          },
        );
      }

      // Create subject if not exists
      final updatedCourse = coursesMap[courseId]!;
      final year = updatedCourse.years[yearId]!;
      if (!year.subjects.containsKey(subjectId)) {
        coursesMap[courseId] = updatedCourse.copyWith(
          years: {
            ...updatedCourse.years,
            yearId: year.copyWith(
              subjects: {
                ...year.subjects,
                subjectId: SubjectEntity(
                  id: subjectId,
                  name: _getSubjectDisplayName(subjectId),
                  tests: {},
                ),
              },
            ),
          },
        );
      }
    }

    return CoursesEntity(courses: coursesMap);
  }

  /// Check if multiple subjects (for Mock Tests conversion)
  static bool hasMultipleSubjects(List<QuestionEntity> questions) {
    final subjects = <String>{};
    for (final question in questions) {
      subjects.add(question.subjectId);
    }
    return subjects.length > 1;
  }

  /// Extract row data
  static Map<String, String> _extractRowData(
    List<Data?> row,
    List<String> headers,
  ) {
    final data = <String, String>{};

    for (int i = 0; i < headers.length && i < row.length; i++) {
      final header = headers[i];
      final value = row[i]?.value?.toString().trim() ?? '';
      if (value.isNotEmpty) {
        data[header] = value;
      }
    }

    return data;
  }

  /// Check if row is empty
  static bool _isEmptyRow(List<Data?> row) {
    return row.every(
      (cell) => cell?.value == null || cell!.value.toString().trim().isEmpty,
    );
  }

  /// Find correct answer index
  static int _findCorrectIndex(
    Map<String, String> rowData,
    List<String> options,
  ) {
    final correctAnswer = rowData['CORRECT ANSWER'];
    if (correctAnswer == null) return 0;

    // Try number first
    final num = int.tryParse(correctAnswer);
    if (num != null && num > 0 && num <= options.length) {
      return num - 1;
    }

    // Try exact match
    for (int i = 0; i < options.length; i++) {
      if (options[i].toLowerCase() == correctAnswer.toLowerCase()) {
        return i;
      }
    }

    return 0;
  }

  /// Parse difficulty
  static QuestionDifficulty _parseDifficulty(String? difficulty) {
    if (difficulty == null) return QuestionDifficulty.easy;

    switch (difficulty.toLowerCase()) {
      case 'medium':
        return QuestionDifficulty.medium;
      case 'hard':
        return QuestionDifficulty.hard;
      default:
        return QuestionDifficulty.easy;
    }
  }

  /// Get proper display name for subject - use original name as-is
  static String _getSubjectDisplayName(String subjectId) {
    // Use the original subject name exactly as it appears in the Excel file
    // This allows users to have any subject names they want without hardcoding
    return subjectId;
  }
}
