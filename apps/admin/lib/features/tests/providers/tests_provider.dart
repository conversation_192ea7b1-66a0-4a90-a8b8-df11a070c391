import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/entities.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../services/published_test_service.dart';

part 'tests_provider.g.dart';

@riverpod
Future<List<TestEntity>> allTests(Ref ref) async {
  // Riverpod automatically wraps exceptions in AsyncValue
  final snapshot = await FirebaseFirestore.instance.collection('tests').get();

  return snapshot.docs
      .map((doc) => TestEntity.fromJson({...doc.data(), 'id': doc.id}))
      .toList();
}

// Individual test publishing state provider
@riverpod
class TestPublishingState extends _$TestPublishingState {
  @override
  AsyncValue<void> build(String testId) => const AsyncValue.data(null);

  Future<void> publishTest(TestEntity test) async {
    if (test.id == null) throw Exception('Test ID required');

    print('🚀 [PUBLISH_FLOW] Starting test publishing for test: ${test.id}');
    print('📝 [PUBLISH_FLOW] Test details: name="${test.name}", course=${test.courseId}, year=${test.yearId}, subject=${test.subjectId}');
    print('❓ [PUBLISH_FLOW] Question count: ${test.questionIds.length} questions');

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      print('⚡ [PUBLISH_FLOW] Starting async publishing process...');
      
      // Step 1: Assemble the published test
      print('🔧 [PUBLISH_FLOW] Step 1: Assembling published test...');
      final publishedTest = await PublishedTestService.assemblePublishedTest(
        testId: test.id!,
      );
      print('✅ [PUBLISH_FLOW] Step 1 Complete: Published test assembled successfully');
      print('📊 [PUBLISH_FLOW] Version: ${publishedTest.version}, Questions: ${publishedTest.questions.length}');

      // Step 2: Save the published test
      print('💾 [PUBLISH_FLOW] Step 2: Saving published test to Firestore...');
      await PublishedTestService.savePublishedTest(publishedTest);
      print('✅ [PUBLISH_FLOW] Step 2 Complete: Published test saved to Firestore');

      // Step 3: Update the original test status
      print('🔄 [PUBLISH_FLOW] Step 3: Updating original test status to published...');
      await FirebaseFirestore.instance.collection('tests').doc(test.id).update({
        'status': ContentStatus.published.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('✅ [PUBLISH_FLOW] Step 3 Complete: Original test status updated');

      print('🔄 [PUBLISH_FLOW] Invalidating tests provider cache...');
      ref.invalidate(allTestsProvider);
      
      print('🎉 [PUBLISH_FLOW] TEST PUBLISHING COMPLETED SUCCESSFULLY!');
      print('🎯 [PUBLISH_FLOW] Test "${test.name}" is now live and available to users');
    });
  }
}

// Individual test deletion state provider
@riverpod
class TestDeletionState extends _$TestDeletionState {
  @override
  AsyncValue<void> build(String testId) => const AsyncValue.data(null);

  Future<void> deleteTest(TestEntity test) async {
    if (test.id == null) throw Exception('Test ID required');

    // Only allow deletion of draft tests
    if (test.status == ContentStatus.published) {
      throw Exception('Cannot delete published tests');
    }

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final firestore = FirebaseFirestore.instance;

      // Step 1: Delete all associated questions
      if (test.questionIds.isNotEmpty) {
        final batch = firestore.batch();

        for (final questionId in test.questionIds) {
          final questionRef = firestore.collection('questions').doc(questionId);
          batch.delete(questionRef);
        }

        await batch.commit();
      }

      // Step 2: Delete the test itself
      await firestore.collection('tests').doc(test.id).delete();

      // Step 3: Refresh the tests list
      ref.invalidate(allTestsProvider);
    });
  }
}

// Individual test update state provider
@riverpod
class TestUpdateState extends _$TestUpdateState {
  @override
  AsyncValue<void> build(String testId) => const AsyncValue.data(null);

  Future<void> updateTest(TestEntity updatedTest) async {
    if (updatedTest.id == null) throw Exception('Test ID required');

    // Only allow updating of draft tests
    if (updatedTest.status == ContentStatus.published) {
      throw Exception('Cannot edit published tests');
    }

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final firestore = FirebaseFirestore.instance;

      // Update the test with current timestamp
      final testData = updatedTest.copyWith(
        updatedAt: DateTime.now(),
      ).toJson();

      await firestore.collection('tests').doc(updatedTest.id).update(testData);

      // Refresh the tests list
      ref.invalidate(allTestsProvider);
    });
  }
}
