import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/entities.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'tests_provider.dart';

part 'questions_provider.g.dart';

@riverpod
Future<QuestionEntity?> question(Ref ref, String questionId) async {
  if (questionId.isEmpty) return null;
  
  final doc = await FirebaseFirestore.instance
      .collection('questions')
      .doc(questionId)
      .get();
  
  if (!doc.exists) return null;
  
  return QuestionEntity.fromJson({...doc.data()!, 'id': doc.id});
}

// Individual question update state provider
@riverpod
class QuestionUpdateState extends _$QuestionUpdateState {
  @override
  AsyncValue<void> build(String questionId) => const AsyncValue.data(null);

  Future<void> updateQuestion(QuestionEntity updatedQuestion) async {
    if (updatedQuestion.id == null) throw Exception('Question ID required');

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      await _validateQuestion(updatedQuestion);

      final firestore = FirebaseFirestore.instance;

      // Update the question with current timestamp
      final questionData = updatedQuestion.copyWith(
        updatedAt: DateTime.now(),
      ).toJson();

      await firestore.collection('questions').doc(updatedQuestion.id).update(questionData);

      // Invalidate question cache
      ref.invalidate(questionProvider(updatedQuestion.id!));
    });
  }

  Future<void> _validateQuestion(QuestionEntity question) async {
    if (question.question.trim().isEmpty) {
      throw Exception('Question text cannot be empty');
    }

    if (question.type == ContentType.mcq) {
      if (question.options.isEmpty) {
        throw Exception('MCQ questions must have at least one option');
      }
      
      if (question.options.length < 2) {
        throw Exception('MCQ questions must have at least 2 options');
      }

      if (question.correctOptionIndex < 0 || 
          question.correctOptionIndex >= question.options.length) {
        throw Exception('Invalid correct option index');
      }

      // Check for empty options
      for (int i = 0; i < question.options.length; i++) {
        if (question.options[i].trim().isEmpty) {
          throw Exception('Option ${i + 1} cannot be empty');
        }
      }

      // Check for duplicate options
      final uniqueOptions = question.options.map((o) => o.trim().toLowerCase()).toSet();
      if (uniqueOptions.length != question.options.length) {
        throw Exception('Options cannot be duplicated');
      }
    }

    if (question.type == ContentType.flipCard && 
        (question.answer?.trim().isEmpty ?? true)) {
      throw Exception('FlipCard questions must have an answer');
    }
  }
}

// Individual question deletion state provider
@riverpod
class QuestionDeletionState extends _$QuestionDeletionState {
  @override
  AsyncValue<void> build(String questionId) => const AsyncValue.data(null);

  Future<void> deleteQuestion(QuestionEntity question, {String? testId}) async {
    if (question.id == null) throw Exception('Question ID required');

    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final firestore = FirebaseFirestore.instance;

      // Check if question is used in other tests
      final testsQuery = await firestore
          .collection('tests')
          .where('questionIds', arrayContains: question.id)
          .get();

      final affectedTests = <TestEntity>[];
      for (final doc in testsQuery.docs) {
        final test = TestEntity.fromJson({...doc.data(), 'id': doc.id});
        affectedTests.add(test);
      }

      // Remove question from all affected tests
      final batch = firestore.batch();
      
      for (final test in affectedTests) {
        final updatedQuestionIds = test.questionIds
            .where((id) => id != question.id)
            .toList();
        
        final testRef = firestore.collection('tests').doc(test.id);
        batch.update(testRef, {
          'questionIds': updatedQuestionIds,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // Delete the question
      final questionRef = firestore.collection('questions').doc(question.id);
      batch.delete(questionRef);

      await batch.commit();

      // Invalidate caches
      ref.invalidate(questionProvider(question.id!));
      ref.invalidate(allTestsProvider);
    });
  }
}

// Question creation state provider
@riverpod
class QuestionCreationState extends _$QuestionCreationState {
  @override
  AsyncValue<String?> build() => const AsyncValue.data(null);

  Future<String> createQuestion(QuestionEntity question) async {
    state = const AsyncValue.loading();

    final result = await AsyncValue.guard(() async {
      await ref.read(questionUpdateStateProvider('').notifier)._validateQuestion(question);

      final firestore = FirebaseFirestore.instance;
      
      // Create question with auto-generated ID
      final docRef = firestore.collection('questions').doc();
      final questionWithId = question.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await docRef.set(questionWithId.toJson());

      return docRef.id;
    });

    state = result;
    return result.value!;
  }
}
