import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'providers/tests_provider.dart';
import 'widgets/tests_header.dart';
import 'widgets/test_card.dart';
import 'widgets/empty_tests_state.dart';
import 'widgets/tests_error_state.dart';

class TestsScreen extends ConsumerWidget {
  const TestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: const Padding(
        padding: EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and actions
            TestsHeader(),
            SizedBox(height: 32),
            // Tests list
            Expanded(child: _Tests()),
          ],
        ),
      ),
    );
  }
}

class _Tests extends ConsumerWidget {
  const _Tests();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tests = ref.watch(allTestsProvider);

    if (tests.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (tests.hasError) {
      return TestsErrorState(error: tests.error!);
    }

    final testsList = tests.value!;

    if (testsList.isEmpty) {
      return const EmptyTestsState();
    }

    return ListView.builder(
      itemCount: testsList.length,
      itemBuilder: (context, index) {
        final test = testsList[index];
        return TestCard(test: test);
      },
    );
  }
}
