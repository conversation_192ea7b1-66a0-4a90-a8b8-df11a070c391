import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../providers/tests_provider.dart';
import '../widgets/confirmation_dialog.dart';
import 'edit_test_screen.dart';
import 'view_question_screen.dart';

class ViewTestScreen extends ConsumerWidget {
  final TestEntity test;

  const ViewTestScreen({
    super.key,
    required this.test,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch deletion state
    final deletionState = ref.watch(testDeletionStateProvider(test.id ?? ''));
    final isDraft = test.status != ContentStatus.published;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Details'),
        backgroundColor: const Color(0xFF5C6BC0),
        foregroundColor: Colors.white,
        actions: [
          // Edit button - only show if test is not published
          if (isDraft)
            IconButton(
              onPressed: deletionState.isLoading ? null : () => _editTest(context),
              icon: const Icon(Icons.edit, color: Colors.white),
              tooltip: 'Edit test',
            ),
          // Delete button - only show if test is not published
          if (isDraft)
            IconButton(
              onPressed: deletionState.isLoading ? null : () => _deleteTest(context, ref),
              icon: deletionState.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.delete, color: Colors.white),
              tooltip: deletionState.isLoading ? 'Deleting...' : 'Delete test',
            ),
          const SizedBox(width: 8),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Header
            _TestHeader(test: test),
            
            const SizedBox(height: 24),
            
            // Test Information
            _TestInformation(test: test),
            
            const SizedBox(height: 24),
            
            // Test Statistics
            _TestStatistics(test: test),
            
            const SizedBox(height: 24),
            
            // Test Metadata
            _TestMetadata(test: test),
            
            const SizedBox(height: 24),
            
            // Questions Section
            _TestQuestionsSection(test: test),
          ],
        ),
      ),
    );
  }

  void _editTest(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditTestScreen(test: test),
      ),
    );
  }

  Future<void> _deleteTest(BuildContext context, WidgetRef ref) async {
    await ConfirmationDialog.showDeleteTestDialog(
      context: context,
      testName: test.name,
      onConfirm: () {
        ref.read(testDeletionStateProvider(test.id ?? '').notifier)
            .deleteTest(test)
            .then((_) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Test deleted successfully')),
            );
            Navigator.of(context).pop(); // Go back to previous screen
          }
        }).catchError((error) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error deleting test: $error')),
            );
          }
        });
      },
    );
  }
}

// Comprehensive Questions Section with integrated viewing
class _TestQuestionsSection extends ConsumerStatefulWidget {
  final TestEntity test;

  const _TestQuestionsSection({required this.test});

  @override
  ConsumerState<_TestQuestionsSection> createState() => _TestQuestionsSectionState();
}

class _TestQuestionsSectionState extends ConsumerState<_TestQuestionsSection> {
  List<QuestionEntity> _questions = [];
  bool _isLoading = true;
  String? _error;
  int? _expandedQuestionIndex;
  bool _showCompactView = true;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
  }

  Future<void> _loadQuestions() async {
    if (widget.test.questionIds.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final firestore = FirebaseFirestore.instance;
      final questionDocs = await Future.wait(
        widget.test.questionIds.map((id) => firestore.collection('questions').doc(id).get()),
      );

      final questions = <QuestionEntity>[];
      for (int i = 0; i < questionDocs.length; i++) {
        final doc = questionDocs[i];
        if (doc.exists && doc.data() != null) {
          questions.add(QuestionEntity.fromJson({
            ...doc.data()!,
            'id': doc.id,
          }));
        }
      }

      setState(() {
        _questions = questions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to load questions: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _toggleQuestion(int index) {
    setState(() {
      if (_expandedQuestionIndex == index) {
        _expandedQuestionIndex = null;
      } else {
        _expandedQuestionIndex = index;
      }
    });
  }

  void _viewQuestionDetails(QuestionEntity question) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ViewQuestionScreen(
          question: question,
          testId: widget.test.id,
        ),
      ),
    );
  }

  bool get _isTestPublished => widget.test.status == ContentStatus.published;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with toggle controls
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const Text(
                      'Questions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 12),
                    if (_isLoading)
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    else
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_questions.length} questions',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[700],
                          ),
                        ),
                      ),
                  ],
                ),
                if (!_isLoading && _questions.isNotEmpty)
                  Row(
                    children: [
                      IconButton(
                        onPressed: _loadQuestions,
                        icon: const Icon(Icons.refresh, size: 20),
                        tooltip: 'Refresh questions',
                        style: IconButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _showCompactView = !_showCompactView;
                            _expandedQuestionIndex = null;
                          });
                        },
                        icon: Icon(
                          _showCompactView ? Icons.view_list : Icons.view_agenda,
                          size: 20,
                        ),
                        tooltip: _showCompactView ? 'Detailed view' : 'Compact view',
                        style: IconButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Content
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(40),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_error != null)
              _ErrorState(
                error: _error!,
                onRetry: _loadQuestions,
              )
            else if (_questions.isEmpty)
              _EmptyQuestionsState()
            else
              _QuestionsList(
                questions: _questions,
                showCompactView: _showCompactView,
                expandedIndex: _expandedQuestionIndex,
                onToggleExpand: _toggleQuestion,
                onViewDetails: _viewQuestionDetails,
              ),
          ],
        ),
      ),
    );
  }
}

// Error state widget
class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading questions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

// Empty state widget
class _EmptyQuestionsState extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Icon(
              Icons.quiz_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No questions found',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This test doesn\'t have any questions yet.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Questions list widget
class _QuestionsList extends StatelessWidget {
  final List<QuestionEntity> questions;
  final bool showCompactView;
  final int? expandedIndex;
  final Function(int) onToggleExpand;
  final Function(QuestionEntity) onViewDetails;

  const _QuestionsList({
    required this.questions,
    required this.showCompactView,
    required this.expandedIndex,
    required this.onToggleExpand,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: questions.asMap().entries.map((entry) {
        final index = entry.key;
        final question = entry.value;
        final isExpanded = expandedIndex == index;
        
        return showCompactView
            ? _CompactQuestionItem(
                question: question,
                index: index,
                isExpanded: isExpanded,
                onToggle: () => onToggleExpand(index),
                onViewDetails: () => onViewDetails(question),
              )
            : _DetailedQuestionItem(
                question: question,
                index: index,
                onViewDetails: () => onViewDetails(question),
              );
      }).toList(),
    );
  }
}

// Compact question item with expandable details
class _CompactQuestionItem extends StatelessWidget {
  final QuestionEntity question;
  final int index;
  final bool isExpanded;
  final VoidCallback onToggle;
  final VoidCallback onViewDetails;

  const _CompactQuestionItem({
    required this.question,
    required this.index,
    required this.isExpanded,
    required this.onToggle,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Question header
          InkWell(
            onTap: onToggle,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Question number
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF5C6BC0),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Question preview
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          question.question.length > 100
                              ? '${question.question.substring(0, 100)}...'
                              : question.question,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            _QuestionTypeChip(question: question),
                            const SizedBox(width: 8),
                            _DifficultyChip(question: question),
                            if (question.type == ContentType.mcq) ...[
                              const SizedBox(width: 8),
                              _OptionsCountChip(optionsCount: question.options.length),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Action buttons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: onViewDetails,
                        icon: const Icon(Icons.open_in_new, size: 18),
                        tooltip: 'View details',
                        style: IconButton.styleFrom(
                          foregroundColor: Colors.blue[700],
                        ),
                      ),
                      Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Expanded content
          if (isExpanded) _ExpandedQuestionContent(question: question),
        ],
      ),
    );
  }
}

// Detailed question item (always shows full content)
class _DetailedQuestionItem extends StatelessWidget {
  final QuestionEntity question;
  final int index;
  final VoidCallback onViewDetails;

  const _DetailedQuestionItem({
    required this.question,
    required this.index,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFF5C6BC0),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Row(
                    children: [
                      _QuestionTypeChip(question: question),
                      const SizedBox(width: 8),
                      _DifficultyChip(question: question),
                      if (question.type == ContentType.mcq) ...[
                        const SizedBox(width: 8),
                        _OptionsCountChip(optionsCount: question.options.length),
                      ],
                    ],
                  ),
                ),
                IconButton(
                  onPressed: onViewDetails,
                  icon: const Icon(Icons.open_in_new, size: 18),
                  tooltip: 'View full details',
                  style: IconButton.styleFrom(
                    foregroundColor: Colors.blue[700],
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          _ExpandedQuestionContent(question: question),
        ],
      ),
    );
  }
}

// Expanded question content
class _ExpandedQuestionContent extends StatelessWidget {
  final QuestionEntity question;

  const _ExpandedQuestionContent({required this.question});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question text
          Text(
            question.question,
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
          
          // Question image
          if (question.questionImage != null && question.questionImage!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  question.questionImage!,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 100,
                      color: Colors.grey[100],
                      child: const Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.broken_image, color: Colors.grey),
                            SizedBox(width: 8),
                            Text('Image not available'),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
          
          const SizedBox(height: 16),
          
          // Answer/Options based on type
          if (question.type == ContentType.mcq)
            _InlineMCQOptions(question: question)
          else if (question.type == ContentType.flipCard && question.answer != null)
            _InlineFlipCardAnswer(answer: question.answer!),
          
          // Additional info
          if (question.explanation?.isNotEmpty ?? false) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb_outline, size: 16, color: Colors.blue[700]),
                      const SizedBox(width: 6),
                      Text(
                        'Explanation',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    question.explanation!,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Keywords
          if (question.keywords.isNotEmpty) ...[
            const SizedBox(height: 12),
            Wrap(
              spacing: 6,
              runSpacing: 6,
              children: [
                Text(
                  'Keywords:',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                ...question.keywords.map((keyword) => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    keyword,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.black87,
                    ),
                  ),
                )),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

// Inline MCQ options display
class _InlineMCQOptions extends StatelessWidget {
  final QuestionEntity question;

  const _InlineMCQOptions({required this.question});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.list_alt, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 6),
            Text(
              'Options:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...question.options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;
          final isCorrect = index == question.correctOptionIndex;
          
          return Container(
            margin: const EdgeInsets.only(bottom: 6),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: isCorrect 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.05),
              border: Border.all(
                color: isCorrect 
                    ? Colors.green.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.2),
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: isCorrect ? Colors.green : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      String.fromCharCode(65 + index), // A, B, C, D
                      style: TextStyle(
                        color: isCorrect ? Colors.white : Colors.black54,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    option,
                    style: TextStyle(
                      fontSize: 13,
                      color: isCorrect ? Colors.green[800] : Colors.black87,
                      fontWeight: isCorrect ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                ),
                if (isCorrect)
                  Icon(
                    Icons.check_circle,
                    color: Colors.green[600],
                    size: 16,
                  ),
              ],
            ),
          );
        }),
      ],
    );
  }
}

// Inline FlipCard answer display
class _InlineFlipCardAnswer extends StatelessWidget {
  final String answer;

  const _InlineFlipCardAnswer({required this.answer});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flip, size: 16, color: Colors.blue[700]),
              const SizedBox(width: 6),
              Text(
                'Answer:',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            answer,
            style: const TextStyle(
              fontSize: 13,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}

// Helper chip widgets
class _QuestionTypeChip extends StatelessWidget {
  final QuestionEntity question;

  const _QuestionTypeChip({required this.question});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: question.type == ContentType.mcq 
            ? Colors.blue.withValues(alpha: 0.1)
            : Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        question.type == ContentType.mcq ? 'MCQ' : 'FlipCard',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: question.type == ContentType.mcq 
              ? Colors.blue[700]
              : Colors.purple[700],
        ),
      ),
    );
  }
}

class _DifficultyChip extends StatelessWidget {
  final QuestionEntity question;

  const _DifficultyChip({required this.question});

  Color _getDifficultyColor(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return Colors.green;
      case QuestionDifficulty.medium:
        return Colors.orange;
      case QuestionDifficulty.hard:
        return Colors.red;
    }
  }

  String _getDifficultyText(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }

  @override
  Widget build(BuildContext context) {
    final color = _getDifficultyColor(question.difficulty);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getDifficultyText(question.difficulty),
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }
}

class _OptionsCountChip extends StatelessWidget {
  final int optionsCount;

  const _OptionsCountChip({required this.optionsCount});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '$optionsCount opts',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: Colors.grey[700],
        ),
      ),
    );
  }
}

class _TestHeader extends StatelessWidget {
  final TestEntity test;

  const _TestHeader({required this.test});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    test.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w700,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    test.type == ContentType.mcq ? 'MCQ Test' : 'FlipCard Test',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: test.type == ContentType.mcq 
                          ? Colors.blue[700] 
                          : Colors.purple[700],
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _InfoChip(
                        label: 'Course',
                        value: test.courseId,
                        color: Colors.indigo,
                      ),
                      const SizedBox(width: 8),
                      _InfoChip(
                        label: 'Year',
                        value: test.yearId,
                        color: Colors.teal,
                      ),
                      const SizedBox(width: 8),
                      _InfoChip(
                        label: 'Subject',
                        value: test.subjectId,
                        color: Colors.orange,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Column(
              children: [
                // Status Chip
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: test.status == ContentStatus.published
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: test.status == ContentStatus.published
                          ? Colors.green.withValues(alpha: 0.3)
                          : Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    test.status == ContentStatus.published ? 'Published' : 'Draft',
                    style: TextStyle(
                      fontSize: 12,
                      color: test.status == ContentStatus.published 
                          ? Colors.green[700] 
                          : Colors.orange[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Difficulty Chip
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getDifficultyColor(test.difficulty).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getDifficultyColor(test.difficulty).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _getDifficultyDisplayName(test.difficulty),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getDifficultyColor(test.difficulty),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return Colors.green;
      case QuestionDifficulty.medium:
        return Colors.orange;
      case QuestionDifficulty.hard:
        return Colors.red;
    }
  }

  String _getDifficultyDisplayName(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }
}

class _InfoChip extends StatelessWidget {
  final String label;
  final String value;
  final MaterialColor color;

  const _InfoChip({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          fontSize: 12,
          color: color[700],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

class _TestInformation extends StatelessWidget {
  final TestEntity test;

  const _TestInformation({required this.test});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            _InfoRow(
              label: 'Duration',
              value: test.duration != null ? '${test.duration} minutes' : 'No time limit',
            ),
            _InfoRow(
              label: 'Type',
              value: test.type == ContentType.mcq ? 'MCQ' : 'FlipCard',
            ),
            _InfoRow(
              label: 'Difficulty',
              value: _getDifficultyDisplayName(test.difficulty),
            ),
          ],
        ),
      ),
    );
  }

  String _getDifficultyDisplayName(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }
}

class _TestStatistics extends StatelessWidget {
  final TestEntity test;

  const _TestStatistics({required this.test});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Statistics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
_StatCard(
              title: 'Total Questions',
              value: test.questionIds.length.toString(),
              icon: Icons.quiz,
              color: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final MaterialColor color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 32,
            color: color[600],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color[700],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: color[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _TestMetadata extends StatelessWidget {
  final TestEntity test;

  const _TestMetadata({required this.test});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Metadata',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            if (test.createdAt != null)
              _MetadataRow(
                label: 'Created',
                value: _formatDate(test.createdAt!),
              ),
            if (test.updatedAt != null)
              _MetadataRow(
                label: 'Updated',
                value: _formatDate(test.updatedAt!),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _MetadataRow extends StatelessWidget {
  final String label;
  final String value;

  const _MetadataRow({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
