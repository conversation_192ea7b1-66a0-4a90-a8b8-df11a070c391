import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';

import '../providers/questions_provider.dart';

class EditQuestionScreen extends ConsumerStatefulWidget {
  final QuestionEntity question;
  final String? testId;

  const EditQuestionScreen({
    super.key,
    required this.question,
    this.testId,
  });

  @override
  ConsumerState<EditQuestionScreen> createState() => _EditQuestionScreenState();
}

class _EditQuestionScreenState extends ConsumerState<EditQuestionScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _questionController;
  late final TextEditingController _answerController;
  late final TextEditingController _explanationController;
  late final TextEditingController _referenceController;
  late final TextEditingController _keywordsController;
  
  late List<TextEditingController> _optionControllers;
  late ContentType _selectedType;
  late QuestionDifficulty _selectedDifficulty;
  late int _correctOptionIndex;

  bool _isDirty = false;
  late Map<String, dynamic> _originalValues;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _storeOriginalValues();
  }

  void _initializeControllers() {
    _questionController = TextEditingController(text: widget.question.question);
    _answerController = TextEditingController(text: widget.question.answer ?? '');
    _explanationController = TextEditingController(text: widget.question.explanation ?? '');
    _referenceController = TextEditingController(text: widget.question.reference ?? '');
    _keywordsController = TextEditingController(text: widget.question.keywords.join(', '));
    
    _selectedType = widget.question.type;
    _selectedDifficulty = widget.question.difficulty;
    _correctOptionIndex = widget.question.correctOptionIndex;

    // Initialize option controllers
    _optionControllers = [];
    if (widget.question.type == ContentType.mcq) {
      for (int i = 0; i < widget.question.options.length; i++) {
        _optionControllers.add(TextEditingController(text: widget.question.options[i]));
      }
      // Ensure at least 2 options
      while (_optionControllers.length < 2) {
        _optionControllers.add(TextEditingController());
      }
    }

    // Add listeners to track changes
    _questionController.addListener(_trackChanges);
    _answerController.addListener(_trackChanges);
    _explanationController.addListener(_trackChanges);
    _referenceController.addListener(_trackChanges);
    _keywordsController.addListener(_trackChanges);
    for (final controller in _optionControllers) {
      controller.addListener(_trackChanges);
    }
  }

  void _storeOriginalValues() {
    _originalValues = {
      'question': widget.question.question,
      'answer': widget.question.answer ?? '',
      'explanation': widget.question.explanation ?? '',
      'reference': widget.question.reference ?? '',
      'keywords': widget.question.keywords.join(', '),
      'type': _selectedType,
      'difficulty': _selectedDifficulty,
      'correctOptionIndex': _correctOptionIndex,
      'options': widget.question.options.toList(),
    };
  }

  void _trackChanges() {
    final currentValues = {
      'question': _questionController.text,
      'answer': _answerController.text,
      'explanation': _explanationController.text,
      'reference': _referenceController.text,
      'keywords': _keywordsController.text,
      'type': _selectedType,
      'difficulty': _selectedDifficulty,
      'correctOptionIndex': _correctOptionIndex,
      'options': _optionControllers.map((c) => c.text).toList(),
    };

    final isDirty = !_mapsEqual(_originalValues, currentValues);
    if (_isDirty != isDirty) {
      setState(() {
        _isDirty = isDirty;
      });
    }
  }

  bool _mapsEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.length != map2.length) return false;
    
    for (final key in map1.keys) {
      if (map1[key] is List && map2[key] is List) {
        final list1 = map1[key] as List;
        final list2 = map2[key] as List;
        if (list1.length != list2.length) return false;
        for (int i = 0; i < list1.length; i++) {
          if (list1[i] != list2[i]) return false;
        }
      } else if (map1[key] != map2[key]) {
        return false;
      }
    }
    return true;
  }

  @override
  void dispose() {
    _questionController.dispose();
    _answerController.dispose();
    _explanationController.dispose();
    _referenceController.dispose();
    _keywordsController.dispose();
    for (final controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _saveQuestion() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_isDirty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No changes to save')),
      );
      return;
    }

    try {
      final keywords = _keywordsController.text
          .split(',')
          .map((k) => k.trim())
          .where((k) => k.isNotEmpty)
          .toList();

      final updatedQuestion = widget.question.copyWith(
        question: _questionController.text.trim(),
        type: _selectedType,
        difficulty: _selectedDifficulty,
        answer: _selectedType == ContentType.flipCard ? _answerController.text.trim() : null,
        options: _selectedType == ContentType.mcq 
            ? _optionControllers.map((c) => c.text.trim()).where((o) => o.isNotEmpty).toList()
            : [],
        correctOptionIndex: _selectedType == ContentType.mcq ? _correctOptionIndex : 0,
        explanation: _explanationController.text.trim().isEmpty ? null : _explanationController.text.trim(),
        reference: _referenceController.text.trim().isEmpty ? null : _referenceController.text.trim(),
        keywords: keywords,
      );

      await ref.read(questionUpdateStateProvider(widget.question.id!).notifier).updateQuestion(updatedQuestion);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Question updated successfully')),
        );
        Navigator.of(context).pop();
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating question: $error')),
        );
      }
    }
  }

  Future<bool> _onWillPop() async {
    if (!_isDirty) return true;

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text('You have unsaved changes. Are you sure you want to leave?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Stay'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Leave'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final updateState = ref.watch(questionUpdateStateProvider(widget.question.id ?? ''));

    return PopScope(
      canPop: !_isDirty,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _isDirty) {
          final shouldPop = await _onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Edit Question'),
          backgroundColor: const Color(0xFF5C6BC0),
          foregroundColor: Colors.white,
          actions: [
            TextButton.icon(
              onPressed: updateState.isLoading || !_isDirty ? null : _saveQuestion,
              icon: updateState.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.save, color: Colors.white),
              label: Text(
                updateState.isLoading ? 'Saving...' : 'Save',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Question Information Card
                _QuestionInfoCard(
                  question: widget.question,
                  selectedType: _selectedType,
                  selectedDifficulty: _selectedDifficulty,
                  onTypeChanged: (type) {
                    setState(() {
                      _selectedType = type;
                      if (type == ContentType.mcq && _optionControllers.length < 2) {
                        while (_optionControllers.length < 2) {
                          _optionControllers.add(TextEditingController());
                        }
                      }
                    });
                    _trackChanges();
                  },
                  onDifficultyChanged: (difficulty) {
                    setState(() {
                      _selectedDifficulty = difficulty;
                    });
                    _trackChanges();
                  },
                ),

                const SizedBox(height: 24),

                // Question Content Card
                _QuestionContentCard(
                  questionController: _questionController,
                ),

                const SizedBox(height: 24),

                // Type-specific content
                if (_selectedType == ContentType.mcq) ...[
                  _MCQOptionsCard(
                    optionControllers: _optionControllers,
                    correctOptionIndex: _correctOptionIndex,
                    onCorrectOptionChanged: (index) {
                      setState(() {
                        _correctOptionIndex = index;
                      });
                      _trackChanges();
                    },
                    onAddOption: _addOption,
                    onRemoveOption: _removeOption,
                  ),
                ] else ...[
                  _FlipCardAnswerCard(
                    answerController: _answerController,
                  ),
                ],

                const SizedBox(height: 24),

                // Additional Information Card
                _AdditionalInfoCard(
                  explanationController: _explanationController,
                  referenceController: _referenceController,
                  keywordsController: _keywordsController,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _addOption() {
    if (_optionControllers.length < 6) {
      setState(() {
        _optionControllers.add(TextEditingController());
      });
      _trackChanges();
    }
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
        
        // Adjust correct option index if necessary
        if (_correctOptionIndex >= index) {
          _correctOptionIndex = (_correctOptionIndex > 0) ? _correctOptionIndex - 1 : 0;
        }
      });
      _trackChanges();
    }
  }
}

class _QuestionInfoCard extends StatelessWidget {
  final QuestionEntity question;
  final ContentType selectedType;
  final QuestionDifficulty selectedDifficulty;
  final ValueChanged<ContentType> onTypeChanged;
  final ValueChanged<QuestionDifficulty> onDifficultyChanged;

  const _QuestionInfoCard({
    required this.question,
    required this.selectedType,
    required this.selectedDifficulty,
    required this.onTypeChanged,
    required this.onDifficultyChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Question Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),

            // Read-only fields
            _ReadOnlyField(label: 'Course', value: question.courseId),
            const SizedBox(height: 12),
            _ReadOnlyField(label: 'Year', value: question.yearId),
            const SizedBox(height: 12),
            _ReadOnlyField(label: 'Subject', value: question.subjectId),
            const SizedBox(height: 16),

            Row(
              children: [
                // Type dropdown
                Expanded(
                  child: DropdownButtonFormField<ContentType>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Type',
                      border: OutlineInputBorder(),
                    ),
                    items: ContentType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type == ContentType.mcq ? 'MCQ' : 'FlipCard'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) onTypeChanged(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                
                // Difficulty dropdown
                Expanded(
                  child: DropdownButtonFormField<QuestionDifficulty>(
                    value: selectedDifficulty,
                    decoration: const InputDecoration(
                      labelText: 'Difficulty',
                      border: OutlineInputBorder(),
                    ),
                    items: QuestionDifficulty.values.map((difficulty) {
                      return DropdownMenuItem(
                        value: difficulty,
                        child: Text(_getDifficultyDisplayName(difficulty)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) onDifficultyChanged(value);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getDifficultyDisplayName(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }
}

class _ReadOnlyField extends StatelessWidget {
  final String label;
  final String value;

  const _ReadOnlyField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}

class _QuestionContentCard extends StatelessWidget {
  final TextEditingController questionController;

  const _QuestionContentCard({
    required this.questionController,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Question Content',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: questionController,
              decoration: const InputDecoration(
                labelText: 'Question Text',
                hintText: 'Enter the question...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a question';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _MCQOptionsCard extends StatelessWidget {
  final List<TextEditingController> optionControllers;
  final int correctOptionIndex;
  final ValueChanged<int> onCorrectOptionChanged;
  final VoidCallback onAddOption;
  final ValueChanged<int> onRemoveOption;

  const _MCQOptionsCard({
    required this.optionControllers,
    required this.correctOptionIndex,
    required this.onCorrectOptionChanged,
    required this.onAddOption,
    required this.onRemoveOption,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'MCQ Options',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                if (optionControllers.length < 6)
                  TextButton.icon(
                    onPressed: onAddOption,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Option'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            ...optionControllers.asMap().entries.map((entry) {
              final index = entry.key;
              final controller = entry.value;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: Row(
                  children: [
                    // Correct option radio button
                    Radio<int>(
                      value: index,
                      groupValue: correctOptionIndex,
                      onChanged: (value) {
                        if (value != null) onCorrectOptionChanged(value);
                      },
                    ),
                    
                    // Option letter
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: index == correctOptionIndex ? Colors.green : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          String.fromCharCode(65 + index), // A, B, C, D
                          style: TextStyle(
                            color: index == correctOptionIndex ? Colors.white : Colors.black54,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // Option text field
                    Expanded(
                      child: TextFormField(
                        controller: controller,
                        decoration: InputDecoration(
                          labelText: 'Option ${String.fromCharCode(65 + index)}',
                          border: const OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter option text';
                          }
                          return null;
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // Remove button (only if more than 2 options)
                    if (optionControllers.length > 2)
                      IconButton(
                        onPressed: () => onRemoveOption(index),
                        icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
                        tooltip: 'Remove option',
                      ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class _FlipCardAnswerCard extends StatelessWidget {
  final TextEditingController answerController;

  const _FlipCardAnswerCard({
    required this.answerController,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Answer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: answerController,
              decoration: const InputDecoration(
                labelText: 'Answer Text',
                hintText: 'Enter the answer...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter an answer for the FlipCard question';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _AdditionalInfoCard extends StatelessWidget {
  final TextEditingController explanationController;
  final TextEditingController referenceController;
  final TextEditingController keywordsController;

  const _AdditionalInfoCard({
    required this.explanationController,
    required this.referenceController,
    required this.keywordsController,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Additional Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 20),
            
            // Explanation
            TextFormField(
              controller: explanationController,
              decoration: const InputDecoration(
                labelText: 'Explanation (Optional)',
                hintText: 'Enter explanation for the answer...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
            ),
            
            const SizedBox(height: 16),
            
            // Reference
            TextFormField(
              controller: referenceController,
              decoration: const InputDecoration(
                labelText: 'Reference (Optional)',
                hintText: 'Enter reference material...',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Keywords
            TextFormField(
              controller: keywordsController,
              decoration: const InputDecoration(
                labelText: 'Keywords (Optional)',
                hintText: 'Enter keywords separated by commas...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
