import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../providers/questions_provider.dart';
import '../widgets/confirmation_dialog.dart';
import 'edit_question_screen.dart';

// Provider to fetch test data by ID
final testByIdProvider = FutureProvider.family<TestEntity?, String>((ref, testId) async {
  if (testId.isEmpty) return null;
  
  try {
    final doc = await FirebaseFirestore.instance.collection('tests').doc(testId).get();
    if (doc.exists && doc.data() != null) {
      return TestEntity.fromJson({...doc.data()!, 'id': doc.id});
    }
    return null;
  } catch (e) {
    return null;
  }
});

class ViewQuestionScreen extends ConsumerWidget {
  final QuestionEntity question;
  final String? testId;

  const ViewQuestionScreen({
    super.key,
    required this.question,
    this.testId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch deletion state
    final deletionState = ref.watch(questionDeletionStateProvider(question.id ?? ''));
    
    // Watch test data if testId is provided to check if test is published
    final testData = testId != null ? ref.watch(testByIdProvider(testId!)) : null;
    
    // Determine if editing/deleting is allowed
    bool canEdit = true;
    String? restrictionReason;
    
    if (testData != null) {
      testData.when(
        data: (test) {
          if (test != null && test.status == ContentStatus.published) {
            canEdit = false;
            restrictionReason = 'Questions in published tests cannot be modified';
          }
        },
        loading: () => canEdit = false, // Disable while loading
        error: (_, __) => canEdit = false, // Disable on error
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Question Details'),
        backgroundColor: const Color(0xFF5C6BC0),
        foregroundColor: Colors.white,
        actions: [
          // Edit button - only show if editing is allowed
          if (canEdit)
            IconButton(
              onPressed: deletionState.isLoading ? null : () => _editQuestion(context),
              icon: const Icon(Icons.edit, color: Colors.white),
              tooltip: 'Edit question',
            ),
          // Delete button - only show if editing is allowed
          if (canEdit)
            IconButton(
              onPressed: deletionState.isLoading ? null : () => _deleteQuestion(context, ref),
              icon: deletionState.isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.delete, color: Colors.white),
              tooltip: deletionState.isLoading ? 'Deleting...' : 'Delete question',
            ),
          // Info button when editing is restricted
          if (!canEdit && restrictionReason != null)
            IconButton(
              onPressed: () => _showRestrictionInfo(context, restrictionReason!),
              icon: const Icon(Icons.info_outline, color: Colors.white70),
              tooltip: 'Why can\'t I edit this question?',
            ),
          const SizedBox(width: 8),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question Header
            _QuestionHeader(question: question),
            
            const SizedBox(height: 24),
            
            // Question Content
            _QuestionContent(question: question),
            
            if (question.type == ContentType.mcq) ...[
              const SizedBox(height: 24),
              _MCQOptions(question: question),
            ],
            
            if (question.type == ContentType.flipCard && question.answer != null) ...[
              const SizedBox(height: 24),
              _FlipCardAnswer(answer: question.answer!),
            ],
            
            if (question.explanation?.isNotEmpty ?? false) ...[
              const SizedBox(height: 24),
              _QuestionExplanation(explanation: question.explanation!),
            ],
            
            if (question.reference?.isNotEmpty ?? false) ...[
              const SizedBox(height: 24),
              _QuestionReference(reference: question.reference!),
            ],
            
            if (question.keywords.isNotEmpty) ...[
              const SizedBox(height: 24),
              _QuestionKeywords(keywords: question.keywords),
            ],
            
            const SizedBox(height: 24),
            _QuestionMetadata(question: question),
          ],
        ),
      ),
    );
  }

  void _editQuestion(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => EditQuestionScreen(
          question: question,
          testId: testId,
        ),
      ),
    );
  }

  Future<void> _deleteQuestion(BuildContext context, WidgetRef ref) async {
    await ConfirmationDialog.showDeleteQuestionDialog(
      context: context,
      question: question,
      onConfirm: () {
        ref.read(questionDeletionStateProvider(question.id ?? '').notifier)
            .deleteQuestion(question, testId: testId)
            .then((_) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Question deleted successfully')),
            );
            Navigator.of(context).pop(); // Go back to previous screen
          }
        }).catchError((error) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Error deleting question: $error')),
            );
          }
        });
      },
    );
  }

  void _showRestrictionInfo(BuildContext context, String reason) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock_outlined, color: Colors.orange),
            SizedBox(width: 8),
            Text('Edit Restricted'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              reason,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'To modify questions, the test must be in draft status.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

class _QuestionHeader extends StatelessWidget {
  final QuestionEntity question;

  const _QuestionHeader({required this.question});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    question.type == ContentType.mcq ? 'MCQ Question' : 'FlipCard Question',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: question.type == ContentType.mcq 
                          ? Colors.blue[700] 
                          : Colors.purple[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _InfoChip(
                        label: 'Course',
                        value: question.courseId,
                        color: Colors.indigo,
                      ),
                      const SizedBox(width: 8),
                      _InfoChip(
                        label: 'Year',
                        value: question.yearId,
                        color: Colors.teal,
                      ),
                      const SizedBox(width: 8),
                      _InfoChip(
                        label: 'Subject',
                        value: question.subjectId,
                        color: Colors.orange,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getDifficultyColor(question.difficulty).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getDifficultyColor(question.difficulty).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _getDifficultyText(question.difficulty),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getDifficultyColor(question.difficulty),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: question.status == ContentStatus.published
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: question.status == ContentStatus.published
                          ? Colors.green.withValues(alpha: 0.3)
                          : Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    question.status == ContentStatus.published ? 'Published' : 'Draft',
                    style: TextStyle(
                      fontSize: 12,
                      color: question.status == ContentStatus.published 
                          ? Colors.green[700] 
                          : Colors.orange[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return Colors.green;
      case QuestionDifficulty.medium:
        return Colors.orange;
      case QuestionDifficulty.hard:
        return Colors.red;
    }
  }

  String _getDifficultyText(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }
}

class _InfoChip extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _InfoChip({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        value,
        style: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w600,
          color: color == Colors.indigo ? Colors.indigo[700] :
                 color == Colors.teal ? Colors.teal[700] :
                 Colors.orange[700],
        ),
      ),
    );
  }
}

class _QuestionContent extends StatelessWidget {
  final QuestionEntity question;

  const _QuestionContent({required this.question});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Question',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              question.question,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
            if (question.questionImage != null) ...[
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    question.questionImage!,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 100,
                        color: Colors.grey[100],
                        child: const Center(
                          child: Text('Image not available'),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _MCQOptions extends StatelessWidget {
  final QuestionEntity question;

  const _MCQOptions({required this.question});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Options',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            ...question.options.asMap().entries.map((entry) {
              final index = entry.key;
              final option = entry.value;
              final isCorrect = index == question.correctOptionIndex;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isCorrect 
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.05),
                  border: Border.all(
                    color: isCorrect 
                        ? Colors.green.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.2),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: isCorrect ? Colors.green : Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          String.fromCharCode(65 + index), // A, B, C, D
                          style: TextStyle(
                            color: isCorrect ? Colors.white : Colors.black54,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option,
                        style: TextStyle(
                          fontSize: 14,
                          color: isCorrect ? Colors.green[800] : Colors.black87,
                          fontWeight: isCorrect ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                    if (isCorrect)
                      Icon(
                        Icons.check_circle,
                        color: Colors.green[600],
                        size: 20,
                      ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

class _FlipCardAnswer extends StatelessWidget {
  final String answer;

  const _FlipCardAnswer({required this.answer});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Answer',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.05),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                answer,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _QuestionExplanation extends StatelessWidget {
  final String explanation;

  const _QuestionExplanation({required this.explanation});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Explanation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              explanation,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _QuestionReference extends StatelessWidget {
  final String reference;

  const _QuestionReference({required this.reference});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Reference',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              reference,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _QuestionKeywords extends StatelessWidget {
  final List<String> keywords;

  const _QuestionKeywords({required this.keywords});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Keywords',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: keywords.map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}

class _QuestionMetadata extends StatelessWidget {
  final QuestionEntity question;

  const _QuestionMetadata({required this.question});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Metadata',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            if (question.createdAt != null)
              _MetadataRow(
                label: 'Created',
                value: _formatDate(question.createdAt!),
              ),
            if (question.updatedAt != null)
              _MetadataRow(
                label: 'Updated',
                value: _formatDate(question.updatedAt!),
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}

class _MetadataRow extends StatelessWidget {
  final String label;
  final String value;

  const _MetadataRow({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
