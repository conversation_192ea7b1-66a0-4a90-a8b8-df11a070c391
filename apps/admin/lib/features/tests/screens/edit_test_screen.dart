import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';

import '../providers/tests_provider.dart';
import '../widgets/question_management_section.dart';

class EditTestScreen extends ConsumerStatefulWidget {
  final TestEntity test;

  const EditTestScreen({super.key, required this.test});

  @override
  ConsumerState<EditTestScreen> createState() => _EditTestScreenState();
}

class _EditTestScreenState extends ConsumerState<EditTestScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _durationController;
  late QuestionDifficulty _selectedDifficulty;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.test.name);
    _durationController = TextEditingController(text: widget.test.duration?.toString() ?? '');
    _selectedDifficulty = widget.test.difficulty;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  Future<void> _saveTest() async {
    if (!_formKey.currentState!.validate()) return;

    // Parse duration - can be null if empty
    int? duration;
    if (_durationController.text.trim().isNotEmpty) {
      duration = int.tryParse(_durationController.text.trim());
    }

    final updatedTest = widget.test.copyWith(
      name: _nameController.text.trim(),
      duration: duration,
      difficulty: _selectedDifficulty,
    );

    await ref.read(testUpdateStateProvider(widget.test.id!).notifier).updateTest(updatedTest);

    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final updateState = ref.watch(testUpdateStateProvider(widget.test.id ?? ''));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Test'),
        backgroundColor: const Color(0xFF5C6BC0),
        foregroundColor: Colors.white,
        actions: [
          TextButton.icon(
            onPressed: updateState.isLoading ? null : _saveTest,
            icon: updateState.isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  )
                : const Icon(Icons.save, color: Colors.white),
            label: Text(
              updateState.isLoading ? 'Saving...' : 'Save',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Test Information Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Test Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      // Test Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Test Name',
                          hintText: 'Enter test name',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a test name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Duration
                      TextFormField(
                        controller: _durationController,
                        decoration: const InputDecoration(
                          labelText: 'Duration (minutes) - Optional',
                          hintText: 'Enter duration in minutes (optional)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          // Only validate if a value is provided
                          if (value != null && value.trim().isNotEmpty) {
                            final duration = int.tryParse(value.trim());
                            if (duration == null || duration <= 0) {
                              return 'Please enter a valid duration';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Difficulty
                      DropdownButtonFormField<QuestionDifficulty>(
                        value: _selectedDifficulty,
                        decoration: const InputDecoration(
                          labelText: 'Difficulty',
                          border: OutlineInputBorder(),
                        ),
                        items: QuestionDifficulty.values.map((difficulty) {
                          return DropdownMenuItem(
                            value: difficulty,
                            child: Text(_getDifficultyDisplayName(difficulty)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedDifficulty = value;
                            });
                          }
                        },
                      ),
                      
                      // Read-only fields
                      const SizedBox(height: 20),
                      const Divider(),
                      const SizedBox(height: 16),
                      
                      _ReadOnlyField(label: 'Course', value: widget.test.courseId),
                      const SizedBox(height: 12),
                      _ReadOnlyField(label: 'Year', value: widget.test.yearId),
                      const SizedBox(height: 12),
                      _ReadOnlyField(label: 'Subject', value: widget.test.subjectId),
                      const SizedBox(height: 12),
                      _ReadOnlyField(label: 'Type', value: _getTypeDisplayName(widget.test.type)),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Question Management Section
              QuestionManagementSection(test: widget.test),
            ],
          ),
        ),
      ),
    );
  }

  String _getDifficultyDisplayName(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }

  String _getTypeDisplayName(ContentType type) {
    switch (type) {
      case ContentType.mcq:
        return 'MCQ';
      case ContentType.flipCard:
        return 'FlipCard';
    }
  }
}

class _ReadOnlyField extends StatelessWidget {
  final String label;
  final String value;

  const _ReadOnlyField({
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}
