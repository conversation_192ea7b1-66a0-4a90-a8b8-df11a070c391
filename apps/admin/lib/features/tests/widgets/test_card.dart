import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';

import '../providers/tests_provider.dart';
import '../screens/view_test_screen.dart';
import 'test_info_chips.dart';
import 'test_stats.dart';

class TestCard extends ConsumerWidget {
  final TestEntity test;

  const TestCard({super.key, required this.test});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Builder(
          builder: (context) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _TestCardHeader(test: test),
                const SizedBox(height: 16),
                TestStats(test: test),
                const SizedBox(height: 16),
                _TestCardActions(test: test),
              ],
            );
          },
        ),
      ),
    );
  }
}

/// Test card header with title and info chips
class _TestCardHeader extends StatelessWidget {
  final TestEntity test;

  const _TestCardHeader({required this.test});

  @override
  Widget build(BuildContext context) {
    final displayNames = <String, String>{
      'course': test.courseId,
      'year': test.yearId,
      'subject': test.subjectId,
    };
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                test.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              TestInfoChips(displayNames: displayNames),
            ],
          ),
        ),
        _TestCardStatusChip(test: test),
      ],
    );
  }
}

/// Test card status chip
class _TestCardStatusChip extends StatelessWidget {
  final TestEntity test;

  const _TestCardStatusChip({required this.test});

  @override
  Widget build(BuildContext context) {
    final isPublished = test.status == ContentStatus.published;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color:
            isPublished
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color:
              isPublished
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        isPublished ? 'Published' : 'Draft',
        style: TextStyle(
          fontSize: 12,
          color: isPublished ? Colors.green[700] : Colors.orange[700],
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Test card actions (edit, delete, publish buttons)
class _TestCardActions extends ConsumerWidget {
  final TestEntity test;

  const _TestCardActions({required this.test});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the test publishing state for this specific test
    final testState = ref.watch(testPublishingStateProvider(test.id ?? ''));

    // Show loading state if publishing is in progress
    if (testState.isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton.icon(
            onPressed: null,
            icon: const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            label: const Text('Publishing...'),
            style: TextButton.styleFrom(foregroundColor: Colors.grey),
          ),
        ],
      );
    }

    if (testState.hasError) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton.icon(
            onPressed: () => _publishTest(ref),
            icon: const Icon(Icons.error),
            label: const Text('Retry'),
            style: TextButton.styleFrom(foregroundColor: Colors.red[700]),
          ),
        ],
      );
    }

    final isDraft = test.status != ContentStatus.published;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // View button - always visible
        IconButton(
          onPressed: () => _viewTest(context),
          icon: const Icon(Icons.visibility_outlined),
          tooltip: 'View test',
          style: IconButton.styleFrom(
            foregroundColor: Colors.blue[700],
          ),
        ),
        const SizedBox(width: 8),

        // Publish button - only for draft tests
        if (isDraft)
          TextButton.icon(
            onPressed: () => _publishTest(ref),
            icon: const Icon(Icons.publish),
            label: const Text('Publish'),
            style: TextButton.styleFrom(foregroundColor: Colors.green[700]),
          ),
      ],
    );
  }

  void _viewTest(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ViewTestScreen(test: test),
      ),
    );
  }

  void _publishTest(WidgetRef ref) {
    print('📺 [UI] Publish button clicked for test: ${test.id}');
    print('📺 [UI] Test name: "${test.name}"');
    print('📺 [UI] Initiating publish flow...');
    ref.read(testPublishingStateProvider(test.id ?? '').notifier).publishTest(test);
  }
}
