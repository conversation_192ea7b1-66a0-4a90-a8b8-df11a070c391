import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/entities.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../providers/tests_provider.dart';
import '../screens/view_question_screen.dart';

class QuestionManagementSection extends ConsumerStatefulWidget {
  final TestEntity test;

  const QuestionManagementSection({super.key, required this.test});

  @override
  ConsumerState<QuestionManagementSection> createState() => _QuestionManagementSectionState();
}

class _QuestionManagementSectionState extends ConsumerState<QuestionManagementSection> {
  List<QuestionEntity> _questions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
  }

  Future<void> _loadQuestions() async {
    if (widget.test.questionIds.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      final firestore = FirebaseFirestore.instance;
      final questionDocs = await Future.wait(
        widget.test.questionIds.map((id) => firestore.collection('questions').doc(id).get()),
      );

      final questions = questionDocs
          .where((doc) => doc.exists)
          .map((doc) => QuestionEntity.fromJson({...doc.data()!, 'id': doc.id}))
          .toList();

      setState(() {
        _questions = questions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _removeQuestion(QuestionEntity question) async {
    // Remove from local list
    setState(() {
      _questions.removeWhere((q) => q.id == question.id);
    });

    // Update test with new question IDs
    final updatedQuestionIds = _questions.map((q) => q.id!).toList();
    final updatedTest = widget.test.copyWith(questionIds: updatedQuestionIds);

    await ref.read(testUpdateStateProvider(widget.test.id!).notifier).updateTest(updatedTest);
  }

  void _reorderQuestions(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final question = _questions.removeAt(oldIndex);
      _questions.insert(newIndex, question);
    });

    // Update test with new order
    final updatedQuestionIds = _questions.map((q) => q.id!).toList();
    final updatedTest = widget.test.copyWith(questionIds: updatedQuestionIds);

    ref.read(testUpdateStateProvider(widget.test.id!).notifier).updateTest(updatedTest);
  }

  void _viewQuestion(BuildContext context, QuestionEntity question) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ViewQuestionScreen(
          question: question,
          testId: widget.test.id,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Questions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  '${_questions.length} questions',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_questions.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(
                        Icons.quiz_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'No questions in this test',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ReorderableListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                onReorder: _reorderQuestions,
                itemCount: _questions.length,
                itemBuilder: (context, index) {
                  final question = _questions[index];
                  return _QuestionListItem(
                    key: ValueKey(question.id),
                    question: question,
                    index: index,
                    onTap: () => _viewQuestion(context, question),
                    onRemove: () => _removeQuestion(question),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}

class _QuestionListItem extends StatelessWidget {
  final QuestionEntity question;
  final int index;
  final VoidCallback onTap;
  final VoidCallback onRemove;

  const _QuestionListItem({
    super.key,
    required this.question,
    required this.index,
    required this.onTap,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      key: ValueKey(question.id),
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: ListTile(
        onTap: onTap,
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: const Color(0xFF5C6BC0),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.drag_handle,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: question.type == ContentType.mcq 
                    ? Colors.blue.withValues(alpha: 0.1)
                    : Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                question.type == ContentType.mcq ? 'MCQ' : 'FlipCard',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: question.type == ContentType.mcq 
                      ? Colors.blue[700]
                      : Colors.purple[700],
                ),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getDifficultyColor(question.difficulty).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _getDifficultyText(question.difficulty),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: _getDifficultyColor(question.difficulty),
                ),
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.visibility_outlined),
              onPressed: onTap,
              tooltip: 'View question',
              style: IconButton.styleFrom(
                foregroundColor: Colors.blue[700],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.remove_circle_outline, color: Colors.red),
              onPressed: onRemove,
              tooltip: 'Remove from test',
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return Colors.green;
      case QuestionDifficulty.medium:
        return Colors.orange;
      case QuestionDifficulty.hard:
        return Colors.red;
    }
  }

  String _getDifficultyText(QuestionDifficulty difficulty) {
    switch (difficulty) {
      case QuestionDifficulty.easy:
        return 'Easy';
      case QuestionDifficulty.medium:
        return 'Medium';
      case QuestionDifficulty.hard:
        return 'Hard';
    }
  }
}
