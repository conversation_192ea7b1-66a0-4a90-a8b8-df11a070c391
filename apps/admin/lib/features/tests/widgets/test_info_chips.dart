import 'package:flutter/material.dart';

/// Just display info chips
class TestInfoChips extends StatelessWidget {
  final Map<String, String> displayNames;

  const TestInfoChips({super.key, required this.displayNames});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _InfoChip(
          icon: Icons.school,
          label: displayNames['course']!,
          color: Colors.blue,
        ),
        const SizedBox(width: 8),
        _InfoChip(
          icon: Icons.calendar_today,
          label: displayNames['year']!,
          color: Colors.purple,
        ),
        const SizedBox(width: 8),
        _InfoChip(
          icon: Icons.book,
          label: displayNames['subject']!,
          color: Colors.green,
        ),
      ],
    );
  }
}

/// Individual info chip
class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _InfoChip({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
