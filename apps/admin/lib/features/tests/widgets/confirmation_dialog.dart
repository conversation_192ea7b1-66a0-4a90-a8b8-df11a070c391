import 'package:flutter/material.dart';
import 'package:entities/entities.dart';

/// Reusable confirmation dialog for destructive actions
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback onConfirm;
  final Color? confirmColor;
  final IconData? icon;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    this.confirmText = 'Confirm',
    this.cancelText = 'Cancel',
    this.confirmColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              color: confirmColor ?? Theme.of(context).colorScheme.error,
              size: 24,
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Text(
        message,
        style: const TextStyle(fontSize: 14),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            cancelText,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop();
            onConfirm();
          },
          style: FilledButton.styleFrom(
            backgroundColor: confirmColor ?? Theme.of(context).colorScheme.error,
          ),
          child: Text(confirmText),
        ),
      ],
    );
  }

  /// Show delete test confirmation dialog
  static Future<void> showDeleteTestDialog({
    required BuildContext context,
    required String testName,
    required VoidCallback onConfirm,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: 'Delete Test',
        message: 'Are you sure you want to delete "$testName"?\n\nThis will permanently delete the test and all its questions. This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        confirmColor: Colors.red[700],
        icon: Icons.delete_outline,
        onConfirm: onConfirm,
      ),
    );
  }

  /// Show delete question confirmation dialog
  static Future<void> showDeleteQuestionDialog({
    required BuildContext context,
    required QuestionEntity question,
    required VoidCallback onConfirm,
  }) {
    final questionPreview = question.question.length > 50
        ? '${question.question.substring(0, 50)}...'
        : question.question;
    
    return showDialog(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: 'Delete Question',
        message: 'Are you sure you want to delete this question?\n\n"$questionPreview"\n\nThis will permanently delete the question and remove it from all tests. This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        confirmColor: Colors.red[700],
        icon: Icons.help_outline,
        onConfirm: onConfirm,
      ),
    );
  }
}
