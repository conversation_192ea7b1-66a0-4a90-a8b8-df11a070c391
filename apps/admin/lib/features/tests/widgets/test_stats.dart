import 'package:flutter/material.dart';
import 'package:entities/entities.dart';

/// Just display test statistics
class TestStats extends StatelessWidget {
  final TestEntity test;

  const TestStats({super.key, required this.test});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _StatItem(
          icon: Icons.quiz,
          label: 'Questions',
          value: '${test.questionIds.length}',
        ),
        const SizedBox(width: 24),
        _StatItem(
          icon: Icons.timer,
          label: 'Duration',
          value: test.duration != null ? '${test.duration} min' : 'Not set',
        ),
        const SizedBox(width: 24),
        _StatItem(
          icon: Icons.category,
          label: 'Type',
          value: _getTypeDisplayName(test.type),
        ),
      ],
    );
  }

  String _getTypeDisplayName(ContentType? type) {
    if (type == null) return 'Unknown';

    switch (type) {
      case ContentType.mcq:
        return 'MCQ';
      case ContentType.flipCard:
        return 'FlipCard';
    }
  }
}

/// Individual stat item
class _StatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _StatItem({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            Text(
              label,
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ],
        ),
      ],
    );
  }
}
