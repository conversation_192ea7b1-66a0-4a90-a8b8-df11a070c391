import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/tests_provider.dart';
import '../../import/widgets/import_button.dart';

class TestsHeader extends ConsumerWidget {
  const TestsHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final testsStream = ref.watch(allTestsProvider);
    final testCount = testsStream.maybeWhen(
      data: (tests) => tests.length,
      orElse: () => 0,
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Tests ($testCount)',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const ImportButton(),
      ],
    );
  }
}
