import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../providers/firebase_user_provider.dart';

part 'admin_user_provider.g.dart';

@Riverpod(keepAlive: true)
Future<bool> isAdmin(Ref ref) async {
  final userAsync = ref.watch(firebaseUserProvider);
  final user = userAsync.valueOrNull;

  if (user == null) {
    return false;
  }

  try {
    final idTokenResult = await user.getIdTokenResult(true);
    final isAdmin = idTokenResult.claims?['isAdmin'] == true;
    return isAdmin;
  } catch (e) {
    return false;
  }
}
