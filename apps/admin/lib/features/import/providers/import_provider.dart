import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:entities/entities.dart';
import '../../tests/services/excel_parser.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/app_messages.dart';
import '../../../core/utils/error_messages.dart';

part 'import_provider.g.dart';

@riverpod
class Import extends _$Import {
  @override
  AsyncValue<void> build() => const AsyncValue.data(null);

  Future<void> importExcel(Uint8List bytes, String fileName) async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      final parsed = await ExcelParser.parseExcelFile(bytes, fileName);

      final totalWrites = parsed.questions.length + parsed.tests.length + 1;
      const safetyLimit = 450;
      if (totalWrites > safetyLimit) {
        throw Exception(
          'Import too large ($totalWrites writes). Please split the file.',
        );
      }

      final fs = FirebaseFirestore.instance;
      await fs.runTransaction((tx) async {
        final coursesRef = fs.collection('app_config').doc('courses');
        final snap = await tx.get(coursesRef);

        final mergedCourses = _mergeCoursesSafely(snap.data(), parsed.courses);

        final questionRefs = <DocumentReference>[];
        final questionData = <Map<String, dynamic>>[];
        for (final q in parsed.questions) {
          final ref = fs.collection('questions').doc();
          questionRefs.add(ref);
          questionData.add(q.copyWith(id: ref.id).toJson());
        }

        final testRefs = <DocumentReference>[];
        final testData = <Map<String, dynamic>>[];
        for (final t in parsed.tests) {
          final testQuestionIds = <String>[];

          for (int i = 0; i < parsed.questions.length; i++) {
            final q = parsed.questions[i];
            if (_questionBelongsToTest(q, t)) {
              testQuestionIds.add(questionRefs[i].id);
            }
          }

          final ref = fs.collection('tests').doc();
          testRefs.add(ref);
          testData.add(
            t.copyWith(id: ref.id, questionIds: testQuestionIds).toJson(),
          );
        }

        tx.set(coursesRef, mergedCourses.toJson());
        for (var i = 0; i < questionRefs.length; i++) {
          tx.set(questionRefs[i], questionData[i]);
        }
        for (var i = 0; i < testRefs.length; i++) {
          tx.set(testRefs[i], testData[i]);
        }
      });

      AppMessages.showSuccess('Import completed successfully!');
    });

    if (state.hasError) {
      AppLogger.error(
        'Import failed for "$fileName"',
        state.error,
        state.stackTrace,
      );
      AppMessages.showError(ErrorMessages.getImportErrorMessage(state.error!));
    }
  }

  CoursesEntity _mergeCoursesSafely(
    Map<String, dynamic>? existing,
    CoursesEntity incoming,
  ) {
    if (existing == null) return incoming;

    final existingCourses = CoursesEntity.fromJson(existing);
    final mergedCourses = <String, CourseEntity>{...existingCourses.courses};

    for (final entry in incoming.courses.entries) {
      final courseId = entry.key;
      final newCourse = entry.value;

      if (!mergedCourses.containsKey(courseId)) {
        mergedCourses[courseId] = newCourse;
      } else {
        mergedCourses[courseId] = _mergeYears(
          mergedCourses[courseId]!,
          newCourse,
        );
      }
    }

    return existingCourses.copyWith(courses: mergedCourses);
  }

  CourseEntity _mergeYears(CourseEntity existing, CourseEntity incoming) {
    final mergedYears = <String, YearEntity>{...existing.years};

    for (final entry in incoming.years.entries) {
      final yearId = entry.key;
      final newYear = entry.value;

      if (!mergedYears.containsKey(yearId)) {
        mergedYears[yearId] = newYear;
      } else {
        mergedYears[yearId] = _mergeSubjects(mergedYears[yearId]!, newYear);
      }
    }

    return existing.copyWith(years: mergedYears);
  }

  YearEntity _mergeSubjects(YearEntity existing, YearEntity incoming) {
    final mergedSubjects = <String, SubjectEntity>{...existing.subjects};

    for (final entry in incoming.subjects.entries) {
      final subjectId = entry.key;
      final newSubject = entry.value;

      if (!mergedSubjects.containsKey(subjectId)) {
        mergedSubjects[subjectId] = newSubject;
      }
    }

    return existing.copyWith(subjects: mergedSubjects);
  }

  bool _questionBelongsToTest(QuestionEntity question, TestEntity test) {
    if (question.courseId != test.courseId || question.yearId != test.yearId) {
      return false;
    }

    if (test.subjectId == 'Mock Tests') {
      return true;
    }

    return question.subjectId == test.subjectId;
  }
}
