import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../tests/providers/tests_provider.dart';
import '../providers/import_provider.dart';

class ImportButton extends ConsumerWidget {
  const ImportButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(importProvider);

    return OutlinedButton.icon(
      onPressed: state.isLoading ? null : () => _import(context, ref),
      icon:
          state.isLoading
              ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : const Icon(Icons.upload_file),
      label: Text(state.isLoading ? 'Importing...' : 'Import Excel'),
      style: OutlinedButton.styleFrom(
        foregroundColor: const Color(0xFF5C6BC0),
        side: const BorderSide(color: Color(0xFF5C6BC0)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  Future<void> _import(BuildContext context, WidgetRef ref) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xls'],
      allowMultiple: false,
    );

    if (result?.files.first.bytes == null) return;

    await ref
        .read(importProvider.notifier)
        .importExcel(result!.files.first.bytes!, result.files.first.name);

    ref.invalidate(allTestsProvider);
  }
}
