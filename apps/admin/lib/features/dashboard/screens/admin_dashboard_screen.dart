import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/login_provider.dart';
import 'package:providers/scaffold_messenger_key_provider.dart';

import '../../tests/tests_screen.dart';

class AdminDashboardScreen extends ConsumerWidget {
  const AdminDashboardScreen({super.key});

  Future<void> _handleSignOut(WidgetRef ref) async {
    try {
      await ref.read(loginProvider.notifier).signOut();
    } catch (e) {
      showSnackBarRef(ref, 'Sign out failed: ${e.toString()}', Colors.red);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loginState = ref.watch(loginProvider);
    final user = loginState.valueOrNull;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Row(
          children: [
            Icon(Icons.medical_services, color: Colors.red[400]),
            const SizedBox(width: 8),
            const Text(
              'Med',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            Text(
              'Pulse',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red[400],
              ),
            ),
          ],
        ),
        actions: [
          const Text(
            'Dev Admin',
            style: TextStyle(fontSize: 14, color: Colors.black87),
          ),
          const SizedBox(width: 8),
          PopupMenuButton<String>(
            icon: CircleAvatar(
              backgroundColor: Colors.blue[700],
              radius: 16,
              child: Text(
                user?.email?.substring(0, 1).toUpperCase() ?? 'D',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            onSelected: (value) {
              if (value == 'signout') {
                _handleSignOut(ref);
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'signout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('Sign Out'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // Tests Header
          Container(
            color: const Color(0xFF5C6BC0),
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.quiz, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text(
                  'Tests',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          // Tests Screen Content
          const Expanded(child: TestsScreen()),
        ],
      ),
    );
  }
}


