# Script to open multiple tabs in Windows Terminal and execute commands

# Get the current directory
$currentDir = (Get-Location).Path

# Define the commands for each tab
$tabCommands = @(
<#
    @{
        Name = "Emulator"
        SubDir = "."
        Command = "firebase emulators:start --import=./.emulator_data --export-on-exit ./.emulator_data"
    },
#>
    @{
        Name = "Functions"
        SubDir = "functions"
        Command = "npm run build:watch"
    },
    @{
        Name = "RiverPod"
        SubDir = "."
        Command = "./br.ps1"
    }
)

# Build the command string for Windows Terminal
$wtCommand = "wt"
foreach ($tab in $tabCommands) {
    # Combine current directory with subdirectory
    $startDir = Join-Path $currentDir $tab.SubDir

    # Add new tab with specified name and command
    $wtCommand += " ``; new-tab --startingDirectory `"$startDir`" --title `"$($tab.Name)`" powershell -NoExit -Command `"$($tab.Command)`""
}

# Remove the first semicolon and space (after "wt")
$wtCommand = $wtCommand -replace "wt ``; ", "wt "

# Execute the command
Invoke-Expression $wtCommand