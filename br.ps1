# Configuration - Add your build runner directories here
$buildRunnerDirs = @(
    "packages\entities",
    "packages\providers",
    "apps\medpulse",
    "apps\admin"
)

# Array to store job information
$script:buildJobs = @()
# Flag to indicate if we're shutting down
$script:isShuttingDown = $false

# Calculate maximum package name length for padding
$maxLength = ($buildRunnerDirs | ForEach-Object { $_.Length } | Measure-Object -Maximum).Maximum

# Function to format log output with colors
function Format-LogOutput {
    param([string]$directory, [string]$message)

    # Color the directory tag cyan with padding
    $dirTag = ("[$directory]".PadRight($maxLength+2)) | Write-Output

    # Skip empty lines
    if ([string]::IsNullOrWhiteSpace($message)) {
        Write-Host $dirTag -ForegroundColor Cyan
        return
    }

    # Parse log level if present
    $logPattern = '\[(INFO|FINE|SEVERE|WARNING|ERROR)\]'
    if ($message -match $logPattern) {
        $logLevel = $matches[1]
        $logMessage = $message -replace $logPattern, ''

        # Write directory tag
        Write-Host $dirTag -ForegroundColor Cyan -NoNewline
        Write-Host " [" -NoNewline

        # Color based on log level
        $levelColor = switch ($logLevel) {
            "INFO"    { "Green" }
            "FINE"    { "Gray" }
            "SEVERE"  { "Red" }
            "WARNING" { "Yellow" }
            "ERROR"   { "Red" }
            default   { "White" }
        }
        Write-Host $logLevel -ForegroundColor $levelColor -NoNewline
        Write-Host "]" -NoNewline
        Write-Host $logMessage
    }
    else {
        # No log level found, just print with directory tag
        Write-Host $dirTag -ForegroundColor Cyan -NoNewline
        Write-Host " $message"
    }
}

# Find dart executable
$dartPath = (Get-Command dart -ErrorAction SilentlyContinue).Path
if (-not $dartPath) {
    Write-Host "Error: Dart executable not found in PATH" -ForegroundColor Red
    exit 1
}

# Function to start a single build runner
function Start-SingleBuildRunner {
    param(
        [string]$directory,
        [string]$fullPath
    )

    Write-Host "Starting build runner in $directory" -ForegroundColor Green

    $job = Start-Job -Name "BuildRunner_$directory" -ScriptBlock {
        param($dartPath, $workDir)
        Set-Location $workDir
        & $dartPath run build_runner watch --delete-conflicting-outputs *>&1
    } -ArgumentList $dartPath, $fullPath

    $script:buildJobs += @{
        Job = $job
        Directory = $directory
        FullPath = $fullPath
    }

    Write-Host "Started build runner for $directory with Job ID: $($job.Id)" -ForegroundColor Green
}

# Function to start build runners
function Start-BuildRunners {
    $baseDir = Get-Location

    foreach ($dir in $buildRunnerDirs) {
        $fullPath = Join-Path $baseDir $dir
        if (Test-Path $fullPath) {
            Start-SingleBuildRunner -directory $dir -fullPath $fullPath
        } else {
            Write-Host "Warning: Directory $fullPath does not exist" -ForegroundColor Yellow
        }
    }
}

# Function to stop all build runners
function Stop-BuildRunners {
    $script:isShuttingDown = $true
    foreach ($jobInfo in $script:buildJobs) {
        Write-Host "Stopping build runner for $($jobInfo.Directory) (Job ID: $($jobInfo.Job.Id))" -ForegroundColor Yellow
        Stop-Job -Job $jobInfo.Job -ErrorAction SilentlyContinue
        Remove-Job -Job $jobInfo.Job -Force -ErrorAction SilentlyContinue
    }
    $script:buildJobs = @()
}

# Register cleanup handler for Ctrl+C
$cancelEventJob = Register-ObjectEvent -InputObject ([Console]) -EventName CancelKeyPress -Action {
    Write-Host "`nCtrl+C detected. Cleaning up..." -ForegroundColor Yellow
    Stop-BuildRunners
    exit
}

# Start the build runners
Start-BuildRunners

if ($script:buildJobs.Count -gt 0) {
    Write-Host "`nAll build runners started. Press Ctrl+C to stop all jobs and exit." -ForegroundColor Cyan
    Write-Host "Active build runner Job IDs: $($script:buildJobs.Job.Id -join ', ')" -ForegroundColor Cyan
} else {
    Write-Host "`nNo build runners were started successfully." -ForegroundColor Red
}

# Keep the script running and monitor jobs
try {
    while ($true) {
        Start-Sleep -Seconds 1

        # Check jobs and handle terminated ones
        $terminatedJobs = @()
        foreach ($jobInfo in $script:buildJobs) {
            $job = $jobInfo.Job

            # Get any new output
            $output = Receive-Job -Job $job
            if ($output) {
                foreach ($line in $output) {
                    Format-LogOutput -directory $jobInfo.Directory -message $line
                }
            }

            # Check if job has terminated
            if ($job.State -ne 'Running') {
                Write-Host "Build runner for $($jobInfo.Directory) (Job ID $($job.Id)) has terminated unexpectedly" -ForegroundColor Red
                Write-Host "State: $($job.State)" -ForegroundColor Red
                $terminatedJobs += $jobInfo
            }
        }

        # Handle terminated jobs
        foreach ($terminatedJob in $terminatedJobs) {
            # Remove the terminated job
            $script:buildJobs = $script:buildJobs | Where-Object { $_ -ne $terminatedJob }

            # If we're not shutting down, restart the build runner
            if (-not $script:isShuttingDown) {
                Write-Host "Restarting build runner for $($terminatedJob.Directory)..." -ForegroundColor Yellow
                Start-SingleBuildRunner -directory $terminatedJob.Directory -fullPath $terminatedJob.FullPath
            }
        }
    }
}
finally {
    # Cleanup on any other termination
    Stop-BuildRunners
    Unregister-Event -SourceIdentifier $cancelEventJob.Name
}