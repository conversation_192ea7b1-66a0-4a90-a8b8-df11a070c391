{"name": "functions", "scripts": {"lint": "eslint", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^13.0.2", "firebase-functions": "^6.3.0", "source-map": "^0.7.4"}, "devDependencies": {"@eslint/js": "^9.21.0", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "eslint": "^9.21.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.31.0", "firebase-functions-test": "^3.4.0", "typescript": "^5.7.3", "typescript-eslint": "^8.25.0"}, "private": true, "volta": {"node": "22.13.1"}, "type": "module"}