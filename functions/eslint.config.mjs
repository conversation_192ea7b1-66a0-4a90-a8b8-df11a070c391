import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    // Configuration without type checking for all files
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx', '**/*.mjs', '**/*.cjs'],
    ignores: ['**/lib/**'],  // Exclude the lib directory
    rules: {
      // Enforce consistent type imports
      '@typescript-eslint/consistent-type-imports': 'error',

      // Prevent unused variables and parameters
      '@typescript-eslint/no-unused-vars': ['error', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      }],

      '@typescript-eslint/naming-convention': 'off',

      // Enforce consistent quotes
      'quotes': ['error', 'single', {avoidEscape: true}],
      // Enforce consistent use of semicolons
      'semi': ['error', 'always'],
      // Enforce consistent comma usage
      'comma-dangle': ['error', 'always-multiline'],
      // Enforce consistent indentation
      'indent': ['error', 2],
      // Enforce maximum line length
      'max-len': ['error', {code: 120, ignoreUrls: true}],
      // Disallow the use of console
      'no-console': ['warn', {allow: ['warn', 'error', 'info']}],
    },
  },
  {
    // Configuration with type checking only for TypeScript files
    // excluding config files and test files
    files: ['**/*.ts', '**/*.tsx'],
    ignores: ['**/*.config.ts', '**/*.config.mts', '**/*.config.cts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: import.meta.dirname,
      },
    },
    rules: {
      // Type-checking rules
      ...tseslint.configs.recommendedTypeChecked.rules,

      // Enforce explicit return types on functions and class methods
      '@typescript-eslint/explicit-function-return-type': ['error', {
        allowExpressions: true,
        allowTypedFunctionExpressions: true,
      }],

      // Disallow unnecessary type assertions
      '@typescript-eslint/no-unnecessary-type-assertion': 'error',

      // Enforce strict null checks
      // '@typescript-eslint/strict-boolean-expressions': 'error',

      // Prefer interfaces over type aliases for object definitions
      '@typescript-eslint/consistent-type-definitions': ['error', 'interface'],

      // Enforce using the nullish coalescing operator
      // '@typescript-eslint/prefer-nullish-coalescing': 'error',

      // Enforce using optional chaining
      '@typescript-eslint/prefer-optional-chain': 'error',

      // Enforce consistent naming conventions
      '@typescript-eslint/naming-convention': [
        'off',
        {
          selector: 'default',
          format: ['camelCase'],
        },
        {
          selector: 'variable',
          format: ['camelCase', 'UPPER_CASE'],
        },
        {
          selector: 'parameter',
          format: ['camelCase'],
          leadingUnderscore: 'allow',
        },
        {
          selector: 'typeLike',
          format: ['PascalCase'],
        },
        {
          selector: 'interface',
          format: ['PascalCase'],
          prefix: ['I'],
        },
        {
          selector: 'enumMember',
          format: ['UPPER_CASE'],
        },
      ],
    },
  },
  {
    // Override rules for test files
    files: ['**/*.test.ts', '**/*.spec.ts', '**/__tests__/**/*.ts'],
    ignores: ['**/lib/**'],  // Exclude the lib directory
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
    },
  },
);
