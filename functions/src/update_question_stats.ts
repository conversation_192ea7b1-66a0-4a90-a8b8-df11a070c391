import {onDocumentWritten} from 'firebase-functions/v2/firestore';
import {logger} from 'firebase-functions';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';

interface TestResult {
  subscriptionId?: string;
  testId?: string;
  [key: string]: unknown;
}

interface TestAttempt {
  userAnswers?: Record<string, number>;
  incorrectAttempts?: Record<string, number[]>; // Frontend sends Set<int> but serializes to number[]
  answersCorrectness?: string[]; // Array of 'correct', 'incorrect', 'skipped' per question
  isCompleted?: boolean;
  result?: TestResult; // Contains subscriptionId and testId
  [key: string]: unknown;
}

export const updateQuestionStats = onDocumentWritten({
  document: 'users/{userId}/attempts/{attemptId}',
  region: 'asia-southeast1',
  memory: '256MiB',
  maxInstances: 10,
  timeoutSeconds: 60,
}, async (event) => {
  const uid = event.params.userId;
  const attemptId = event.params.attemptId;
  const change = event.data;

  if (!change) {
    logger.error(`No data available for attempt '${attemptId}' by user '${uid}'`, {uid, attemptId});
    return;
  }

  const afterExists = change.after?.exists;
  const afterData = change.after?.data();
  const beforeData = change.before?.data();

  // Handle deletion
  if (!afterExists) {
    logger.info('Attempt deleted, no action needed');
    return;
  }

  const attempt = afterData as TestAttempt;
  const previousAttempt = beforeData as TestAttempt | undefined;

  // Only process completed tests that weren't already completed
  if (!attempt?.isCompleted) {
    logger.info('Test not completed yet, skipping stats update');
    return;
  }

  // Skip if this was already processed (avoid duplicate processing)
  if (previousAttempt?.isCompleted) {
    logger.info('Test was already completed, skipping duplicate processing');
    return;
  }

  const { userAnswers, incorrectAttempts, answersCorrectness, result } = attempt;
  const subscriptionId = result?.subscriptionId;
  const testId = result?.testId;

  if (!subscriptionId || !testId || !userAnswers) {
    logger.error(
      `Missing required fields for attempt '${attemptId}' by user '${uid}'. ` +
      `subscriptionId: ${subscriptionId}, testId: ${testId}, userAnswers: ${userAnswers ? 'present' : 'missing'}`,
      {uid, attemptId},
    );
    return;
  }

  try {
    const db = getFirestore();
    const statsRef = db.doc(`stats/${subscriptionId}/tests/${testId}`);

    // Ensure the stats document exists
    await statsRef.set({}, { merge: true });

    // Prepare batch operations
    const batch = db.batch();

    // Process each answered question
    for (const [questionIndexStr, selectedOptionIndex] of Object.entries(userAnswers)) {
      const questionIndex = parseInt(questionIndexStr, 10);

      // Validate data
      if (isNaN(questionIndex) || selectedOptionIndex < 0) {
        logger.warn(`Invalid question data: index=${questionIndexStr}, option=${selectedOptionIndex}`);
        continue;
      }

      // Get incorrect attempts for this question in Feedback Mode
      const incorrectAttemptsForQuestion = incorrectAttempts?.[questionIndexStr] || [];
      const attemptPosition = incorrectAttemptsForQuestion.length + 1; // 1st, 2nd, or 3rd attempt

      // Update question stats
      const questionPath = `questions.${questionIndex}`;

      // Get first attempt choice
      let firstAttemptChoice: number;
      if (incorrectAttemptsForQuestion.length === 0) {
        // Got it right on first try - final answer IS the first attempt
        firstAttemptChoice = selectedOptionIndex;
      } else {
        // Got it wrong first - first attempt is the first incorrect choice
        firstAttemptChoice = incorrectAttemptsForQuestion[0];
      }

      // Simple increment operations
      const updates: Record<string, FieldValue> = {
        [`${questionPath}.totalAttempts`]: FieldValue.increment(1),
        [`${questionPath}.firstAttemptOptionCounts.${firstAttemptChoice}`]: FieldValue.increment(1),
      };

      batch.update(statsRef, updates);

      // Track success by attempt position
      const wasCorrect = answersCorrectness?.[questionIndex] === 'correct';
      if (wasCorrect) {
        // Increment the appropriate success counter based on attempt position
        const successField = attemptPosition === 1 ? 'correctOnFirstAttempt' :
          attemptPosition === 2 ? 'correctOnSecondAttempt' :
            attemptPosition === 3 ? 'correctOnThirdAttempt' : null;
        
        if (successField) {
          batch.update(statsRef, {
            [`${questionPath}.${successField}`]: FieldValue.increment(1),
          });
        }
      }
    }

    await batch.commit();
    logger.info(`✅ Updated stats for test ${testId} (${Object.keys(userAnswers).length} questions)`);

  } catch (error) {
    logger.error(
      `❌ Stats update failed for attempt '${attemptId}' by user '${uid}':`,
      error,
      {uid, attemptId},
    );
  }
});
