import {isDeepStrictEqual} from 'util';

import {onDocumentWritten} from 'firebase-functions/v2/firestore';
import {logger} from 'firebase-functions';
import {FieldPath, FieldValue} from 'firebase-admin/firestore';
import {getAuth} from 'firebase-admin/auth';

const CLAIMS_FIELD: string | null = null; // process.env.CLAIMS_FIELD ?? null;
const CLAIMS_COLLECTION = 'user_claims';

export const syncUserClaims = onDocumentWritten({
  document: CLAIMS_COLLECTION + '/{uid}',
  region: 'asia-southeast1',
  memory: '128MiB',
  maxInstances: 1,
  timeoutSeconds: 30,
}, async (event) => {
  const uid = event.params.uid;
  const change = event.data;
  const auth = getAuth();

  if (!change) {
    logger.error(`No data available for user '${uid}'`, {uid});
    return;
  }

  try {
    // make sure the user exists (can be fetched) before trying to set claims
    await auth.getUser(uid);
  } catch (e) {
    logger.error(
      `Unable to sync claims for user '${uid}', error:`,
      e,
      {uid},
    );
    return;
  }

  const afterExists = change.after?.exists;
  const afterData = change.after?.data();
  const beforeData = change.before?.data();

  if (
    !afterExists ||
    (CLAIMS_FIELD && !afterData?.[CLAIMS_FIELD])
  ) {
    logger.info(
      `Claims for user '${uid}' were deleted, removing from Auth.`,
      {uid},
    );
    return auth.setCustomUserClaims(uid, null);
  }

  const beforeClaims =
    (CLAIMS_FIELD && beforeData ? beforeData[CLAIMS_FIELD] : beforeData) ||
    {};
  const claims =
    (CLAIMS_FIELD && afterData ? afterData[CLAIMS_FIELD] : afterData) ||
    {};

  // don't write the _synced field to Auth
  const beforeClaimsToCompare = {...beforeClaims};
  const claimsToSet = {...claims};

  if (claimsToSet._synced) {
    delete claimsToSet._synced;
  }
  if (beforeClaimsToCompare._synced) {
    delete beforeClaimsToCompare._synced;
  }

  if (isDeepStrictEqual(beforeClaimsToCompare, claimsToSet)) {
    // don't persist identical claims
    return;
  }

  logger.info(
    `Updating claims for user '${uid}', setting keys ${Object.keys(claimsToSet).join(
      ', ',
    )}.`,
    {uid},
  );

  if (typeof claimsToSet !== 'object') {
    logger.error(
      `Invalid custom claims for user '${uid}'. Must be object, was ${JSON.stringify(
        claimsToSet,
      )}`,
      {uid},
    );
    return;
  }

  await auth.setCustomUserClaims(uid, claimsToSet);

  const fpath = ['_synced'];
  if (CLAIMS_FIELD) {
    fpath.unshift(CLAIMS_FIELD);
  }

  logger.info(
    `Claims set for user '${uid}', logging sync time to Firestore`,
    {uid},
  );

  return change.after.ref.update(
    new FieldPath(...fpath),
    FieldValue.serverTimestamp(),
  );
});
