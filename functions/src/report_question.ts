import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {logger} from 'firebase-functions';

/**
 * Interface for question issue report data
 */
interface IQuestionIssueReport {
  testId: string;
  questionIndex: number;
  issueType: string;
  comments?: string;
  timestamp?: string;
  version: number;
  courseId: string;
  yearId: string;
  questionData: Record<string, never>;
}

// Report a question issue
export const reportQuestionIssue = onCall<IQuestionIssueReport>({
  region: 'asia-southeast1',
  memory: '128MiB',
  maxInstances: 1,
  timeoutSeconds: 30,
  minInstances: 0,
  cors: true,
}, async (request) => {
  // Ensure the user is authenticated
  if (!request.auth) {
    throw new HttpsError(
      'unauthenticated',
      'You must be logged in to report question issues.',
    );
  }

  // Validate required data
  if (!request.data.testId ||
      request.data.questionIndex === undefined ||
      !request.data.issueType ||
      request.data.version === undefined ||
      !request.data.courseId ||
      !request.data.yearId ||
      !request.data.questionData) {
    throw new HttpsError(
      'invalid-argument',
      'Missing required fields for question issue report.',
    );
  }

  logger.log('Processing question issue report', request.data);

  try {
    // Store the report in Firestore
    const db = getFirestore();
    await db.collection('question_reports').add({
      testId: request.data.testId,
      questionIndex: request.data.questionIndex,
      issueType: request.data.issueType,
      comments: request.data.comments || '',
      userId: request.auth.uid,
      timestamp: FieldValue.serverTimestamp(),
      status: 'pending', // pending, reviewed, resolved
      version: request.data.version,
      courseId: request.data.courseId,
      yearId: request.data.yearId,
      questionData: request.data.questionData,
    });

    return { success: true, message: 'Question issue reported successfully.' };
  } catch (error) {
    logger.error('Error reporting question issue:', error);
    throw new HttpsError(
      'internal',
      'Failed to report question issue.',
      error instanceof Error ? error.message : 'Unknown error',
    );
  }
});
