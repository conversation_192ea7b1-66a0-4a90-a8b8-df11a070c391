import {SourceMapConsumer} from 'source-map';
import {logger} from 'firebase-functions';
import {getFirestore} from 'firebase-admin/firestore';

interface ISourceMapData {
  sourceMap: string;
}

class SourceMapManager {
  private static instance: SourceMapManager;
  private consumers: Map<string, SourceMapConsumer> = new Map();

  private constructor() {
  }

  static getInstance(): SourceMapManager {
    if (!SourceMapManager.instance) {
      SourceMapManager.instance = new SourceMapManager();
    }
    return SourceMapManager.instance;
  }

  async translateStackTrace(stackTrace: string, sourceMapId: string): Promise<string> {
    // Skip translation for debug builds
    if (sourceMapId === 'debug') {
      return stackTrace;
    }

    let consumer = this.consumers.get(sourceMapId);

    if (!consumer) {
      await this.loadSourceMap(sourceMapId);
      consumer = this.consumers.get(sourceMapId);

      if (!consumer) {
        return stackTrace; // Return original if source map couldn't be loaded
      }
    }

    const lines = stackTrace.split('\n');
    const translatedLines = await Promise.all(lines.map(async (line) => {
      const match = line.match(/at (.*) \((.*):(\d+):(\d+)\)/);
      if (!match) return line;

      const [, fnName, file, lineNo, colNo] = match;
      if (!file.includes('main.dart.js')) return line;

      try {
        const pos = consumer.originalPositionFor({
          line: parseInt(lineNo),
          column: parseInt(colNo),
        });

        if (pos.source && pos.line) {
          return `    at ${pos.name || fnName} (${pos.source}:${pos.line}:${pos.column || 0})`;
        }
      } catch (error) {
        logger.warn(`Failed to translate line: ${line}`, error);
      }
      return line;
    }));

    return translatedLines.join('\n');
  }

  private async loadSourceMap(sourceMapId: string): Promise<void> {
    try {
      const sourceMapDoc = await getFirestore().collection('sourceMap').doc(sourceMapId).get();
      const data = sourceMapDoc.data() as ISourceMapData | undefined;

      if (!data) {
        logger.warn(`No source map found in database for ID: ${sourceMapId}`);
        return;
      }

      await SourceMapConsumer.with(JSON.parse(data.sourceMap), null, newConsumer => {
        this.consumers.set(sourceMapId, newConsumer);
      });

      logger.info(`Source map loaded for ID ${sourceMapId}`);
    } catch (error) {
      logger.error(`Error loading source map for ID ${sourceMapId}:`, error);
      throw error;
    }
  }
}

// Export for use in other files
export const sourceMapManager = SourceMapManager.getInstance();
