import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {FieldValue, getFirestore, Timestamp} from 'firebase-admin/firestore';
import {defineInt} from 'firebase-functions/params';
import {logger} from 'firebase-functions';
import {sourceMapManager} from './source_map_manager.js';

// Configuration parameters with defaults
const maxOccurrences = defineInt('MAX_OCCURRENCES', {default: 100});

// Browser information interface matching <PERSON>lut<PERSON>'s structure
interface IBrowserInfo {
  userAgent?: string;
  platform?: string;
  language?: string;
  vendor?: string;
  cookieEnabled?: boolean;
  onLine?: boolean;
  hardwareConcurrency?: number;
  maxTouchPoints?: number;
  deviceMemory?: number;
  windowWidth?: number;
  windowHeight?: number;
  screenWidth?: number;
  screenHeight?: number;
  pixelRatio?: number;
}

interface IErrorReport {
  errorHash: string;
  error: string;
  stackTrace: string;
  errorType: string;
  timestamp: string;
  appVersion: string;
  buildNumber: string;
  packageName: string;
  userId: string;
  userDisplayName: string;
  userEmail: string;
  sourceMapId: string;
  platform: {
    isWeb: boolean;
    browserInfo: IBrowserInfo;
  };
}

export const reportError = onCall<IErrorReport>({
  region: 'asia-southeast1',
  memory: '128MiB',
  maxInstances: 1,
  timeoutSeconds: 30,
  minInstances: 0,
  cors: true,
}, async (request) => {

  // Check if the user is authenticated
  if (!request.auth) {
    throw new HttpsError(
      'unauthenticated',
      'Only authenticated users can report errors',
    );
  }

  logger.log('Processing error report');

  if (!request.data.errorHash || !request.data.error || !request.data.stackTrace || !request.data.sourceMapId) {
    throw new HttpsError(
      'invalid-argument',
      'Error report must include errorHash, error message, stack trace, and sourceMapId',
    );
  }

  try {
    const db = getFirestore();
    // Translate stack trace before processing using the sourceMapId
    const translatedStackTrace = await sourceMapManager.translateStackTrace(
      request.data.stackTrace,
      request.data.sourceMapId,
    );
    logger.log('Stack trace translated');

    const errorDocRef = db.collection('bugs').doc(request.data.errorHash);

    const occurrence = {
      timestamp: Timestamp.now(),
      userId: request.data.userId || 'signed out',
      userDisplayName: request.data.userDisplayName || 'Unknown',
      userEmail: request.data.userEmail || 'Unknown',
      browserInfo: request.data.platform.browserInfo,
      sourceMapId: request.data.sourceMapId,
    };

    await db.runTransaction(async (transaction) => {
      const errorDoc = await transaction.get(errorDocRef);

      if (errorDoc.exists) {
        const currentData = errorDoc.data();
        const occurrences = currentData?.occurrences ?? [];
        occurrences.unshift(occurrence);

        if (occurrences.length > maxOccurrences.value()) {
          occurrences.length = maxOccurrences.value();
        }

        transaction.set(errorDocRef, {
          occurrences,
          totalCount: FieldValue.increment(1),
          lastOccurrence: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        }, { merge: true });
      } else {
        transaction.set(errorDocRef, {
          error: request.data.error,
          stackTrace: translatedStackTrace,
          errorType: request.data.errorType,
          appVersion: request.data.appVersion,
          buildNumber: request.data.buildNumber,
          packageName: request.data.packageName,
          sourceMapId: request.data.sourceMapId,
          firstOccurrence: FieldValue.serverTimestamp(),
          lastOccurrence: FieldValue.serverTimestamp(),
          occurrences: [occurrence],
          totalCount: 1,
          createdAt: FieldValue.serverTimestamp(),
        });
      }
    });

    return {
      success: true,
      message: 'Error reported successfully',
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error('Error in reportError function:', error);
    throw new HttpsError(
      'internal',
      'Failed to report error',
      error instanceof Error ? error.message : 'Unknown error',
    );
  }
});
