import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {logger} from 'firebase-functions';

/**
 * Firebase function to accept legal terms and privacy policy.
 * This function can only be called by authenticated users and will update
 * their user document with acceptance information.
 */
export const acceptTerms = onCall({
  region: 'asia-southeast1',
  memory: '128MiB',
  maxInstances: 1,
  timeoutSeconds: 30,
  minInstances: 0,
  cors: true,
}, async (request) => {
  // Check if the user is authenticated
  if (!request.auth) {
    throw new HttpsError(
      'unauthenticated',
      'Only authenticated users can accept terms',
    );
  }

  const userId = request.auth.uid;
  logger.log(`Processing terms acceptance for user: ${userId}`);

  try {
    const db = getFirestore();
    const userDocRef = db.collection('users').doc(userId);

    // Update the user document with acceptance information
    await userDocRef.set({
      acceptedTerms: true,
      acceptedPrivacyPolicy: true,
      acceptedTermsAt: FieldValue.serverTimestamp(),
      acceptedPrivacyPolicyAt: FieldValue.serverTimestamp(),
    }, {merge: true});

    // Get the updated user document to return to the client
    const updatedUserDoc = await userDocRef.get();

    if (!updatedUserDoc.exists) {
      throw new HttpsError(
        'not-found',
        'User document not found after update',
      );
    }

    return {
      success: true,
      message: 'Terms accepted successfully',
      user: updatedUserDoc.data(),
    };
  } catch (error) {
    logger.error('Error accepting terms:', error);
    throw new HttpsError(
      'internal',
      'Failed to accept terms',
      error instanceof Error ? error.message : 'Unknown error',
    );
  }
});
