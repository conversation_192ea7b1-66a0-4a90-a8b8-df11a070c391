/*
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';

admin.initializeApp();

// Paymob webhook verification
const PAYMOB_HMAC_KEY = process.env.PAYMOB_HMAC_KEY || 'YOUR_PAYMOB_HMAC_KEY';

interface IPaymobCallbackData {
  obj: {
    order: {
      id: string;
      // eslint-disable-next-line @typescript-eslint/naming-convention
      amount_cents: number;
      currency: string;
      merchant_order_id: string;
    };
    success: boolean;
    is_voided: boolean;
    is_refunded: boolean;
    is_3d_secure: boolean;
    integration_id: string;
    profile_id: string;
    has_parent_transaction: boolean;
    parent_transaction: null;
    owner: number;
    pending: boolean;
    amount_cents: number;
    source_data: {
      pan: string;
      type: string;
      sub_type: string;
    };
    created_at: string;
    transaction_processed_callback_responses: null;
    currency: string;
    terminal_id: string;
    merchant_commission: number;
    merchant_staff_tag: null;
    metadata: {
      subscription_id: string;
      subscription_type: string;
      transaction_id: string;
    };
  };
}
*/

/*
function verifyPaymobWebhook(data: any, hmac: string): boolean {
  // Sort the data object by keys
  const sortedData = Object.keys(data)
    .sort()
    .reduce((acc: any, key: string) => {
      acc[key] = data[key];
      return acc;
    }, {});

  // Convert the sorted data to a string
  const dataString = JSON.stringify(sortedData);

  // Create HMAC-SHA512 hash
  const hmacHash = crypto
    .createHmac('sha512', PAYMOB_HMAC_KEY)
    .update(dataString)
    .digest('hex');

  // Compare the calculated hash with the provided hash
  return hmacHash === hmac;
}
*/

/*
export const handlePaymobCallback = functions.https.onRequest(async (req, res) => {
  try {
    // Verify the webhook signature
    const hmac = req.headers['x-paymob-signature'] as string;
    if (!hmac) {
      console.error('No HMAC signature provided');
      res.status(401).send('Unauthorized');
      return;
    }

    if (!verifyPaymobWebhook(req.body, hmac)) {
      console.error('Invalid HMAC signature');
      res.status(401).send('Unauthorized');
      return;
    }

    const data = req.body as IPaymobCallbackData;
    const transactionId = data.obj.metadata.transaction_id;

    const { subscription_id } = data.obj.metadata;
    const [courseId, yearId] = subscription_id.split(':');
    const planType = data.obj.order.merchant_order_id.split('_')[0];
    const months = getSubscriptionMonths(planType);
    const amount = data.obj.amount_cents / 100; // Convert to PKR

    // Create transaction details
    const transactionDetails = {
      id: transactionId,
      userId: data.obj.owner.toString(),
      courseId,
      yearId,
      planType,
      months,
      amount,
      success: data.obj.success,
      isVoided: data.obj.is_voided,
      isRefunded: data.obj.is_refunded,
      status: data.obj.success ? 'success' : 'failed',
      message: data.obj.success ? 'Payment successful' : 'Payment failed',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    // Save transaction in transactions collection
    await admin.firestore().collection('transactions').doc(transactionId).set(transactionDetails);

    // Handle voided transactions
    if (data.obj.is_voided) {
      await admin.firestore().collection('transactions').doc(transactionId).update({
        status: 'voided',
        message: 'Transaction was voided',
      });
      res.status(200).send('Transaction voided');
      return;
    }

    // Handle refunded transactions
    if (data.obj.is_refunded) {
      const userId = data.obj.owner.toString();
      const userRef = admin.firestore().collection('users').doc(userId);

      await admin.firestore().runTransaction(async (transaction) => {
        const userDoc = await transaction.get(userRef);
        if (!userDoc.exists) {
          throw new Error('User not found');
        }

        const userData = userDoc.data()!;
        const subscriptions = userData.subscriptions || {};

        // Remove the subscription
        delete subscriptions[subscription_id];

        transaction.update(userRef, {
          subscriptions,
        });
      });

      // Update transaction status
      await admin.firestore().collection('transactions').doc(transactionId).update({
        status: 'refunded',
        message: 'Transaction was refunded and subscription revoked',
      });

      res.status(200).send('Transaction refunded and subscription revoked');
      return;
    }

    // Verify the payment was successful
    if (!data.obj.success) {
      await admin.firestore().collection('transactions').doc(transactionId).update({
        status: 'error',
        message: 'Payment was not successful',
      });
      res.status(400).send('Payment was not successful');
      return;
    }

    // Calculate subscription end date based on plan
    const subscriptionEndDate = new Date();
    subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + months);

    // Update user's subscription in Firestore
    const userId = data.obj.owner.toString();
    const userRef = admin.firestore().collection('users').doc(userId);

    await admin.firestore().runTransaction(async (transaction) => {
      const userDoc = await transaction.get(userRef);
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const userData = userDoc.data()!;
      const subscriptions = userData.subscriptions || {};

      // Update the subscription for this course and year
      subscriptions[subscription_id] = subscriptionEndDate.toISOString();

      transaction.update(userRef, {
        subscriptions,
      });
    });

    // Update transaction status
    await admin.firestore().collection('transactions').doc(transactionId).update({
      status: 'success',
      message: 'Payment successful',
    });

    res.status(200).send('Subscription updated successfully');
  } catch (error) {
    console.error('Error processing payment callback:', error);
    res.status(500).send('Error processing payment callback');
  }
});
*/

/*
function getSubscriptionMonths(planType: string): number {
  switch (planType) {
  case '3_months':
    return 3;
  case '6_months':
    return 6;
  case '12_months':
    return 12;
  case 'lifetime':
    return 1200; // 100 years
  default:
    throw new Error('Invalid subscription plan type');
  }
}
*/
