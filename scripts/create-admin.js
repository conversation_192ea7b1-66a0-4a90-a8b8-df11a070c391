const admin = require('firebase-admin');

// Usage: node create-admin.js <env> <name> <email> <password>
// Example: node create-admin.js dev "Admin User" <EMAIL> password123

function showUsage() {
  console.log('Usage: node create-admin.js <env> <name> <email> <password>');
  console.log('  env: dev or prod');
  console.log('  name: Admin display name');
  console.log('  email: Admin email address');
  console.log('  password: Admin password (min 6 chars)');
}

// Validation functions
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidPassword(password) {
  return password && password.length >= 6;
}

function isValidName(name) {
  return name && name.trim().length > 0;
}

async function createAdmin() {
  try {
    // Parse command line arguments (modern 2025 approach)
    const args = process.argv.slice(2);
    
    if (args.length !== 4) {
      showUsage();
      process.exit(1);
    }
    
    const [environment, displayName, email, password] = args;
    
    // Validate inputs
    if (!['dev', 'prod'].includes(environment)) {
      console.error('Environment must be "dev" or "prod"');
      process.exit(1);
    }
    if (!isValidName(displayName)) {
      console.error('Display name cannot be empty');
      process.exit(1);
    }
    if (!isValidEmail(email)) {
      console.error('Invalid email format');
      process.exit(1);
    }
    if (!isValidPassword(password)) {
      console.error('Password must be at least 6 characters');
      process.exit(1);
    }

    // Load service account based on environment
    let serviceAccount;
    try {
      serviceAccount = environment === 'prod' 
        ? require('../service-account-prod.json')
        : require('../service-account-dev.json');
    } catch (error) {
      console.error(`Service account file not found: service-account-${environment}.json`);
      process.exit(1);
    }

    // Initialize Firebase Admin
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: serviceAccount.project_id
    });

    console.log(`Creating admin user in ${environment} environment...`);
    
    // Create user with email and password
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
      emailVerified: true,
      displayName: displayName,
      disabled: false,
    });
    
    console.log(`User created: ${userRecord.uid}`);
    
    // Write admin claims to Firestore
    await admin.firestore().collection('user_claims').doc(userRecord.uid).set({
      isAdmin: true
    });
    
    console.log('Admin user created successfully!');
    console.log(`UID: ${userRecord.uid}`);
    console.log(`Email: ${email}`);
    console.log(`Name: ${displayName}`);
    
  } catch (error) {
    if (error.code === 'auth/email-already-exists') {
      console.error('User with email already exists');
    } else if (error.code === 'auth/invalid-email') {
      console.error('Invalid email address');
    } else if (error.code === 'auth/weak-password') {
      console.error('Password is too weak');
    } else {
      console.error('Error creating admin:', error.message);
    }
    process.exit(1);
  }
  
  process.exit(0);
}

// Run the script
createAdmin();
