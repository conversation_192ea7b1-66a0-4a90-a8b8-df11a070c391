# Creates a local backup of the MedPulse Firestore database for use with Firebase Emulator

# Constants
$PROJECT_ID = "medpulse-prod"
$BACKUP_PATH = ".emulator_data"
$BUCKET_NAME = "firestore-backup"
$TIMESTAMP = Get-Date -Format "yyyyMMdd-HHmmss"
$GCS_PATH = "gs://$BUCKET_NAME/temp-backup-$TIMESTAMP"

# Function to check if gcloud is installed
function Test-GCloudInstalled {
    try {
        $null = Get-Command gcloud -ErrorAction Stop
        return $true
    }
    catch {
        Write-Error "Google Cloud SDK (gcloud) is not installed or not in PATH"
        return $false
    }
}

# Function to check if user is authenticated with gcloud
function Test-GCloudAuth {
    $auth = gcloud auth list --filter=status:ACTIVE --format="value(account)"
    return ![string]::IsNullOrEmpty($auth)
}

# Function to handle command execution and errors
function Invoke-GCloudCommand {
    param(
        [string]$Message,
        [scriptblock]$Command
    )
    Write-Host $Message
    & $Command
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Command failed with exit code: $LASTEXITCODE"
        exit 1
    }
}

# Main script
try {
    # Check if gcloud is installed
    if (-not (Test-GCloudInstalled)) {
        exit 1
    }

    # Create backup directory if it doesn't exist
    if (-not (Test-Path -Path $BACKUP_PATH)) {
        New-Item -ItemType Directory -Path $BACKUP_PATH -Force | Out-Null
        Write-Host "Created backup directory: $BACKUP_PATH"
    }

    # Check authentication
    if (-not (Test-GCloudAuth)) {
        Write-Host "Please authenticate with gcloud..."
        Invoke-GCloudCommand "Authenticating..." { gcloud auth login }
    }

    # Set the current project
    Invoke-GCloudCommand "Setting current project to: $PROJECT_ID" {
        gcloud config set project $PROJECT_ID
    }

    # Check if bucket exists
    Write-Host "Checking backup bucket..."
    $bucketExists = gcloud storage buckets list --filter="name=$BUCKET_NAME" --format="get(name)"

    # Create bucket if it doesn't exist
    if (-not $bucketExists) {
        Invoke-GCloudCommand "Creating backup bucket: $BUCKET_NAME" {
            gcloud storage buckets create gs://$BUCKET_NAME --project=$PROJECT_ID
        }
    }

    # Execute the export to GCS
    Invoke-GCloudCommand "Starting Firestore export to GCS..." {
        gcloud firestore export $GCS_PATH
    }

    # Download the export locally
    Invoke-GCloudCommand "Downloading export to local directory..." {
        gcloud storage cp -r $GCS_PATH/* $BACKUP_PATH
    }

    # Clean up GCS
    Invoke-GCloudCommand "Cleaning up GCS temporary files..." {
        gcloud storage rm -r $GCS_PATH
    }

    Write-Host "Export completed successfully!"
    Write-Host "Backup location: $BACKUP_PATH"

} catch {
    Write-Error "An unexpected error occurred: $_"
    exit 1
}