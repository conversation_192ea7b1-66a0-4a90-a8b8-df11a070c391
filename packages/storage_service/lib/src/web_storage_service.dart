import 'dart:async';
import 'dart:typed_data';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:idb_shim/idb_browser.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:http/http.dart' as http;

import 'storage_service.dart';
import 'web_secure_storage.dart';

class WebStorageService implements StorageService {
  static const String _dbName = 'storage_cache';
  static const int _dbVersion = 2;
  static const String _filesStore = 'files';
  static const String _contentTypeStore = 'content_types';
  static const String _metadataStore = 'metadata';
  static const String _secureStore = 'secure_items';

  // Optional crypto parameters for advanced encryption
  static const Map<String, String>? _cryptoOptions = null;

  late Database _db;
  late WebSecureStorage _secureStorage;
  bool _initialized = false;

  WebStorageService();

  Future<void> _ensureInitialized() async {
    if (_initialized) return;

    final idbFactory = getIdbFactory();
    if (idbFactory == null) {
      throw StateError('IndexedDB is not supported in this environment');
    }

    _db = await idbFactory.open(_dbName, version: _dbVersion, onUpgradeNeeded: (VersionChangeEvent event) {
      Database db = event.database;
      // Store for cached files (as Uint8List)
      if (!db.objectStoreNames.contains(_filesStore)) {
        db.createObjectStore(_filesStore);
      }

      // Store for content types
      if (!db.objectStoreNames.contains(_contentTypeStore)) {
        db.createObjectStore(_contentTypeStore);
      }

      // Store for cache metadata
      if (!db.objectStoreNames.contains(_metadataStore)) {
        db.createObjectStore(_metadataStore);
      }

      // Add secure items store - no keyPath specified, we'll use explicit keys
      if (db.objectStoreNames.contains(_secureStore)) {
        // Delete old schema if it exists
        db.deleteObjectStore(_secureStore);
      }
      db.createObjectStore(_secureStore);
    });

    // Initialize secure storage with the same database
    _secureStorage = WebSecureStorage(_db);
    _initialized = true;
  }

  @override
  Future<Uint8List> getCachedFile(String storagePath) async {
    await _ensureInitialized();

    final txn = _db.transaction(_filesStore, idbModeReadOnly);
    final store = txn.objectStore(_filesStore);
    final cacheData = await store.getObject(storagePath);
    await txn.completed;

    if (cacheData != null) {
      if (cacheData is Uint8List) {
        return cacheData;
      }
      throw StateError('Invalid data format in cache for $storagePath');
    }

    // If not in cache, download and cache the file
    return await _download(storagePath);
  }

  @override
  Future<String?> getFileContentType(String storagePath) async {
    await _ensureInitialized();

    final txn = _db.transaction(_contentTypeStore, idbModeReadOnly);
    final store = txn.objectStore(_contentTypeStore);

    try {
      final contentType = await store.getObject(storagePath);
      return contentType?.toString();
    } finally {
      await txn.completed;
    }
  }

  @override
  Future<void> clearCache() async {
    await _ensureInitialized();

    final txn = _db.transaction([_filesStore, _contentTypeStore], idbModeReadWrite);
    final fileStore = txn.objectStore(_filesStore);
    final contentTypeStore = txn.objectStore(_contentTypeStore);

    await fileStore.clear();
    await contentTypeStore.clear();
    await txn.completed;

    final metaTxn = _db.transaction(_metadataStore, idbModeReadWrite);
    final metaStore = metaTxn.objectStore(_metadataStore);
    await metaStore.clear();
    await metaTxn.completed;

    // Clear secure preferences as well
    await _secureStorage.deleteAll(
      prefix: _getUserPrefix(),
      cryptoOptions: _cryptoOptions,
    );
  }

  @override
  Future<bool> isFileCached(String storagePath) async {
    await _ensureInitialized();

    final txn = _db.transaction(_filesStore, 'readonly');
    final store = txn.objectStore(_filesStore);

    try {
      final data = await store.getObject(storagePath);
      return data != null;
    } finally {
      await txn.completed;
    }
  }

  @override
  Future<int> getCacheSize() async {
    await _ensureInitialized();

    final txn = _db.transaction(_metadataStore, idbModeReadOnly);
    final store = txn.objectStore(_metadataStore);

    try {
      final size = await store.getObject('totalSize');
      return (size as int?) ?? 0;
    } finally {
      await txn.completed;
    }
  }

  @override
  Future<void> setMaxCacheSize(int maxSize) async {
    await _ensureInitialized();

    final txn = _db.transaction(_metadataStore, idbModeReadWrite);
    final store = txn.objectStore(_metadataStore);

    try {
      await store.put(maxSize, 'maxSize');
      await _enforceCacheSize(maxSize);
    } finally {
      await txn.completed;
    }
  }

  @override
  Future<String?> getPref(String key, [String? prefix]) async {
    await _ensureInitialized();

    try {
      // Use secure storage with user-specific prefix
      final userPrefix = prefix ?? _getUserPrefix();
      return await _secureStorage.read(
        key: key,
        prefix: userPrefix,
        cryptoOptions: _cryptoOptions,
      );
    } catch (e) {
      // Handle errors and return null if preference can't be read
      print('Error reading secure preference: $e');
      return null;
    }
  }

  @override
  Future<void> setPref(String key, String? value, [String? prefix]) async {
    await _ensureInitialized();

    final userPrefix = prefix ?? _getUserPrefix();

    if (value == null) {
      // Delete the preference if value is null
      await _secureStorage.delete(
        key: key,
        prefix: userPrefix,
        cryptoOptions: _cryptoOptions,
      );
    } else {
      // Write the preference with secure storage
      await _secureStorage.write(
        key: key,
        value: value,
        prefix: userPrefix,
        cryptoOptions: _cryptoOptions,
      );
    }
  }

  // Helper method to create user-specific prefix
  String _getUserPrefix() {
    return FirebaseAuth.instance.currentUser?.uid ?? 'none';
  }

  // Helper method to check if content type is valid binary or image type
  bool _isValidBinaryType(String contentType) {
    final lowerContentType = contentType.toLowerCase();
    return lowerContentType.startsWith('application/octet-stream') ||
        lowerContentType.startsWith('application/binary') ||
        lowerContentType.startsWith('image/') ||
        lowerContentType.startsWith('audio/') ||
        lowerContentType.startsWith('video/') ||
        lowerContentType.startsWith('application/pdf') ||
        lowerContentType.startsWith('application/zip') ||
        lowerContentType.startsWith('application/x-zip') ||
        lowerContentType.startsWith('application/x-compressed') ||
        lowerContentType.startsWith('multipart/byteranges');
  }

  // Clean up resources
  Future<void> dispose() async {
    if (_initialized) {
      _db.close();
      _initialized = false;
    }
  }

  Future<Uint8List> _download(String storagePath) async {
    try {
      // Check if the path is a direct URL or a Firebase Storage path
      if (storagePath.startsWith('https://')) {
        return await _downloadFromUrl(storagePath);
      }

      // Handle as Firebase Storage path
      final storageRef = FirebaseStorage.instance.ref().child(storagePath);

      // Get metadata to determine content type
      final metadata = await storageRef.getMetadata();
      final contentType = metadata.contentType ?? 'application/octet-stream';

      // Validate content type is binary or image
      if (!_isValidBinaryType(contentType)) {
        throw StateError('Invalid content type: $contentType. Only binary or image types are supported.');
      }

      // Download file as bytes
      final Uint8List? data = await storageRef.getData();
      if (data == null) {
        throw StateError('Failed to download file from Firebase Storage');
      }

      // Cache the downloaded file
      await _cacheFile(storagePath, data, contentType);

      return data;
    } catch (e) {
      throw StateError('Error downloading from Firebase: $e');
    }
  }

  Future<Uint8List> _downloadFromUrl(String url) async {
    try {
      // Make the HTTP request
      final response = await http.get(Uri.parse(url));

      // Check if the request was successful
      if (response.statusCode == 200) {
        final contentType = response.headers['content-type'] ?? 'application/octet-stream';

        // Validate content type is binary or image
        if (!_isValidBinaryType(contentType)) {
          throw StateError('Invalid content type: $contentType. Only binary or image types are supported.');
        }

        // Get the bytes directly from the response
        final bytes = response.bodyBytes;

        // Cache the downloaded file
        await _cacheFile(url, bytes, contentType);

        return bytes;
      } else {
        throw StateError('Failed to download file: HTTP status ${response.statusCode}');
      }
    } catch (e) {
      throw StateError('Error downloading from URL: $e');
    }
  }

  // Helper method to cache a file and its metadata
  Future<void> _cacheFile(String path, Uint8List data, String contentType) async {
    // Store the raw Uint8List in IndexedDB
    final writeTxn = _db.transaction([_filesStore, _contentTypeStore], idbModeReadWrite);
    try {
      final fileStore = writeTxn.objectStore(_filesStore);
      final contentTypeStore = writeTxn.objectStore(_contentTypeStore);
      await fileStore.put(data, path);
      await contentTypeStore.put(contentType, path);
    } finally {
      await writeTxn.completed;
    }
    // Update cache metadata
    await _updateCacheMetadata(path, data.length);
  }

  Future<void> _updateCacheMetadata(String path, int fileSize) async {
    final txn = _db.transaction(_metadataStore, idbModeReadWrite);
    final store = txn.objectStore(_metadataStore);

    try {
      // Update total cache size
      final currentSize = (await store.getObject('totalSize') as int?) ?? 0;
      final newSize = currentSize + fileSize;
      await store.put(newSize, 'totalSize');

      // Store individual file metadata
      await store.put({
        'size': fileSize,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'path': path,
      }, 'file_$path');

      // Check if we need to enforce cache size limits
      final maxSize = await store.getObject('maxSize') as int?;
      if (maxSize != null) {
        await _enforceCacheSize(maxSize);
      }
    } finally {
      await txn.completed;
    }
  }

  Future<void> _enforceCacheSize(int maxSize) async {
    final currentSize = await getCacheSize();
    if (currentSize <= maxSize) return;

    final txn = _db.transaction([_filesStore, _contentTypeStore, _metadataStore], idbModeReadWrite);
    final fileStore = txn.objectStore(_filesStore);
    final contentTypeStore = txn.objectStore(_contentTypeStore);
    final metaStore = txn.objectStore(_metadataStore);

    try {
      // Get all file metadata entries
      final cursor = metaStore.openCursor();
      final List<Map<String, dynamic>> fileEntries = [];

      await for (final cursorWithValue in cursor) {
        final key = cursorWithValue.key;
        final value = cursorWithValue.value;
        if (key.toString().startsWith('file_')) {
          fileEntries.add({
            ...value as Map,
            'key': key,
          });
        }
      }

      // Sort by timestamp (oldest first)
      fileEntries.sort((a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int));

      var sizeToFree = currentSize - maxSize;
      for (final entry in fileEntries) {
        if (sizeToFree <= 0) break;

        final path = entry['path'] as String;
        final size = entry['size'] as int;

        // Delete file from cache
        await fileStore.delete(path);
        // Delete content type
        await contentTypeStore.delete(path);
        // Delete metadata
        await metaStore.delete('file_$path');

        sizeToFree -= size;
      }

      // Update total size
      await metaStore.put(currentSize - (currentSize - maxSize), 'totalSize');
    } finally {
      await txn.completed;
    }
  }
}
