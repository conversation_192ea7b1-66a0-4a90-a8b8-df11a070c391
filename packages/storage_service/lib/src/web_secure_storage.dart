/// Web implementation of secure storage using IndexedDB
import 'dart:async';
import 'dart:convert';
import 'dart:js_interop' as js_interop;

import 'package:flutter/foundation.dart';
import 'package:idb_shim/idb_browser.dart';
import 'package:idb_shim/idb_shim.dart';
import 'package:web/web.dart' as web;

/// Secure storage implementation using IndexedDB for web
class WebSecureStorage {
  static const _wrapKey = 'wrapKey';
  static const _wrapKeyIv = 'wrapKeyIv';

  // IndexedDB specific constants
  static const _dbVersion = 1;
  static const _storeName = 'secure_items';

  // Database reference provided externally
  final Database _db;

  /// Factory for database creation
  final IdbFactory? _idbFactory = getIdbFactory();

  /// Check if IndexedDB is available
  bool get isIndexedDBAvailable => _idbFactory != null;

  WebSecureStorage(this._db) {
    if (!isIndexedDBAvailable) {
      throw UnsupportedError(
        'IndexedDB is not available in this environment. '
            'WebSecureStorage cannot be used.',
      );
    }
  }

  /// Begin a transaction for the secure items store
  ObjectStore _getSecureStore(Transaction txn) {
    return txn.objectStore(_storeName);
  }

  /// Returns true if the storage contains the given [key].
  Future<bool> containsKey({
    required String key,
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final fullKey = '$prefix.$key';

    final txn = _db.transaction(_storeName, 'readonly');
    final store = _getSecureStore(txn);

    try {
      final item = await store.getObject(fullKey);
      await txn.completed;
      return item != null;
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error checking key: $e');
      }
      return false;
    }
  }

  /// Safely abort a transaction if it's still active
  Future<void> _abortTransactionSafely(Transaction txn) async {
    try {
      txn.abort();
    } catch (e) {
      // Transaction might already be completed or aborted
      if (kDebugMode) {
        print('Error aborting transaction: $e');
      }
    }
  }

  /// Deletes associated value for the given [key].
  ///
  /// If the given [key] does not exist, nothing will happen.
  Future<void> delete({
    required String key,
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final fullKey = '$prefix.$key';

    final txn = _db.transaction(_storeName, 'readwrite');
    final store = _getSecureStore(txn);

    try {
      await store.delete(fullKey);
      await txn.completed;
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error deleting key: $e');
      }
    }
  }

  /// Deletes all keys with associated values for a given prefix.
  Future<void> deleteAll({
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final txn = _db.transaction(_storeName, 'readwrite');
    final store = _getSecureStore(txn);

    try {
      // Get all keys with the prefix
      final keyCursor = store.openKeyCursor(
        range: KeyRange.bound(
          '$prefix.',
          '$prefix.\uffff',
          false,
          false,
        ),
      );

      final keys = <String>[];
      await for (final cursor in keyCursor) {
        keys.add(cursor.key as String);
      }

      // Also delete the prefix entry itself (used for storing encryption key)
      keys.add(prefix);

      // Delete each key
      for (final key in keys) {
        await store.delete(key);
      }

      await txn.completed;
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error deleting all keys: $e');
      }
    }
  }

  /// Reads and decrypts the value for the given [key].
  ///
  /// Returns null if the key does not exist or if decryption fails.
  Future<String?> read({
    required String key,
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final fullKey = '$prefix.$key';

    final txn = _db.transaction(_storeName, 'readonly');
    final store = _getSecureStore(txn);

    try {
      final encryptedValue = await store.getObject(fullKey) as String?;
      await txn.completed;

      if (encryptedValue == null) return null;
      return await _decryptValue(encryptedValue, prefix, cryptoOptions);
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error reading key: $e');
      }
      return null;
    }
  }

  /// Decrypts and returns all keys with associated values.
  Future<Map<String, String>> readAll({
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final map = <String, String>{};
    final prefixWithDot = '$prefix.';

    final txn = _db.transaction(_storeName, 'readonly');
    final store = _getSecureStore(txn);

    try {
      final cursor = store.openCursor(
        range: KeyRange.bound(
          prefixWithDot,
          '$prefixWithDot\uffff',
          false,
          false,
        ),
      );

      final entries = <MapEntry<String, String?>>[];

      await for (final item in cursor) {
        final key = item.key as String;
        final encryptedValue = item.value as String?;
        entries.add(MapEntry(key, encryptedValue));
      }

      await txn.completed;

      // Process decryption outside the transaction
      for (final entry in entries) {
        final decryptedValue = await _decryptValue(entry.value, prefix, cryptoOptions);
        if (decryptedValue == null) continue;
        map[entry.key.substring(prefixWithDot.length)] = decryptedValue;
      }

      return map;
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error reading all keys: $e');
      }
      return {};
    }
  }

  js_interop.JSAny _getAlgorithm(Uint8List iv) {
    return {'name': 'AES-GCM', 'length': 256, 'iv': iv}.jsify()!;
  }

  Future<web.CryptoKey> _getEncryptionKey(
      js_interop.JSAny algorithm,
      String prefix,
      Map<String, String>? cryptoOptions,
      ) async {
    late web.CryptoKey encryptionKey;
    final useWrapKey = cryptoOptions?[_wrapKey]?.isNotEmpty ?? false;

    // Important: First perform all crypto key operations before beginning transaction
    // Use a separate transaction for key retrieval
    final readTxn = _db.transaction(_storeName, 'readonly');
    final readStore = _getSecureStore(readTxn);

    String? storedKey;

    try {
      // Check if we have a key stored using prefix as the key identifier
      storedKey = await readStore.getObject(prefix) as String?;
      await readTxn.completed;
    } catch (e) {
      await _abortTransactionSafely(readTxn);
      if (kDebugMode) {
        print('Error retrieving encryption key: $e');
      }
      rethrow;
    }

    if (storedKey != null) {
      final jwk = base64Decode(storedKey);

      if (useWrapKey) {
        final unwrappingKey = await _getWrapKey(cryptoOptions!);
        final unwrapAlgorithm = _getWrapAlgorithm(cryptoOptions);
        encryptionKey = await web.window.crypto.subtle
            .unwrapKey(
          'raw',
          jwk.toJS,
          unwrappingKey,
          unwrapAlgorithm,
          algorithm,
          false,
          ['encrypt', 'decrypt'].toJS,
        )
            .toDart;
      } else {
        encryptionKey = await web.window.crypto.subtle
            .importKey(
          'raw',
          jwk.toJS,
          algorithm,
          false,
          ['encrypt', 'decrypt'].toJS,
        )
            .toDart;
      }
    } else {
      encryptionKey = (await web.window.crypto.subtle
          .generateKey(algorithm, true, ['encrypt', 'decrypt'].toJS)
          .toDart)! as web.CryptoKey;

      final js_interop.JSAny? jsonWebKey;
      if (useWrapKey) {
        final wrappingKey = await _getWrapKey(cryptoOptions!);
        final wrapAlgorithm = _getWrapAlgorithm(cryptoOptions);
        jsonWebKey = await web.window.crypto.subtle
            .wrapKey(
          'raw',
          encryptionKey,
          wrappingKey,
          wrapAlgorithm,
        )
            .toDart;
      } else {
        jsonWebKey = await web.window.crypto.subtle
            .exportKey('raw', encryptionKey)
            .toDart;
      }

      final encodedKey = base64Encode(
        (jsonWebKey! as js_interop.JSArrayBuffer).toDart.asUint8List(),
      );

      // Store the key in a new transaction
      final writeTxn = _db.transaction(_storeName, 'readwrite');
      final writeStore = _getSecureStore(writeTxn);

      try {
        // Store the encoded key with the prefix as the primary key
        await writeStore.put(encodedKey, prefix);
        await writeTxn.completed;
      } catch (e) {
        await _abortTransactionSafely(writeTxn);
        if (kDebugMode) {
          print('Error storing encryption key: $e');
        }
        rethrow;
      }
    }

    return encryptionKey;
  }

  Future<web.CryptoKey> _getWrapKey(Map<String, String> cryptoOptions) async {
    final wrapKey = base64Decode(cryptoOptions[_wrapKey]!);
    final algorithm = _getWrapAlgorithm(cryptoOptions);
    return web.window.crypto.subtle
        .importKey(
      'raw',
      wrapKey.toJS,
      algorithm,
      true,
      ['wrapKey', 'unwrapKey'].toJS,
    )
        .toDart;
  }

  js_interop.JSAny _getWrapAlgorithm(Map<String, String> cryptoOptions) {
    final iv = base64Decode(cryptoOptions[_wrapKeyIv]!);
    return _getAlgorithm(iv);
  }

  /// Encrypts and saves the [key] with the given [value].
  ///
  /// If the key was already in the storage, its associated value is changed.
  /// If the value is null, deletes associated value for the given [key].
  Future<void> write({
    required String key,
    required String value,
    required String prefix,
    Map<String, String>? cryptoOptions,
  }) async {
    final fullKey = '$prefix.$key';

    // Generate IV and prepare algorithm outside of transaction
    final iv = (web.window.crypto.getRandomValues(Uint8List(12).toJS)
    as js_interop.JSUint8Array)
        .toDart;

    final algorithm = _getAlgorithm(iv);

    // Perform all crypto operations before starting a transaction
    final encryptionKey = await _getEncryptionKey(algorithm, prefix, cryptoOptions);

    final encryptedContent = (await web.window.crypto.subtle
        .encrypt(
      algorithm,
      encryptionKey,
      Uint8List.fromList(
        utf8.encode(value),
      ).toJS,
    )
        .toDart)! as js_interop.JSArrayBuffer;

    final encoded = '${base64Encode(iv)}.'
        '${base64Encode(encryptedContent.toDart.asUint8List())}';

    // Now that all async crypto work is complete, start a new transaction for database write
    final txn = _db.transaction(_storeName, 'readwrite');
    final store = _getSecureStore(txn);

    try {
      // Store with explicit key
      await store.put(encoded, fullKey);
      await txn.completed;
    } catch (e) {
      await _abortTransactionSafely(txn);
      if (kDebugMode) {
        print('Error writing key: $e');
      }
      rethrow; // Rethrow to let caller know there was an error
    }
  }

  Future<String?> _decryptValue(
      String? cypherText,
      String prefix,
      Map<String, String>? cryptoOptions,
      ) async {
    if (cypherText != null) {
      try {
        final parts = cypherText.split('.');

        final iv = base64Decode(parts[0]);
        final algorithm = _getAlgorithm(iv);

        final decryptionKey = await _getEncryptionKey(algorithm, prefix, cryptoOptions);

        final value = base64Decode(parts[1]);

        final decryptedContent = await web.window.crypto.subtle
            .decrypt(
          _getAlgorithm(iv),
          decryptionKey,
          Uint8List.fromList(value).toJS,
        )
            .toDart;

        final plainText = utf8.decode(
          (decryptedContent! as js_interop.JSArrayBuffer).toDart.asUint8List(),
        );

        return plainText;
      } on Exception catch (e, s) {
        if (kDebugMode) {
          print(e);
          debugPrintStack(stackTrace: s);
        }
      }
    }

    return null;
  }
}

extension on List<String> {
  js_interop.JSArray<js_interop.JSString> get toJS => [
    ...map((e) => e.toJS),
  ].toJS;
}