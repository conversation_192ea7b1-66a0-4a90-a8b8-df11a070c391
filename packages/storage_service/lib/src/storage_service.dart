import 'dart:typed_data';

import 'web_storage_service.dart';

abstract class StorageService {
  /// Downloads and caches a file from Firebase Storage
  /// Returns the file data as Uint8List
  Future<Uint8List> getCachedFile(String storagePath);

  /// Returns the content type for a cached file
  Future<String?> getFileContentType(String storagePath);

  /// Clears all cached files and metadata
  Future<void> clearCache();

  /// Checks if a file exists in cache
  Future<bool> isFileCached(String storagePath);

  /// Gets the size of the cache in bytes
  Future<int> getCacheSize();

  /// Sets maximum cache size in bytes
  Future<void> setMaxCacheSize(int maxSize);

  /// Gets a stored preference value
  Future<String?> getPref(String key, [String? prefix]);

  /// Sets a preference value
  Future<void> setPref(String key, String? value, [String? prefix]);

  /// Factory constructor to get platform-specific implementation
  factory StorageService() {
    bool kIsWeb = const bool.fromEnvironment('dart.library.js_util');
    if (kIsWeb) {
      return WebStorageService();
    } else {
      throw UnsupportedError('Unsupported platform for StorageService');
    }
  }
}