@TestOn('browser')
library;

import 'dart:convert';
import 'dart:typed_data';

import 'package:storage_service/src/web_storage_service.dart';
import 'package:test/test.dart';
import 'package:idb_shim/idb_browser.dart';

void main() {
  late WebStorageService storageService;

  setUp(() async {
    // Create a new instance for each test
    storageService = WebStorageService();
  });

  tearDown(() async {
    // Clean up after each test
    await storageService.dispose();

    // Delete the test database to ensure clean state
    final idbFactory = getIdbFactory();
    await idbFactory?.deleteDatabase('storage_cache');
  });

  group('Storage Operations', () {
    test('should store and retrieve preferences', () async {
      const testKey = 'test_preference';
      const testValue = 'test_value';

      // Store preference
      await storageService.setPref(testKey, testValue);

      // Retrieve preference
      final retrievedValue = await storageService.getPref(testKey);
      expect(retrievedValue, equals(testValue));
    });

    test('should handle null preference values', () async {
      const testKey = 'null_test';

      // Store null value
      await storageService.setPref(testKey, null);

      // Retrieve value
      final retrievedValue = await storageService.getPref(testKey);
      expect(retrievedValue, isNull);
    });

    test('should clear preferences', () async {
      // Store multiple preferences
      await storageService.setPref('key1', 'value1');
      await storageService.setPref('key2', 'value2');

      // Clear cache (which includes preferences)
      await storageService.clearCache();

      // Verify preferences are cleared
      final value1 = await storageService.getPref('key1');
      final value2 = await storageService.getPref('key2');
      expect(value1, isNull);
      expect(value2, isNull);
    });
  });

  group('Cache Management', () {
    test('should track cache size correctly', () async {
      // Initial size should be 0
      final initialSize = await storageService.getCacheSize();
      expect(initialSize, equals(0));

      // Store some test data
      const testPath = 'test/file.txt';
      final testData = 'data:text/plain;base64,${base64.encode(utf8.encode('Test content'))}';

      final txn = await _getWriteTransaction(storageService, '_filesStore');
      final store = txn.objectStore('files');
      await store.put(testData, testPath);
      await txn.completed;

      // Update metadata
      await _updateTestMetadata(storageService, testPath, utf8.encode('Test content').length);

      // Check updated size
      final updatedSize = await storageService.getCacheSize();
      expect(updatedSize, greaterThan(0));
    });

    test('should enforce max cache size', () async {
      const maxSize = 100; // Small size to trigger cleanup
      await storageService.setMaxCacheSize(maxSize);

      // Add files that exceed max size
      for (var i = 0; i < 5; i++) {
        final testPath = 'test/file_$i.txt';
        final testData = 'data:text/plain;base64,${base64.encode(utf8.encode('Test content $i' * 10))}';

        final txn = await _getWriteTransaction(storageService, '_filesStore');
        final store = txn.objectStore('files');
        await store.put(testData, testPath);
        await txn.completed;

        await _updateTestMetadata(storageService, testPath, utf8.encode('Test content $i' * 10).length);
      }

      // Verify cache size is enforced
      final finalSize = await storageService.getCacheSize();
      expect(finalSize, lessThanOrEqualTo(maxSize));
    });
  });

  group('File Operations', () {
    test('should check file cache status correctly', () async {
      const testPath = 'test/cache_check.txt';

      // Initially file should not be cached
      var isCached = await storageService.isFileCached(testPath);
      expect(isCached, isFalse);

      // Store test file
      final testData = 'data:text/plain;base64,${base64.encode(utf8.encode('Test content'))}';
      final txn = await _getWriteTransaction(storageService, '_filesStore');
      final store = txn.objectStore('files');
      await store.put(testData, testPath);
      await txn.completed;

      // Now file should be cached
      isCached = await storageService.isFileCached(testPath);
      expect(isCached, isTrue);
    });

    test('should download and cache new file', () async {
      const storagePath = 'test/download.txt';

      // Setup test file content
      final testContent = 'Hello, World!';
      final testBytes = utf8.encode(testContent);
      final expectedDataUrl = 'data:text/plain;base64,${base64.encode(testBytes)}';

      // Get file - this should trigger download and caching
      final downloadedUrl = await storageService.getCachedFile(storagePath);
      expect(downloadedUrl, expectedDataUrl);

      // Verify file is now cached
      final isCached = await storageService.isFileCached(storagePath);
      expect(isCached, isTrue);

      // Get file again - should return cached version
      final cachedUrl = await storageService.getCachedFile(storagePath);
      expect(cachedUrl, expectedDataUrl);
    });

    test('should handle multiple file downloads and caching', () async {
      final files = [
        {'path': 'test/file1.txt', 'content': 'Content 1'},
        {'path': 'test/file2.txt', 'content': 'Content 2'},
        {'path': 'test/file3.txt', 'content': 'Content 3'},
      ];

      // Download and cache multiple files
      for (var file in files) {
        final downloadedUrl = await storageService.getCachedFile(file['path']!);
        expect(downloadedUrl, contains(base64.encode(utf8.encode(file['content']!))));
      }

      // Verify all files are cached
      for (var file in files) {
        final isCached = await storageService.isFileCached(file['path']!);
        expect(isCached, isTrue);
      }
    });

    test('should handle large files correctly', () async {
      const storagePath = 'test/large_file.dat';

      // Create large test content (1MB)
      final largeContent = List.generate(1024 * 1024, (i) => i % 256).toList();
      final largeBytes = Uint8List.fromList(largeContent);
      final expectedDataUrl = 'data:application/octet-stream;base64,${base64.encode(largeBytes)}';

      // Download and cache large file
      final downloadedUrl = await storageService.getCachedFile(storagePath);
      expect(downloadedUrl, expectedDataUrl);

      // Verify file size in metadata
      final txn = await _getWriteTransaction(storageService, '_metadataStore');
      final store = txn.objectStore('metadata');
      final metadataObj = await store.getObject('file_$storagePath');
      final metadata = metadataObj as Map<String, dynamic>;
      expect(metadata['size'], equals(largeBytes.length));
      await txn.completed;
    });

    test('should handle file updates correctly', () async {
      const storagePath = 'test/update.txt';

      // Initial content
      final initialContent = 'Initial content';
      final initialBytes = utf8.encode(initialContent);
      final initialDataUrl = 'data:text/plain;base64,${base64.encode(initialBytes)}';

      // Store initial version
      final initialTxn = await _getWriteTransaction(storageService, '_filesStore');
      final initialStore = initialTxn.objectStore('files');
      await initialStore.put(initialDataUrl, storagePath);
      await initialTxn.completed;

      // Verify initial version is cached
      final initialCachedUrl = await storageService.getCachedFile(storagePath);
      expect(initialCachedUrl, initialDataUrl);

      // Update content
      final updatedContent = 'Updated content';
      final updatedBytes = utf8.encode(updatedContent);
      final updatedDataUrl = 'data:text/plain;base64,${base64.encode(updatedBytes)}';

      // Store updated version
      final txn = await _getWriteTransaction(storageService, '_filesStore');
      final store = txn.objectStore('files');
      await store.put(updatedDataUrl, storagePath);
      await txn.completed;

      // Get updated file
      final retrievedUrl = await storageService.getCachedFile(storagePath);
      expect(retrievedUrl, updatedDataUrl);
    });

    test('should clean up old files when cache limit is reached', () async {
      // Set small cache size limit
      const maxCacheSize = 1024; // 1KB
      await storageService.setMaxCacheSize(maxCacheSize);

      // Add files until we exceed cache size
      var fileCount = 0;
      while (await storageService.getCacheSize() < maxCacheSize * 2) {
        final path = 'test/overflow_$fileCount.txt';
        final content = 'Content for file $fileCount' * 10; // Make files big enough
        final dataUrl = 'data:text/plain;base64,${base64.encode(utf8.encode(content))}';

        final txn = await _getWriteTransaction(storageService, '_filesStore');
        final store = txn.objectStore('files');
        await store.put(dataUrl, path);
        await txn.completed;

        await _updateTestMetadata(storageService, path, utf8.encode(content).length);
        fileCount++;
      }

      // Verify cache size is enforced
      final finalSize = await storageService.getCacheSize();
      expect(finalSize, lessThanOrEqualTo(maxCacheSize));

      // Verify oldest files were removed
      final firstFile = await storageService.isFileCached('test/overflow_0.txt');
      expect(firstFile, isFalse);
    });
  });
}

// Helper functions for testing
Future<Transaction> _getWriteTransaction(WebStorageService service, String storeName) async {
  final idbFactory = getIdbFactory();
  final db = await idbFactory!.open('storage_cache',
      version: 1,
      onUpgradeNeeded: (VersionChangeEvent event) {
        Database db = event.database;
        db.createObjectStore('files');
        db.createObjectStore('preferences');
        db.createObjectStore('metadata');
      });

  return db.transaction(storeName.substring(1), 'readwrite');
}

Future<void> _updateTestMetadata(WebStorageService service, String path, int size) async {
  final txn = await _getWriteTransaction(service, '_metadataStore');
  final store = txn.objectStore('metadata');

  // Get current total size and ensure it's an int
  final totalSizeObj = await store.getObject('totalSize');
  final currentSize = (totalSizeObj as int?) ?? 0;

  // Update total size
  await store.put(currentSize + size, 'totalSize');

  // Add file metadata
  await store.put({
    'size': size,
    'timestamp': DateTime.now().millisecondsSinceEpoch,
    'path': path,
  }, 'file_$path');

  await txn.completed;
}