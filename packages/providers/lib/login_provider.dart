// packages/providers/lib/login_provider.dart

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'login_provider.g.dart';

@riverpod
class Login extends _$Login {
  @override
  FutureOr<User?> build() => FirebaseAuth.instance.currentUser;

  Future<void> signInWithEmail(String email, String password) async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      final userCredential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return userCredential.user;
    });
  }

  Future<void> resetPassword(String email) async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);
      return FirebaseAuth.instance.currentUser;
    });
  }

  Future<void> signInWithGoogle() async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      final GoogleSignInAccount? googleUser = await GoogleSignIn(scopes: ['email', 'profile']).signIn();
      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
      return userCredential.user;
    });

/*
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      if (kIsWeb) {
        final googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

        final googleUser = await googleSignIn.signIn();
        if (googleUser == null) throw Exception("Sign-in cancelled");
        final googleAuth = await googleUser.authentication;

        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        await FirebaseAuth.instance.signInWithCredential(credential);
*/

/*
        final GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        googleProvider.addScope('email');
        googleProvider.addScope('profile');
        // attempt to link accounts but only if current user is anonymous
        if (FirebaseAuth.instance.currentUser?.isAnonymous == true) {
          // get idToken from firebase to link the anonymous user with the new login
          final idToken = await FirebaseAuth.instance.currentUser?.getIdToken();
          // link the anonymous user with the new login

          final googleAuth = await googleUser.authentication;
          final credential = GoogleAuthProvider.credential(
            accessToken: googleAuth.accessToken,
            idToken: googleAuth.idToken,
          );

          final credential = GoogleAuthProvider.credential(idToken: idToken);
          await _linkWithCredentials(credential);
        }
        // Once signed in, return the UserCredential
        final newCredentials = await FirebaseAuth.instance.signInWithPopup(googleProvider);
        return newCredentials.user;
      }
*/
    // android
/*
      if (defaultTargetPlatform == TargetPlatform.android){
        final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
        if (googleUser == null) return null;

        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        // attempt to link accounts but only if current user is anonymous
        if (FirebaseAuth.instance.currentUser?.isAnonymous == true) {
          await _linkWithCredentials(credential);
        }

        final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
        return userCredential.user;
      }

      dbgPrint('signInWithGoogle: not implemented for this platform: $defaultTargetPlatform');
      throw UnimplementedError('signInWithGoogle: not implemented for this platform: $defaultTargetPlatform');
    });
*/
  }

/*
  Future<void> _linkWithCredentials(OAuthCredential credential) async {
    try {
      await FirebaseAuth.instance.currentUser?.linkWithCredential(credential);
      dbgPrint('signInWithGoogle: linked successfully');
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "provider-already-linked":
          dbgPrint("signInWithGoogle: The provider has already been linked to the user.");
          break;
        case "invalid-credential":
          dbgPrint("signInWithGoogle: The provider's credential is not valid.");
          break;
        case "credential-already-in-use":
          dbgPrint("signInWithGoogle: The account corresponding to the credential already exists, "
              "or is already linked to a Firebase User.");
          break;
        default:
          dbgPrint("signInWithGoogle: Unknown error: $e");
      }
    }
  }
*/

  Future<void> signInWithApple() async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      final provider = OAuthProvider("apple.com")
        ..addScope('email')
        ..addScope('name');

      final credential = await FirebaseAuth.instance.signInWithPopup(provider);

      return credential.user;
    });
  }

/*
  Future<void> signInWithFacebook() async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      final LoginResult result = await FacebookAuth.instance.login();
      if (result.status != LoginStatus.success) return null;

      final OAuthCredential credential =
      FacebookAuthProvider.credential(result.accessToken!.token);

      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
      return userCredential.user;
    });
  }
*/

  Future<void> signInAnonymously() async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      final userCredential = await FirebaseAuth.instance.signInAnonymously();
      final user = userCredential.user;
      return userCredential.user;
    });
  }

  Future<void> signOut() async {
    state = AsyncLoading<User?>().copyWithPrevious(state);
    state = await AsyncValue.guard(() async {
      await FirebaseAuth.instance.signOut();
      return null;
    });
  }

  void resetState() {
    state = AsyncData(FirebaseAuth.instance.currentUser);
  }
}
