// packages/providers/lib/firebase_user_provider.dart

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'common.dart';

part 'firebase_user_provider.g.dart';

@Riverpod(keepAlive: true)
Stream<User?> firebaseUser(Ref ref) async* {
  dbgPrint('firebaseUserProvider: init');
  await for (final user in FirebaseAuth.instance.authStateChanges()) {
    dbgPrint('firebaseUserProvider: authChange: $user');
    yield user;
  }
}
