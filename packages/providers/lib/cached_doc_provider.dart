import 'dart:convert';
import 'package:entities/timestamp_converter.dart';
import 'package:providers/connectivity_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:storage_service/storage_service.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'common.dart';

part 'cached_doc_provider.g.dart';
part 'cached_doc_provider.freezed.dart';

@freezed
class CacheEntry with _$CacheEntry {
  const factory CacheEntry({
    required Map<String, dynamic>? data,
    required DateTime timestamp,
  }) = _CacheEntry;

  factory CacheEntry.fromJson(Map<String, dynamic> json) => _$CacheEntryFromJson(json);
}

@riverpod
class CachedDoc extends _$CachedDoc {
  /// Cache expiration duration
  static const cacheDuration = Duration(hours: 1);

  /// Storage service instance
  late final StorageService _storage;

  /// Firestore instance
  late final FirebaseFirestore _firestore;

  @override
  Stream<Map<String, dynamic>?> build(String path) async* {
    _storage = StorageService();
    _firestore = FirebaseFirestore.instance;

    // make sure the cache refreshes on online state changes
    await ref.watch(connectivityProvider.future);

    // Try to load from cache first
    final cachedData = await _loadFromCache(path);
    if (cachedData != null) {
      dbgPrint('cachedDocProvider: cache: $path');
      yield cachedData.data;

      // If cache is expired, fetch fresh data
      if (_isCacheExpired(cachedData.timestamp)) {
        final freshData = await _fetchAndCache(path);
        dbgPrint('cachedDocProvider: online after cache: $path');
        yield freshData;
      }
    } else {
      // No cache available, fetch from Firebase
      final freshData = await _fetchAndCache(path);
      dbgPrint('cachedDocProvider: no cache: $path');
      yield freshData;
    }
  }

  /// Load document from cache
  Future<CacheEntry?> _loadFromCache(String path) async {
    try {
      final cached = await _storage.getPref('doc:$path');
      if (cached != null) {
        return CacheEntry.fromJson(jsonDecode(cached));
      }
    } catch (e) {
      dbgPrint('Error loading from cache: $e');
      rethrow;
    }
    return null;
  }

  /// Save document to cache
  Future<void> _saveToCache(String path, Map<String, dynamic> data) async {
    try {
      final entry = CacheEntry(
        data: data,
        timestamp: DateTime.now(),
      );
      await _storage.setPref('doc:$path', jsonEncodeWithTS(entry.toJson()));
    } catch (e) {
      dbgPrint('Error saving to cache: $e');
      rethrow;
    }
  }

  /// Fetch document from Firebase and cache it
  Future<Map<String, dynamic>?> _fetchAndCache(String path) async {
    final doc = await _firestore.doc(path).get();
    if (!doc.exists) {
      return null;
      // throw StateError('Document does not exist: $path');
    }

    final data = doc.data()!;
    await _saveToCache(path, data);
    return data;
  }

  /// Check if cache entry is expired
  bool _isCacheExpired(DateTime timestamp) {
    return DateTime.now().difference(timestamp) > cacheDuration;
  }

  /// Manual refresh method to force fetch fresh data
  Future<void> refresh() async {
    state = const AsyncLoading();
    try {
      final freshData = await _fetchAndCache(path);
      state = AsyncData(freshData);
    } catch (error, stack) {
      state = AsyncError(error, stack);
    }
  }
}
