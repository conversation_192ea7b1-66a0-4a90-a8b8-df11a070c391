// packages/providers/lib/question_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'question_provider.g.dart';

@riverpod
class Question extends _$Question {
  @override
  FutureOr<void> build() {}

  // Build a query with the specified filters
  Query _buildQuery({
    String? courseId,
    String? yearId,
    String? subjectId,
    String? type,
    String? difficulty,
    String? status,
  }) {
    Query query = FirebaseFirestore.instance.collection('questions');

    // Apply filters if provided
    if (courseId != null) {
      query = query.where('courseId', isEqualTo: courseId);
    }
    if (yearId != null) {
      query = query.where('yearId', isEqualTo: yearId);
    }
    if (subjectId != null) {
      query = query.where('subjectId', isEqualTo: subjectId);
    }
    if (type != null) {
      query = query.where('type', isEqualTo: type);
    }
    if (difficulty != null) {
      query = query.where('difficulty', isEqualTo: difficulty);
    }
    if (status != null) {
      query = query.where('status', isEqualTo: status);
    }

    // Order by updated time by default
    query = query.orderBy('updatedAt', descending: true);
    
    return query;
  }

  // Get a question by ID
  Future<QuestionEntity?> getQuestionById(String questionId) async {
    final doc = await FirebaseFirestore.instance.collection('questions').doc(questionId).get();

    if (!doc.exists) {
      return null;
    }

    final questionData = doc.data()!;
    questionData['id'] = doc.id;
    return QuestionEntity.fromJson(questionData);
  }

  // Get a list of questions (one-time fetch)
  Future<List<QuestionEntity>> getQuestions({
    String? courseId,
    String? yearId,
    String? subjectId,
    ContentType? type,
    QuestionDifficulty? difficulty,
    ContentStatus? status,
    int limit = 50,
    DocumentSnapshot? startAfter,
  }) async {
    Query query = _buildQuery(
      courseId: courseId,
      yearId: yearId,
      subjectId: subjectId,
      type: type?.name.toLowerCase(),
      difficulty: difficulty?.name.toLowerCase(),
      status: status?.name.toLowerCase(),
    );

    // Apply pagination
    if (limit > 0) {
      query = query.limit(limit);
    }
    
    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    final querySnapshot = await query.get();
    return querySnapshot.docs
        .map((doc) => QuestionEntity.fromJson({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }))
        .toList();
  }

  // Watch questions with the specified filters
  Stream<List<QuestionEntity>> watchQuestions({
    String? courseId,
    String? yearId,
    String? subjectId,
    String? type,
    QuestionDifficulty? difficulty,
    ContentStatus? status,
    int limit = 50,
  }) {
    Query query = _buildQuery(
      courseId: courseId,
      yearId: yearId,
      subjectId: subjectId,
      type: type, // Type is already a string here
      difficulty: difficulty?.name.toLowerCase(),
      status: status?.name.toLowerCase(),
    );
    
    // Apply pagination
    if (limit > 0) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) => snapshot.docs
        .map((doc) => QuestionEntity.fromJson({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }))
        .toList());
  }

  // Add a new question
  Future<String> addQuestion(QuestionEntity question) async {
    final String id = question.id ?? FirebaseFirestore.instance.collection('questions').doc().id;

    final docRef = FirebaseFirestore.instance.collection('questions').doc(id);

    // Create properly formatted data
    final data = _prepareQuestionData(question.copyWith(id: id));

    // Set data
    await docRef.set(data);
    return id;
  }

  // Update an existing question
  Future<void> updateQuestion(QuestionEntity question) async {
    if (question.id == null) {
      throw Exception('Cannot update question without ID');
    }

    // Create properly formatted data
    final data = _prepareQuestionData(question, isUpdate: true);
    
    await FirebaseFirestore.instance.collection('questions').doc(question.id).update(data);
  }



  // Prepare question data for storage with consistent formatting and image paths
  Map<String, dynamic> _prepareQuestionData(QuestionEntity question, {bool isUpdate = false}) {
    final data = question.toJson();

    // Remove ID from data (handled by Firestore)
    data.remove('id');

    // Note: Enum values are automatically handled by @JsonEnum(fieldRename: FieldRename.snake)
    // No manual conversion needed - toJson() handles the proper snake_case conversion

    // Set timestamps
    final now = DateTime.now().toIso8601String();
    data['updatedAt'] = now;

    // Only set createdAt for new items
    if (!isUpdate) {
      data['createdAt'] = now;
    }

    return data;
  }

  // Publish a question
  Future<void> publishQuestion(String questionId) async {
    final docRef = FirebaseFirestore.instance.collection('questions').doc(questionId);
    final questionDoc = await docRef.get();

    if (!questionDoc.exists) {
      throw Exception('Question not found');
    }

    await docRef.update({
      'status': ContentStatus.published.name, // Use enum for consistency
      'updatedAt': DateTime.now().toIso8601String()
    });
  }

  // Delete a question
  Future<void> deleteQuestion(String questionId) async {
    await FirebaseFirestore.instance.collection('questions').doc(questionId).delete();
  }
}

// Convenience provider for getting a question by ID
@riverpod
Future<QuestionEntity?> questionById(Ref ref, {required String questionId}) {
  return ref.read(questionProvider.notifier).getQuestionById(questionId);
}

// Provider for streaming filtered questions
@riverpod
Stream<List<QuestionEntity>> filteredQuestions(
  Ref ref, {
  String? courseId,
  String? yearId,
  String? subjectId,
  String? type,
  QuestionDifficulty? difficulty,
  ContentStatus? status,
  int limit = 50,
}) {
  // If no required filters are provided, still show all questions
  final shouldShowAll = courseId == null && yearId == null && subjectId == null;
  
  if (shouldShowAll) {
    // Get all questions (with optional type/difficulty/status filters)
    return ref.read(questionProvider.notifier).watchQuestions(
      type: type,
      difficulty: difficulty,
      status: status,
      limit: limit,
    );
  }
  
  // Otherwise, apply all provided filters
  return ref.read(questionProvider.notifier).watchQuestions(
    courseId: courseId,
    yearId: yearId,
    subjectId: subjectId,
    type: type,
    difficulty: difficulty,
    status: status,
    limit: limit,
  );
}

// Query for FirestoreListView for more efficient pagination
@riverpod
Query questionsQuery(
  Ref ref, {
  String? courseId,
  String? yearId,
  String? subjectId,
  String? type,
  String? difficulty,
  String? status,
}) {
  return ref.read(questionProvider.notifier)._buildQuery(
    courseId: courseId,
    yearId: yearId,
    subjectId: subjectId,
    type: type,
    difficulty: difficulty,
    status: status,
  );
}