import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in_platform_interface/google_sign_in_platform_interface.dart';
import 'package:google_sign_in_web/google_sign_in_web.dart';

import 'common.dart';

part 'google_sign_in.g.dart';

part 'google_sign_in.freezed.dart';

/// Todo: Web authentication and authorization are now supposed to be separated. However, stuff does not appear to work (yet)
/// Not really in use until this will be implemented.

@freezed
class GoogleAuthState with _$GoogleAuthState {
  const factory GoogleAuthState({
    GoogleSignInAccount? currentUser,
    @Default(false) bool isAuthorized,
  }) = _GoogleAuthState;
}

// GoogleSignIn _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);

@Riverpod(keepAlive: true)
GoogleSignIn googleSignIn(Ref ref) {
  dbgPrint('googleSignInProvider: init');
  final GoogleSignInPlugin googleSignInPlugin = GoogleSignInPlatform.instance as GoogleSignInPlugin;
  googleSignInPlugin.initWithParams(SignInInitParameters());
  dbgPrint('googleSignInProvider: init 2');


  // dbgPrint('googleSignInProvider: ${_googleSignIn.scopes}');
  return GoogleSignIn(scopes: ['email', 'profile']);
}

@riverpod
Stream<GoogleAuthState> googleAuthStateStream(Ref ref) async* {
  final googleSignIn = ref.watch(googleSignInProvider);

  final account = await googleSignIn.signInSilently();
  yield GoogleAuthState(
    currentUser: account,
    isAuthorized: await isAuthorized(account, googleSignIn),
  );

  await for (final account in googleSignIn.onCurrentUserChanged) {
    yield GoogleAuthState(
      currentUser: account,
      isAuthorized: await isAuthorized(account, googleSignIn),
    );
  }
}

Future<bool> isAuthorized(GoogleSignInAccount? account, GoogleSignIn googleSignIn) async {
  bool isAuthorized = account != null;
  if (kIsWeb && account != null) {
    isAuthorized = await googleSignIn.canAccessScopes(['email', 'profile']);
  }
  return isAuthorized;
}
