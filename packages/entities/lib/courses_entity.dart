// packages/entities/lib/courses_entity.dart

import 'package:entities/course_entity.dart';
import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'courses_entity.freezed.dart';
part 'courses_entity.g.dart';

@Freezed(toJson: true)
class CoursesEntity extends Entity with _$CoursesEntity {
  factory CoursesEntity({
    @Default({}) Map<String, CourseEntity> courses,
  }) = _CoursesEntity;

  @override
  factory CoursesEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$CoursesEntityFromJson, json);
}
