import 'dart:convert';

import 'package:flutter/services.dart';

/// Base class to mark all of our data classes as entity type and enforce toJson.
abstract class Entity {
  Object toJson();

}

/// Use reportAnyJsonBugs as wrapper around _${SomeEntity}FromJson to have parsing errors reported.
///
/// See [UserEntity.fromJson(json)] as an example.
T reportAnyJsonBugs<T>(T Function(Map<String, dynamic>) parseFunction, Map<String, dynamic> json) {
  try {
    return parseFunction(json);
  } catch (error, stackTrace) {
    if (appFlavor != 'prod') {
      // we cannot use .withIntent when the json conversion failed
      // final prettyJson =JsonEncoder.withIndent('  ').convert(json);
      // ignore: avoid_print
      print('JSON parsing error for ${T.toString()}: $error\nOriginal JSON:\n$json\nStack trace: $stackTrace');
    }
    rethrow;
  }
}