// packages/entities/lib/course_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'year_entity.dart';

part 'course_entity.freezed.dart';
part 'course_entity.g.dart';

@Freezed(toJson: true)
class CourseEntity extends Entity with _$CourseEntity {
  factory CourseEntity({
    required String id,
    @Default({}) Map<String, YearEntity> years,
  }) = _CourseEntity;

  @override
  factory CourseEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$CourseEntityFromJson, json);
}
