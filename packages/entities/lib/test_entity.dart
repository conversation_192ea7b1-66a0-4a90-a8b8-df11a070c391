// packages/entities/lib/test_entity.dart

import 'package:entities/entity.dart';
import 'package:entities/timestamp_converter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'question_entity.dart';

part 'test_entity.freezed.dart';
part 'test_entity.g.dart';


@freezed
class TestEntity extends Entity with _$TestEntity {
  factory TestEntity({
    String? id,
    @TimestampConverter() DateTime? createdAt,
    @TimestampConverter() DateTime? updatedAt,
    required String courseId,
    required String yearId,
    required String subjectId,
    required String name,
    required ContentType type,
    int? duration,
    @Default(QuestionDifficulty.easy) QuestionDifficulty difficulty,
    @Default(ContentStatus.draft) ContentStatus status,
    @Default([]) List<String> questionIds,
  }) = _TestEntity;

  @override
  factory TestEntity.fromJson(Map<String, dynamic> json) =>
      reportAnyJsonBugs(_$TestEntityFromJson, json);
}
