// packages/entities/lib/subject_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'published_test_entity.dart';

part 'subject_entity.freezed.dart';
part 'subject_entity.g.dart';

@Freezed(toJson: true)
class SubjectEntity extends Entity with _$SubjectEntity {
  factory SubjectEntity({
    required String id,
    required String name,
    @Default({}) Map<String, PublishedTestEntity> tests,
  }) = _SubjectEntity;

  @override
  factory SubjectEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$SubjectEntityFromJson, json);
}
