import 'package:freezed_annotation/freezed_annotation.dart';

part 'question_stats_entity.freezed.dart';
part 'question_stats_entity.g.dart';

/// Simple question statistics
@Freezed(toJson: true)
class QuestionStatsEntity with _$QuestionStatsEntity {
  const QuestionStatsEntity._();

  factory QuestionStatsEntity({
    /// How many people attempted this question
    @Default(0) int totalAttempts,

    /// How many selected each option on FIRST attempt only {"0": count, "1": count, ...}
    @Default({}) Map<String, int> firstAttemptOptionCounts,

    /// Success counts by attempt position
    @Default(0) int correctOnFirstAttempt,
    @Default(0) int correctOnSecondAttempt, 
    @Default(0) int correctOnThirdAttempt,
  }) = _QuestionStatsEntity;

  factory QuestionStatsEntity.fromJson(Map<String, dynamic> json) =>
      _$QuestionStatsEntityFromJson(json);

  /// Get first attempt percentage for a specific option
  /// Example: getFirstAttemptPercentage("0") returns percentage who chose option A first
  double getFirstAttemptPercentage(String optionIndex) {
    if (totalAttempts == 0) return 0.0;
    final count = firstAttemptOptionCounts[optionIndex] ?? 0;
    return (count / totalAttempts) * 100;
  }

  /// Get success rate by attempt position (1st, 2nd, 3rd)
  /// Example: getSuccessRateByPosition(1) returns % who got it right on 1st try
  double getSuccessRateByPosition(int attemptPosition) {
    if (totalAttempts == 0) return 0.0;
    
    final successCount = switch (attemptPosition) {
      1 => correctOnFirstAttempt,
      2 => correctOnSecondAttempt,
      3 => correctOnThirdAttempt,
      _ => 0,
    };
    
    return (successCount / totalAttempts) * 100;
  }
}
