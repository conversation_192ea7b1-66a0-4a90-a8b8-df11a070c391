import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'question_entity.dart';

part 'published_test_entity.freezed.dart';
part 'published_test_entity.g.dart';

@freezed
class PublishedTestEntity extends Entity with _$PublishedTestEntity {
  factory PublishedTestEntity({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    required String courseId,
    required String yearId,
    required String subjectId,
    required String name,
    required ContentType type,
    int? duration,
    required String subscriptionId,
    @Default(0) int questionCount,
    @Default(0) int fullQuestionCount,
    @Default(1) int version,
    @Default(QuestionDifficulty.easy) QuestionDifficulty difficulty,
    @Default(ContentStatus.draft) ContentStatus status,
    @Default([]) List<QuestionEntity> questions,
  }) = _PublishedTestEntity;

  @override
  factory PublishedTestEntity.fromJson(Map<String, dynamic> json) =>
      reportAnyJsonBugs(_$PublishedTestEntityF<PERSON><PERSON><PERSON>, json);
}
