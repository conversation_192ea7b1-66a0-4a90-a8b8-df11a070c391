import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:entities/timestamp_converter.dart';

part 'transaction_entity.freezed.dart';
part 'transaction_entity.g.dart';

@freezed
class TransactionEntity with _$TransactionEntity {
  const factory TransactionEntity({
    required String id,
    required String userId,
    required String courseId,
    required String yearId,
    required String planType,
    required int months,
    required double amount,
    required bool success,
    required bool isVoided,
    required bool isRefunded,
    required String status,
    String? message,
    @TimestampConverter() required DateTime timestamp,
  }) = _TransactionEntity;

  factory TransactionEntity.fromJson(Map<String, dynamic> json) => _$TransactionEntityFromJson(json);
}
