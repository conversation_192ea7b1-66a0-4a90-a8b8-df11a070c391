// packages/entities/lib/year_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'subject_entity.dart';

part 'year_entity.freezed.dart';
part 'year_entity.g.dart';

@Freezed(toJson: true)
class YearEntity extends Entity with _$YearEntity {
  factory YearEntity({
    required String id,
    @Default({}) Map<String, SubjectEntity> subjects,
  }) = _YearEntity;

  @override
  factory YearEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$YearEntityFromJson, json);
}
