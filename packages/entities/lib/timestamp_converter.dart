import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class TimestampConverter implements JsonConverter<DateTime?, dynamic> {
  const TimestampConverter();

  @override
  DateTime? fromJson(dynamic data) {
    return switch (data) {
      null => null,
      String s => DateTime.parse(s),
      Timestamp t => t.toDate(),
      _ => () {
          throw ArgumentError('Unsupported type for TimestampConverter: ${data.runtimeType}');
        }()
    };
  }

  @override
  Timestamp? toJson(DateTime? dateTime) {
    if (dateTime == null) {
      return null;
    }
    final timestamp = Timestamp.fromDate(dateTime);
    return timestamp; // .toString();
  }
}

class TimestampConverterNN implements JsonConverter<DateTime, dynamic> {
  const TimestampConverterNN();

  @override
  DateTime fromJson(dynamic data) {
    return switch (data) {
      String s => DateTime.parse(s),
      Timestamp t => t.toDate(),
      _ => () {
          throw ArgumentError('Unsupported type for TimestampConverterNN: ${data.runtimeType}');
        }()
    };
  }

  @override
  Timestamp toJson(DateTime dateTime) {
    final timestamp = Timestamp.fromDate(dateTime);
    return timestamp;
  }
}

class TimestampMapConverter implements JsonConverter<Map<String, DateTime>?, dynamic> {
  const TimestampMapConverter();

  @override
  Map<String, DateTime>? fromJson(dynamic data) {
    return switch (data) {
      null => null,
      Map<String, dynamic> map => map.map(
          (key, value) => MapEntry(
            key,
            switch (value) {
              String s => DateTime.parse(s),
              Timestamp t => t.toDate(),
              _ => () {
                  throw ArgumentError('Unsupported type for TimestampMapConverter: ${value.runtimeType}');
                }()
            },
          ),
        ),
      _ => () {
          throw ArgumentError('Expected Map<String, dynamic> but got ${data.runtimeType}');
        }()
    };
  }

  @override
  Map<String, dynamic>? toJson(Map<String, DateTime>? dateTimeMap) {
    if (dateTimeMap == null) {
      return null;
    }
    return dateTimeMap.map(
      (key, dateTime) => MapEntry(key, Timestamp.fromDate(dateTime)),
    );
  }
}

extension TimestampJsonConverter on Timestamp {
  String toJson() => toDate().toIso8601String();
}

String jsonEncodeWithTS(Object? json) {
  try {
    return jsonEncode(json, toEncodable: (object) {
      return switch (object) {
        Timestamp timestamp => timestamp.toJson(),
        _ => object,
      };
    });
  } catch (error) {
    throw ArgumentError('jsonEncodeWithTS failed with $error');
  }
}

/// A JsonConverter for handling sets in Firebase.
///
/// Firebase doesn't natively support sets, so this converter transforms
/// between Dart's Set and List which is storable in Firebase.
class SetConverter<T> implements JsonConverter<Set<T>?, List<T>?> {
  const SetConverter();

  @override
  Set<T>? fromJson(List<dynamic>? json) {
    return switch (json) {
      null => null,
      [] => <T>{}, // Empty list becomes empty set
      _ => json.map((item) => item as T).toSet(),
    };
  }

  @override
  List<T>? toJson(Set<T>? dartSet) {
    return dartSet?.toList();
  }
}
