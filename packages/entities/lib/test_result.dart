import 'package:freezed_annotation/freezed_annotation.dart';

import 'timestamp_converter.dart';

part 'test_result.freezed.dart';
part 'test_result.g.dart';

/// Model representing the results of a completed test
@Freezed(toJson: true)
class TestResult with _$TestResult {

  const TestResult._();

  factory TestResult({

    required String subscriptionId,
    /// id of the the test that was attempted
    required String testId,
    /// name of the the test that was attempted
    required String testName,
    /// Unique identifier for the test attempt
    required String id,
    /// Whether the test was free or paid
    required bool free,

    /// Timestamp when the test result was created
    @TimestampConverter() required DateTime timestamp,

    /// Score percentage (0-100)
    required double scorePercentage,

    /// Number of correct answers
    required int correctAnswers,

    /// Total number of questions in the test
    required int questionCount,

    /// Number of questions that were answered
    required int answeredCount,

    /// Time taken to complete the test
    required Duration timeTaken,

  }) = _TestResult;

/*
  /// Create a TestResult with default values
  factory TestResult.empty() => TestResult(
    id: Uuid().v4(),
    testId: '',
    testName: '',
    timestamp: DateTime.now(),
    scorePercentage: 0.0,
    correctAnswers: 0,
    questionCount: 0,
    answeredCount: 0,
    timeTaken: Duration.zero,
  );
*/

  factory TestResult.fromJson(Map<String, dynamic> json) => _$TestResultFromJson(json);

  String getResultMessage() {
    if (scorePercentage >= 90) {
      return 'Excellent!';
    } else if (scorePercentage >= 70) {
      return 'Good Job!';
    } else if (scorePercentage >= 50) {
      return 'Keep Practicing';
    } else {
      return 'Needs Improvement';
    }
  }

}
