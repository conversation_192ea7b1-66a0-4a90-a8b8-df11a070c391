// packages/entities/lib/user_entity.dart

import 'package:freezed_annotation/freezed_annotation.dart';

import 'entity.dart';
import 'test_result.dart';
import 'timestamp_converter.dart';

part 'user_entity.freezed.dart';

part 'user_entity.g.dart';

@Freezed(toJson: true)
class UserEntity extends Entity with _$UserEntity {
  factory UserEntity({
    required String displayName,
    @TimestampConverter() DateTime? signedUp,
    String? email,
    List<String>? provider,
    required String uid,
    String? courseId,
    String? yearId,
    List<BookmarkEntity>? bookmarks,
    List<TestResult>? results,
    @TimestampMapConverter() Map<String, DateTime>? subscriptions,
    @Default({}) Map<String, Map<String, dynamic>>? transactions,
    bool? acceptedTerms,
    bool? acceptedPrivacyPolicy,
    @TimestampConverter() DateTime? acceptedTermsAt,
    @TimestampConverter() DateTime? acceptedPrivacyPolicyAt,
  }) = _UserEntity;

  @override
  factory UserEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$UserEntityFromJson, json);
}

@Freezed(toJson: true)
class BookmarkEntity extends Entity with _$BookmarkEntity {
  factory BookmarkEntity({
    required String publishedTestId,
    required String courseId,
    required String subjectId,
    required String subscriptionId,
    required int version,
    required int questionIndex,
  }) = _BookmarkEntity;

  @override
  factory BookmarkEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$BookmarkEntityFromJson, json);
}
